<?php

namespace Tests\Feature;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Gate;
use Tests\TestCase;

class GatesTest extends TestCase
{
    use RefreshDatabase;


    /*********************************************************************
     * TEST SUPER ADMIN GATES
     *********************************************************************
     *
     * Verifies that <PERSON><PERSON><PERSON><PERSON> has access to all gates in the system.
     * Tests both role-specific and resource-specific permissions.
     *
     * @return void
     *
     *********************************************************************/
    public function test_super_admin_gates(): void
    {
        // -----------------------
        // Create SuperAdmin User
        $superAdmin = User::factory()->create(['role' => Role::SuperAdmin->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertTrue(Gate::forUser($superAdmin)->allows('super-admin'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('admin'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('sales'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('finance'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertTrue(Gate::forUser($superAdmin)->allows('manage-users'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('manage-payments'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('manage-orders'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('manage-websites'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('manage-content'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('view-analytics'));
        $this->assertTrue(Gate::forUser($superAdmin)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST ADMIN GATES
     *********************************************************************
     *
     * Verifies that Admin has access to all gates except super-admin.
     * Tests both role-specific and resource-specific permissions.
     *
     * @return void
     *
     *********************************************************************/
    public function test_admin_gates(): void
    {
        // -----------------------
        // Create Admin User
        $admin = User::factory()->create(['role' => Role::Admin->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($admin)->allows('super-admin'));
        $this->assertTrue(Gate::forUser($admin)->allows('admin'));
        $this->assertTrue(Gate::forUser($admin)->allows('sales'));
        $this->assertTrue(Gate::forUser($admin)->allows('finance'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertTrue(Gate::forUser($admin)->allows('manage-users'));
        $this->assertTrue(Gate::forUser($admin)->allows('manage-payments'));
        $this->assertTrue(Gate::forUser($admin)->allows('manage-orders'));
        $this->assertTrue(Gate::forUser($admin)->allows('manage-websites'));
        $this->assertTrue(Gate::forUser($admin)->allows('manage-content'));
        $this->assertTrue(Gate::forUser($admin)->allows('view-analytics'));
        $this->assertTrue(Gate::forUser($admin)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST SALES GATES
     *********************************************************************
     *
     * Verifies that Sales role has access to:
     * - Sales-specific gates
     * - Order management
     * - Analytics viewing
     *
     * @return void
     *
     *********************************************************************/
    public function test_sales_gates(): void
    {
        // -----------------------
        // Create Sales User
        $sales = User::factory()->create(['role' => Role::Sales->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($sales)->allows('super-admin'));
        $this->assertFalse(Gate::forUser($sales)->allows('admin'));
        $this->assertTrue(Gate::forUser($sales)->allows('sales'));
        $this->assertFalse(Gate::forUser($sales)->allows('finance'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertFalse(Gate::forUser($sales)->allows('manage-users'));
        $this->assertFalse(Gate::forUser($sales)->allows('manage-payments'));
        $this->assertTrue(Gate::forUser($sales)->allows('manage-orders'));
        $this->assertFalse(Gate::forUser($sales)->allows('manage-websites'));
        $this->assertFalse(Gate::forUser($sales)->allows('manage-content'));
        $this->assertTrue(Gate::forUser($sales)->allows('view-analytics'));
        $this->assertFalse(Gate::forUser($sales)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST FINANCE GATES
     *********************************************************************
     *
     * Verifies that Finance role has access to:
     * - Finance-specific gates
     * - Payment management
     * - Order management
     * - Analytics viewing
     *
     * @return void
     *
     *********************************************************************/
    public function test_finance_gates(): void
    {
        // -----------------------
        // Create Finance User
        $finance = User::factory()->create(['role' => Role::Finance->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($finance)->allows('super-admin'));
        $this->assertFalse(Gate::forUser($finance)->allows('admin'));
        $this->assertFalse(Gate::forUser($finance)->allows('sales'));
        $this->assertTrue(Gate::forUser($finance)->allows('finance'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertFalse(Gate::forUser($finance)->allows('manage-users'));
        $this->assertTrue(Gate::forUser($finance)->allows('manage-payments'));
        $this->assertTrue(Gate::forUser($finance)->allows('manage-orders'));
        $this->assertFalse(Gate::forUser($finance)->allows('manage-websites'));
        $this->assertFalse(Gate::forUser($finance)->allows('manage-content'));
        $this->assertTrue(Gate::forUser($finance)->allows('view-analytics'));
        $this->assertFalse(Gate::forUser($finance)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST PUBLISHER GATES
     *********************************************************************
     *
     * Verifies that Publisher role has access to:
     * - Publisher-specific gates
     * - Website management
     * - Content management
     * - Analytics viewing
     *
     * @return void
     *
     *********************************************************************/
    public function test_publisher_gates(): void
    {
        // -----------------------
        // Create Publisher User
        $publisher = User::factory()->create(['role' => Role::Publisher->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($publisher)->allows('super-admin'));
        $this->assertFalse(Gate::forUser($publisher)->allows('admin'));
        $this->assertFalse(Gate::forUser($publisher)->allows('sales'));
        $this->assertFalse(Gate::forUser($publisher)->allows('finance'));
        $this->assertTrue(Gate::forUser($publisher)->allows('publisher'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertFalse(Gate::forUser($publisher)->allows('manage-users'));
        $this->assertFalse(Gate::forUser($publisher)->allows('manage-payments'));
        $this->assertFalse(Gate::forUser($publisher)->allows('manage-orders'));
        $this->assertTrue(Gate::forUser($publisher)->allows('manage-websites'));
        $this->assertTrue(Gate::forUser($publisher)->allows('manage-content'));
        $this->assertTrue(Gate::forUser($publisher)->allows('view-analytics'));
        $this->assertFalse(Gate::forUser($publisher)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST ADVERTISER GATES
     *********************************************************************
     *
     * Verifies that Advertiser role has access to:
     * - Advertiser-specific gates only
     * No access to management or analytics features
     *
     * @return void
     *
     *********************************************************************/
    public function test_advertiser_gates(): void
    {
        // -----------------------
        // Create Advertiser User
        $advertiser = User::factory()->create(['role' => Role::Advertiser->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($advertiser)->allows('super-admin'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('admin'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('sales'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('finance'));
        $this->assertTrue(Gate::forUser($advertiser)->allows('advertiser'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertFalse(Gate::forUser($advertiser)->allows('manage-users'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('manage-payments'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('manage-orders'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('manage-websites'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('manage-content'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('view-analytics'));
        $this->assertFalse(Gate::forUser($advertiser)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST OUTREACH GATES
     *********************************************************************
     *
     * Verifies that Outreach role has access to:
     * - Outreach-specific gates
     * - Website management
     * No access to other management features
     *
     * @return void
     *
     *********************************************************************/
    public function test_outreach_gates(): void
    {
        // -----------------------
        // Create Outreach User
        $outreach = User::factory()->create(['role' => Role::Outreach->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($outreach)->allows('super-admin'));
        $this->assertFalse(Gate::forUser($outreach)->allows('admin'));
        $this->assertFalse(Gate::forUser($outreach)->allows('sales'));
        $this->assertFalse(Gate::forUser($outreach)->allows('finance'));
        $this->assertTrue(Gate::forUser($outreach)->allows('outreach'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertFalse(Gate::forUser($outreach)->allows('manage-users'));
        $this->assertFalse(Gate::forUser($outreach)->allows('manage-payments'));
        $this->assertFalse(Gate::forUser($outreach)->allows('manage-orders'));
        $this->assertTrue(Gate::forUser($outreach)->allows('manage-websites'));
        $this->assertFalse(Gate::forUser($outreach)->allows('manage-content'));
        $this->assertFalse(Gate::forUser($outreach)->allows('view-analytics'));
        $this->assertFalse(Gate::forUser($outreach)->allows('manage-settings'));
    }


    /*********************************************************************
     * TEST WRITER GATES
     *********************************************************************
     *
     * Verifies that Writer role has access to:
     * - Writer-specific gates
     * - Content management
     * No access to other management features
     *
     * @return void
     *
     *********************************************************************/
    public function test_writer_gates(): void
    {
        // -----------------------
        // Create Writer User
        $writer = User::factory()->create(['role' => Role::Writer->value]);

        // -----------------------
        // Test Role-Specific Gates
        $this->assertFalse(Gate::forUser($writer)->allows('super-admin'));
        $this->assertFalse(Gate::forUser($writer)->allows('admin'));
        $this->assertFalse(Gate::forUser($writer)->allows('sales'));
        $this->assertFalse(Gate::forUser($writer)->allows('finance'));
        $this->assertTrue(Gate::forUser($writer)->allows('writer'));

        // -----------------------
        // Test Resource-Specific Gates
        $this->assertFalse(Gate::forUser($writer)->allows('manage-users'));
        $this->assertFalse(Gate::forUser($writer)->allows('manage-payments'));
        $this->assertFalse(Gate::forUser($writer)->allows('manage-orders'));
        $this->assertFalse(Gate::forUser($writer)->allows('manage-websites'));
        $this->assertTrue(Gate::forUser($writer)->allows('manage-content'));
        $this->assertFalse(Gate::forUser($writer)->allows('view-analytics'));
        $this->assertFalse(Gate::forUser($writer)->allows('manage-settings'));
    }
}
