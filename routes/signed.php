<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Publisher\OrderController;
use App\Http\Controllers\Publisher\PublisherWebsiteController;
use App\Livewire\Marketplace\Order\Items\OrderSingleItemDetails;


/**********************************************************************************************************************/
// -----------------------------------------------  SIGNED URLS ROUTES -----------------------------------------------//
/**********************************************************************************************************************/
// Get Publisher Order Details
Route::get('/publisher/signed/order/details/{id}', [OrderController::class, 'orderDetails'])
    ->name('publisher.orders.details.signed');

// Get Advertiser Order Details
Route::get('/advertiser/signed/order/details/{id}', OrderSingleItemDetails::class)
    ->name('advertiser.orders.details.signed');

// Get Publisher Website Details
Route::get('/publisher/signed/websites/edit/{id}', [PublisherWebsiteController::class, 'signedEditWebsite'])
    ->name('publisher.websites.edit.signed');

// Publisher Website Update Form
Route::put('/publisher/signed/websites/update', [PublisherWebsiteController::class, 'signedUpdateWebsite'])
    ->name('publisher.websites.update.signed');
