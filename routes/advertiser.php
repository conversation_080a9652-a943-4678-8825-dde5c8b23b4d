<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Marketplace\OrderSingleDetails;
use App\Http\Controllers\MarketplaceOrderController;
use App\Http\Controllers\Advertiser\AdvertiserController;
use App\Livewire\Marketplace\Order\Items\OrderSingleItemDetails;
use App\Livewire\Advertiser\{Dashboard, Guidelines, Wallet, WalletWithdraw, WalletDetails};


/*******************************************************************************************************/
//-----------------------------------------  ADVERTISER ROUTES ----------------------------------------//
/*******************************************************************************************************/


//--------------------------------------------------------------------------------//
//                                        GENERAL                                 //
//--------------------------------------------------------------------------------//
Route::get('/dashboard', Dashboard::class)->name('dashboard');
Route::get('/guidelines', Guidelines::class)->name('guidelines'); //advertisre guidelines



//--------------------------------------------------------------------------------//
//                                        WALLET                                  //
//--------------------------------------------------------------------------------//
Route::get('/wallet', Wallet::class)->name('wallet.index');
Route::get('/wallet/withdraw', WalletWithdraw::class)->name('wallet.withdraw');
Route::get('/wallet/details/{id}', WalletDetails::class)->name('wallet.details');
Route::get('/transactions/details/{id}', [AdvertiserController::class, 'transactionsDetails'])
    ->name('transactions.details');


//--------------------------------------------------------------------------------//
//                                         ORDER                                  //
//--------------------------------------------------------------------------------//
Route::get('/orders', [MarketplaceOrderController::class, 'ordersList'])->name('my-orders');
Route::get('/order-detail/{id}', OrderSingleDetails::class)->name('order-details');
Route::get('/order-item-detail/{id}', OrderSingleItemDetails::class)->name('order-item-details');
Route::get('/order-invoice', [MarketplaceOrderController::class, 'showInvoice'])->name('show-invoice');
