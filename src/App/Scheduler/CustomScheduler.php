<?php

declare(strict_types=1);

namespace Pressbear\Scheduler;

use Illuminate\Console\Scheduling\Schedule;

class CustomScheduler
{
    /*******************************************************************
     * CUSTOM SCHEDULER
     *********************************************************************
     *
     * This class is used to schedule the custom commands.
     *
     * @var Schedule
     */

    public function __construct(private Schedule $schedule) {}

    /*******************************************************************
     * SCHEDULE THE COMMANDS
     *********************************************************************
     *
     * This method is used to schedule the commands.
     *
     * @return void
     */
    public function __invoke(): void
    {
        // SimilarWeb
        $this->schedule->command('seo:fetch --source=similarweb --type=domain')
            ->everyFiveMinutes()
            ->between('1:00', '2:00')
            ->withoutOverlapping();

        // Ahrefs
        $this->schedule->command('seo:fetch --source=ahref --type=domain')
            ->everyFiveMinutes()
            ->between('2:00', '3:00')
            ->withoutOverlapping();
    }
}
