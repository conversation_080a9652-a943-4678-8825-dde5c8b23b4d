<?php

namespace Domain\Activity\List;

use App\Models\MarketplaceSingleOrderItem;
use Spatie\Activitylog\Models\Activity;
use Carbon\Carbon;

class GetOrderItemActivityLogs
{
    /*********************************************************************
     * ORDER ITEM ACTIVITY LOGS
     **********************************************************************
     * Retrieves logs for an order item.
     *
     * @param int $orderItemId
     * @return array
     *********************************************************************/
    public function __invoke(int $orderItemId): array
    {
        // -----------------------
        // Load item
        $item = MarketplaceSingleOrderItem::with(['content', 'requirements'])
            ->findOrFail($orderItemId);

        // -----------------------
        // Prepare subjects
        $subjects = collect([
            $item,
            $item->content,
            $item->requirements,
        ])->filter()->merge($item->publication);

        // -----------------------
        // Fetch logs
        $logs = Activity::with('causer')
            ->where(function ($query) use ($subjects) {
                foreach ($subjects as $subject) {
                    $query->orWhere(fn ($q) => $q->forSubject($subject));
                }
            })
            ->latest()
            ->paginate(10)
            ->through(fn (Activity $log) => $this->formatLog($log));

        return ['props' => ['logs' => $logs]];
    }




    /*********************************************************************
     * FORMAT LOG ENTRY
     **********************************************************************
     * Formats a log record.
     *
     * @param Activity $log
     * @return array
     *********************************************************************/
    protected function formatLog(Activity $log): array
    {
        // Extract properties
        $props = $log->properties->toArray();

        // Format dates
        foreach (['attributes', 'old'] as $section) {
            if (! is_array($props[$section] ?? null)) {
                continue;
            }
            foreach ($props[$section] as $key => $val) {
                if ($val && str_ends_with($key, '_at')) {
                    try {
                        $props[$section][$key] = Carbon::parse($val)->format('d M Y');
                    } catch (\Exception $e) {
                        // invalid date
                    }
                }
            }
        }

        return [
            'id'           => $log->id,
            'description'  => $log->description,
            'subject_type' => class_basename($log->subject_type),
            'user'         => $log->causer?->name ?? 'System',
            'changes'      => $props,
            'created_at'   => $log->created_at->format('d M Y, h:i A'),
        ];
    }

}
