<?php

namespace Domain\Wallet;

use Domain\Wallet\Interface\WalletInterface;
use Domain\Wallet\Actions\{CreditWallet, TransferCredit};
use App\Models\User;

class Wallet implements WalletInterface
{
    /*************************************************
     * CREDIT
     *************************************************
     *
     * Credits a user's wallet with the specified amount.
     *
     * @param User $user
     * @param float $amount
     * @param array $data   
    /************************************************/
    public function credit(User $user, float $amount, array $data): bool
    {
        try {
            $amount = (int) round($amount * 100);

            (new CreditWallet())($user, [
                'amount' => $amount,
                'reference' => $data['reference'] ?? 'Wallet Credit',
                'type' => $data['type'] ?? 'wallet_credit',
                'message' => $data['message'] ?? 'Wallet credited',
            ]);

            return true;
        } catch (\Exception $e) {

            return false;
        }
    }


    /*************************************************
     * TRANSFER
     *************************************************
     *
     * Transfers a credit from one user's wallet to another.
     *
     * @param User $fromUser
     * @param User $toUser
     * @param float $amount 
    /************************************************/
    public function transfer(User $fromUser, User $toUser, float $amount, array $data): bool
    {
        try {
            $amount = (int) round($amount * 100);

            (new TransferCredit())($fromUser, $toUser, [
                'amount' => $amount,
                'reference' => $data['reference'] ?? 'Wallet Transfer',
                'type' => $data['type'] ?? 'wallet_transfer',
                'message' => $data['message'] ?? 'Wallet transferred',
            ]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
