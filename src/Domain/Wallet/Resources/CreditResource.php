<?php

declare(strict_types=1);

namespace Domain\Wallet\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Bavix\Wallet\Models\Transaction;

/**
 * @mixin Transaction
 * Resource for formatting credit transactions
 * 
 * @property Transaction $resource
 * @property-read \App\Models\User $holder
 * @property-read int $id
 * @property-read \Bavix\Wallet\Models\Wallet|null $wallet
 */
class CreditResource extends JsonResource
{
    /*************************************************
     * toArray
    /************************************************/
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'user_name' => $this->wallet?->holder?->name ?? 'Unknown User',
            'amount' => number_format($this->amount / 100, 2),
            'reason' => $this->meta['message'] ?? 'Admin credit',
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
