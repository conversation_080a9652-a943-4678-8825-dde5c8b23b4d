<?php

namespace Domain\Wallet\Interface;

use App\Models\User;

interface WalletInterface
{
    /*************************************************
     * CREDIT
    /************************************************/
    public function credit(User $user, float $amount, array $data): bool;

    /*************************************************
     * TRANSFER
    /************************************************/
    public function transfer(User $fromUser, User $toUser, float $amount, array $data): bool;
}
