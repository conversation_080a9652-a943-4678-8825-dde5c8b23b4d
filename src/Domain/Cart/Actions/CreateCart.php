<?php

namespace Domain\Cart\Actions;

use App\Models\MarketplaceCartItem;
use App\Models\MarketplaceWebsite;
use App\Models\User;
use Domain\Cart\DataTransferObjects\CartData;

class CreateCart
{


    /*************************************************
     * Handle
    /************************************************/
    public function __invoke(CartData $payload, User $user): bool
    {
        try {
            // -----------------------
            // Get Website
            $websiteID      = $payload->website;
            $niche          = $payload->niche ?? 'general';

            // -----------------------
            // Check if Website Accepts Niche
            $acceptsNiche = MarketplaceWebsite::find($websiteID)->acceptsNiche($niche);

            // -----------------------
            // Create Cart Item
            if ($acceptsNiche) {
                MarketplaceCartItem::firstOrCreate([
                    'user_id'                => $user->id,
                    'marketplace_website_id' => $websiteID,
                    'niche'                  => $niche
                ]);

                return true;
            }

            return false;
        } catch (\Throwable $th) {
            return false;
        }
    }
}
