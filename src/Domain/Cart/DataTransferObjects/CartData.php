<?php

declare(strict_types=1);

namespace Domain\Cart\DataTransferObjects;

use <PERSON><PERSON>\LaravelData\Data;
use App\Rules\ValidNiche;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Rule;

/*********************************************************************
 * CART DATA TRANSFER OBJECT
 *********************************************************************
 *
 * Represents the data structure for adding items to the shopping cart.
 * This DTO encapsulates the required data for cart operations including
 * website selection and niche specification.
 *
 * Properties:
 * - website: The ID of the marketplace website to add to cart
 * - niche: The content niche/category for the cart item
 *
 * Validation:
 * - Website ID must exist in marketplace_websites table
 * - Niche must be valid according to ValidNiche rule
 *
 *********************************************************************/
class CartData extends Data
{
    public function __construct(
        #[Rule('integer|exists:marketplace_websites,id')]
        public int $website,
        #[Rule(new ValidNiche)]
        public string $niche,
    ) {}
}
