<?php

declare(strict_types=1);

namespace Domain\Cart\DataTransferObjects;

use <PERSON><PERSON>\LaravelData\Data;
use App\Rules\ValidNiche;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\Rule;

/*********************************************************************
 * UPDATE CART DATA TRANSFER OBJECT
 *********************************************************************
 *
 * Represents the data structure for updating shopping cart items.
 * This DTO encapsulates the required data for cart modification
 * operations including niche updates, content writing changes,
 * and item deletion.
 *
 * Properties:
 * - cartWebsiteId: The ID of the website in the cart to update
 * - niche: The new niche/category for the cart item (optional)
 * - contentWriting: Whether content writing is enabled (optional)
 * - task: The specific operation to perform (updateNiche, deleteCartItem, updateContentWriting)
 *
 * Validation:
 * - Website ID must exist in marketplace_websites table
 * - Niche must be valid according to ValidNiche rule (when provided)
 * - Task must be one of the allowed operations
 *
 *********************************************************************/
class UpdateCartData extends Data
{
    public function __construct(
        #[Rule('integer|exists:marketplace_websites,id')]
        public int $cartWebsiteId,
        #[Rule(new ValidNiche)]
        public ?string $niche,
        public ?bool $contentWriting,
        #[Rule('in:updateNiche,deleteCartItem,updateContentWriting')]
        public string $task,
    ) {}
}
