<?php

namespace Domain\User;

use App\Models\User as ModelsUser;
use Illuminate\Pagination\LengthAwarePaginator;
use Domain\User\Interface\UserInterface;
use Domain\User\Services\UserActivityService;
use Domain\User\Traits\UserActionTrait;

class User implements UserInterface
{
    use UserActionTrait;

    /*********************************************************************
     * GET USER ACTIVITY LOGS - Formatted activity history for admin view
     *********************************************************************
     *
     * Retrieves and formats user activity logs:
     * - Includes causer relationship data
     * - Formats dates for display
     * - Processes timestamp properties in log data
     * - Handles pagination for large activity sets
     *
     * @param ModelsUser $user - User to get activity logs for
     * @param int $perPage - Number of logs per page
     * @return LengthAwarePaginator - Paginated activity log collection
     *
     *********************************************************************/
    public function getUserActivityLogs(ModelsUser $user, int $perPage = 10): LengthAwarePaginator
    {
        return (new UserActivityService())->getUserActivityLogs($user, $perPage);
    }
}
