<?php

namespace Domain\User\Actions;

use App\Models\User;
use Domain\User\DataTransferObjects\UserData;

class UpdateUser
{
    /*********************************************************************
     * UPDATE USER - Existing user modification with data processing
     *********************************************************************
     *
     * Updates existing user with proper data processing:
     * - Hashes password securely if provided
     * - Processes address data into JSON format
     * - Handles email verification status changes
     * - Returns updated user instance
     *
     * @param User $user - User model to update
     * @param UserData $payload - Updated user data including password and address info
     * @return User - Updated user model instance
     *
     *********************************************************************/
    public function __invoke(User $user, UserData $payload): User
    {

        //-----------------------
        // Handle Password & Other Data Update
        if ($payload->password === null) {
            unset($payload->password);
        }
        $user->email_verified_at = $payload->email_verified ? now() : null;

        //-----------------------
        // Process Address and Email Data
        $payload = processAddressData($payload->toArray());

        $user->update($payload);
        return $user;
    }
}
