<?php

namespace Domain\User\Actions;

use App\Models\User;
use Domain\User\DataTransferObjects\UserData;

class CreateUser
{
    /*********************************************************************
     * CREATE USER - Create a new user with address processing
     *********************************************************************
     *
     * Creates a new user with address processing:
     * - Hashes password securely
     * - Processes address data into JSON format
     *
     * @param UserData $payload - User data
     * @return User - Created user model instance
     *
     *********************************************************************/
    public function __invoke(UserData $payload): User
    {

        //-----------------------
        // Process Address Data
        $userData = processAddressData($payload->toArray());

        return User::create($userData);
    }
}
