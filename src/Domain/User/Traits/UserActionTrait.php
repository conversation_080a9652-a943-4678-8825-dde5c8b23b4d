<?php

namespace Domain\User\Traits;

use App\Models\User;
use Domain\User\Actions\CreateUser;
use Domain\User\Actions\UpdateUser;
use Domain\User\DataTransferObjects\UserData;

trait UserActionTrait
{


    /*********************************************************************
     * CREATE USER - New user creation with data processing
     *********************************************************************
     *
     * Creates a new user with proper data processing:
     * - Hashes password securely
     * - Processes address data into JSON format
     * - Returns created user instance
     *
     * @param AdminUserRequest $request - Validated user request data
     * @return User - Created user model instance
     *
     *********************************************************************/
    public function createUser(UserData $payload): User
    {
        return (new CreateUser())($payload);
    }





    /*********************************************************************
     * UPDATE USER - Existing user modification with validation
     *********************************************************************
     *
     * Updates existing user with conditional processing:
     * - Only hashes password if new one provided
     * - Processes address data and email verification
     * - Handles email verification status changes
     *
     * @param User $user - User model to update
     * @param AdminUserRequest $request - Validated user request data
     * @return User - Updated user model instance
     *
     *********************************************************************/
    public function updateUser(User $user, UserData $payload): User
    {
        return (new UpdateUser())($user, $payload);
    }





    /*********************************************************************
     * SOFT DELETE USER - Soft delete user with data preservation
     *********************************************************************
     *
     * Performs soft delete operation on user:
     * - Preserves user data in database
     * - Sets deleted_at timestamp
     * - Allows for potential restoration
     *
     * @param User $user - User model to soft delete
     * @return bool - Success status of delete operation
     *
     *********************************************************************/
    public function softDeleteUser(User $user): bool
    {
        return $user->delete();
    }





    /*********************************************************************
     * RESTORE USER - Restore soft-deleted user
     *********************************************************************
     *
     * Restores previously soft-deleted user:
     * - Removes deleted_at timestamp
     * - Reactivates user account
     * - Restores all user relationships
     *
     * @param User $user - Soft-deleted user to restore
     * @return bool - Success status of restore operation
     *
     *********************************************************************/
    public function restoreUser(User $user): bool
    {
        return $user->restore();
    }





    /*********************************************************************
     * FORCE DELETE USER - Permanent user deletion
     *********************************************************************
     *
     * Permanently removes user from database:
     * - Irreversible operation
     * - Removes all associated data
     * - Cannot be restored
     *
     * @param User $user - User model to permanently delete
     * @return bool - Success status of delete operation
     *
     *********************************************************************/
    public function forceDeleteUser(User $user): bool
    {
        return $user->forceDelete();
    }
}
