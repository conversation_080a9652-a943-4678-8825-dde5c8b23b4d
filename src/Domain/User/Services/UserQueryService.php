<?php

namespace Domain\User\Services;

use App\Enums\Role;
use App\Models\User;
use App\Enums\OrderItemStates;
use App\Models\MarketplaceOrder;
use App\Enums\MarketplaceOrderStatus;
use App\Models\MarketplaceSingleOrderItem;
use App\Http\Resources\UserTransitionResource;

class UserQueryService
{
    /*********************************************************************
     * GET USER STATS FOR ADMIN
     *********************************************************************
     *
     * Retrieves statistics for admin page:
     * - Financial transaction totals: (deposits, withdrawals, pending)
     * - Role-specific order statistics: (advertiser/publisher)
     * - Order status breakdowns for publishers
     *
     * @param User $user - User model to get statistics for
     * @return array - Statistics data organized by category
     *
     *********************************************************************/
    public function getUserStats(User $user): array
    {
        // -----------------------
        // Financial Statistics
        $transactionStats = $user->transactions()->query();
        $stats = [
            'total_deposits' => $transactionStats->clone()->where('type', 'deposit')->where('confirmed', true)
                ->sum('amount') / 100,

            'total_withdrawals' => $transactionStats->clone()->where('type', 'withdraw')->where('confirmed', true)
                ->sum('amount') / 100,

            'pending_transactions' => $transactionStats->clone()->where('confirmed', false)->count(),
        ];


        // -----------------------
        // Advertiser Order Statistics
        $advertiserOrderStats = [];
        if ($user->role === Role::Advertiser->value) {
            $advertiserOrderStats = [
                'total' => MarketplaceOrder::where('user_id', $user->id)->count(),
                'pending' => MarketplaceOrder::where('user_id', $user->id)->where('status', MarketplaceOrderStatus::PENDING->value)->count(),
                'cancelled' => MarketplaceOrder::where('user_id', $user->id)->where('status', MarketplaceOrderStatus::CANCELLED->value)->count(),
            ];
        }


        // -----------------------
        // Publisher Order Statistics
        $publisherOrderStats = $user->role === Role::Publisher->value
            ? $this->getPublisherOrderStats($user)
            : [];

        return [
            'stats' => $stats,
            'advertiser_order_stats' => $advertiserOrderStats,
            'publisher_order_stats' => $publisherOrderStats,
        ];
    }





    /*********************************************************************
     * GET PUBLISHER ORDER STATS - Publisher-specific order statistics
     *********************************************************************
     *
     * Calculates order statistics for publisher users:
     * - Total order items across all publisher websites
     * - Breakdown by order item state (pending, completed, etc.)
     * - Uses OrderItemStates enum for comprehensive coverage
     *
     * @param User $user - Publisher user to get statistics for
     * @return array - Order statistics by state
     *
     *********************************************************************/
    private function getPublisherOrderStats(User $user): array
    {
        $baseQuery = MarketplaceSingleOrderItem::whereHas('website', function ($query) use ($user) {
            $query->where('publisher_user_id', $user->id);
        });

        $stats = [
            'total' => $baseQuery->count(),
        ];

        foreach (OrderItemStates::cases() as $state) {
            $stats[$state->value] = (clone $baseQuery)->where('state', $state->value)->count();
        }

        return $stats;
    }





    /*********************************************************************
     * GET USER TRANSACTIONS - Recent transaction history for admin view
     *********************************************************************
     *
     * Retrieves formatted transaction history for user admin page:
     * - Includes wallet and holder relationship data
     * - Formats amounts and dates for display
     * - Separates debit and credit transactions
     * - Limits results for performance
     *
     * @param User $user - User to get transactions for
     * @param int $limit - Maximum number of transactions to return
     * @return \Illuminate\Support\Collection - Formatted transaction collection
     *
     *********************************************************************/
    public function getUserTransactions(User $user, int $limit = 10): \Illuminate\Support\Collection
    {
        return $user->transactions()
            ->with('wallet', 'wallet.holder')
            ->latest()
            ->limit($limit)
            ->get()
            ->map(fn($transaction) => new UserTransitionResource($transaction));
    }





    /*********************************************************************
     * GET USER WEBSITES - Recent websites for admin view
     *********************************************************************
     *
     * Retrieves recent websites associated with user:
     * - Limited to specified number for performance
     * - Ordered by creation date descending
     * - Returns essential website data only
     *
     * @param User $user - User to get websites for
     * @param int $limit - Maximum number of websites to return
     * @return \Illuminate\Support\Collection - Website collection
     *
     *********************************************************************/
    public function getUserWebsites(User $user, int $limit = 10): \Illuminate\Support\Collection
    {
        return $user->websites()
            ->latest()
            ->take($limit)
            ->orderBy('id', 'desc')
            ->get(['id', 'website_domain', 'active']);
    }
}
