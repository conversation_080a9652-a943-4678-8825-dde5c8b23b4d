<?php

namespace Domain\User\Services;

use App\Models\User;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;


class UserActivityService
{
    /*********************************************************************
     * GET USER ACTIVITY LOGS - Formatted activity history for admin view
     *********************************************************************
     *
     * Retrieves and formats user activity logs:
     * - Includes causer relationship data
     * - Formats dates for display
     * - Processes timestamp properties in log data
     * - Handles pagination for large activity sets
     *
     * @param User $user - User to get activity logs for
     * @param int $perPage - Number of logs per page
     * @return LengthAwarePaginator - Paginated activity log collection
     *
     *********************************************************************/
    public function getUserActivityLogs(User $user, int $perPage = 10): LengthAwarePaginator
    {
        $logs = Activity::with('causer')
            ->where('subject_type', User::class)
            ->where('subject_id', $user->id)
            ->latest()
            ->paginate($perPage);

        $logs->getCollection()->transform(function ($log) {
            $log->formatted_date = Carbon::parse($log->updated_at)->format('d M Y');

            // Format timestamps in properties
            $props = $log->properties->toArray();
            foreach (['attributes', 'old'] as $section) {
                if (!isset($props[$section])) continue;

                foreach ($props[$section] as $key => $val) {
                    if (str_ends_with($key, '_at') && $val) {
                        try {
                            $props[$section][$key] = Carbon::parse($val)->format('d M Y');
                        } catch (\Exception $e) {
                            // Keep original value if parsing fails
                        }
                    }
                }
            }

            $log->properties = collect($props);
            return $log;
        });

        return $logs;
    }
}
