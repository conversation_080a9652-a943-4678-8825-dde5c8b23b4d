<?php

declare(strict_types=1);

namespace Domain\User\DataTransferObjects;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON>tie\LaravelData\Attributes\Validation\Rule;
use App\Rules\ValidRole;

/*********************************************************************
 * USER DATA TRANSFER OBJECT
 *********************************************************************
 *
 * Represents the data structure for user creation and updates.
 * This DTO encapsulates all user-related data including personal
 * information, contact details, and address information.
 *
 * Properties:
 * - name: User's full name
 * - email: User's email address (validated)
 * - password: User's password (optional for updates)
 * - role: User's role in the system (validated)
 * - phone: User's phone number (optional)
 * - country_id: User's country ID (validated)
 * - company: User's company name (optional)
 * - address: User's address (optional)
 * - city: User's city (optional)
 * - postal_code: User's postal code (optional)
 * - email_verified: Email verification status (optional)
 *
 * Validation:
 * - Email must be valid format
 * - Role must be valid according to ValidRole rule
 * - Phone must be numeric
 * - Country ID must exist in countries_list table
 *
 *********************************************************************/
class UserData extends Data
{
    public function __construct(
        public string $name,
        #[Rule('email')]
        public string $email,
        public ?string $password,
        #[Rule(new ValidRole)]
        public string $role,
        #[Rule('numeric')]
        public ?string $phone,
        #[Rule('exists:countries_list,id')]
        public ?int $country_id,
        public ?string $company,
        public ?string $address,
        public ?string $city,
        public ?string $postal_code,
        public ?bool $email_verified = null,
    ) {}
}
