<?php

// Right now this  code is not per our standards, we need to refactor it to be more modular and testable.
namespace Domain\Withdrawal;

use App\Enums\RefundEnum;
use Illuminate\Support\Facades\DB;
use App\Models\WalletWithdrawalRequest;
use App\Notifications\WithdrawalRequestApproved;
use App\Http\Resources\Admin\WalletWithdrawalRequestResource;


class AdminWithdrawalService
{
    /**********************************************************************
     * GET WITHDRAWALS
     **********************************************************************
     *
     * Retrieves all withdrawal requests with related user data.
     * 
     * @return \Illuminate\Support\Collection
     * Collection of withdrawal requests with formatted data
     * 
     **********************************************************************/
    public function getWithdrawals()
    {
        $query = WalletWithdrawalRequest::with('user')->orderByDesc('id')->paginate(10);
        $withdrawals = WalletWithdrawalRequestResource::collection($query);
        return $withdrawals;
    }





    /**********************************************************************
     * GET WITHDRAWAL
     **********************************************************************
     *
     * Retrieves a specific withdrawal request with related data.
     * 
     **********************************************************************/
    public function getWithdrawal($id)
    {
        $withdrawal = WalletWithdrawalRequest::with(['user', 'paymentMethod', 'media'])
            ->findOrFail($id);

        $payments = !$withdrawal->paymentMethod
            ? $withdrawal->user->payments()->latest()->take(5)->get()
            : null;

        return [
            'withdrawal' => $withdrawal,
            'payments' => $payments,
        ];
    }





    /**********************************************************************
     * APPROVE WITHDRAWAL
     **********************************************************************
     *
     * Approves a withdrawal request and updates the withdrawal request status to "approved".
     * 
     **********************************************************************/
    public function approveWithdrawal($id, $data)
    {

        try {
            DB::beginTransaction();
            $withdrawal = WalletWithdrawalRequest::findOrFail($id);

            $withdrawal->update([
                'status'       => RefundEnum::Approved,
                'files_array'  => $data['media_files'] ?? [],
                'payment_url'  => $data['payment_url'] ?? null,
            ]);

            // -----------------------
            // Handle Media Attachments
            if (!empty($data['media_files'])) {
                $withdrawal->attachMedia($data['media_files'] ?? []);
            }

            // Refund the amount to the user's wallet
            $admin = super_admin_user();

            $admin->wallet->withdraw($withdrawal->amount, [
                'withdrawal_id' => $withdrawal->id,
                'reference' => 'Withdrawal Transfer To User Payment Method',
                'message' => 'Withdrawal request approved',
            ]);

            $withdrawal->user->notify(new WithdrawalRequestApproved($withdrawal));

            DB::commit();
            return redirect()->back()
                ->with('success', 'Withdrawal request approved successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Failed to approve withdrawal request.');
        }
    }
}
