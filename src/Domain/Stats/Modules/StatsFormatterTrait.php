<?php

namespace Domain\Stats\Modules;

// Formatting and processing utilities for stats
trait StatsFormatterTrait
{

    /*********************************************************************
     * FORMAT CURRENCY
     **********************************************************************
     *
     * Format currency values consistently
     *
     * @param float $amount
     * @return string
     *
     *********************************************************************/
    protected function formatCurrency(float $amount): string
    {
        return number_format($amount, 2);
    }




    /*********************************************************************
     * GENERATE CACHE KEY
     **********************************************************************
     *
     * Generate cache key for stats data
     *
     * @param string $prefix
     * @param array $filters
     * @return string
     *
     *********************************************************************/
    protected function generateCacheKey(string $prefix, array $filters): string
    {
        $filterHash = md5(serialize($filters));
        return "sales_stats_{$prefix}_{$filterHash}";
    }
}
