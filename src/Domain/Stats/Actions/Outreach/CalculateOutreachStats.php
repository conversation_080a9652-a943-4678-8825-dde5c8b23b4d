<?php

namespace Domain\Stats\Actions\Outreach;

use App\Models\Outreach;
use App\Traits\Filters\DateFilterTrait;

class CalculateOutreachStats
{
    use DateFilterTrait;

    /*********************************************************************
     * HANDLE
     **********************************************************************
     *
     * Calculate counts for each status and the total using optimized
     *
     * @param int $userId
     * @param array $filters
     * @return array
     *
     *******************************************************************
     */
    public function handle(?int $userId, array $filters): array
    {
        $baseQuery = Outreach::query();
        if ($userId) {
            $baseQuery->where('user_id', $userId);
        }

        // -----------------------
        // Apply Date Filters
        $filtered = $this->applyDateFilter(clone $baseQuery, $filters);

        // -----------------------
        // Get a list of all statuses
        // e.g. ['inprogress', 'onboarded', 'rejected', …]
        $statusList = $filtered->pluck('status');

        // -----------------------
        $stats = $statusList->countBy()->all();
        $stats['total'] = $statusList->count();

        return $stats;
    }
}
