<?php

namespace Domain\Stats\Actions\Sales;

use App\Enums\MarketplaceOrderStatus;
use App\Enums\Role;
use App\Models\MarketplaceOrder;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\User;
use App\Models\WalletWithdrawalRequest;
use App\Traits\Filters\DateFilterTrait;
use Bavix\Wallet\Models\Wallet;
use Domain\Stats\Modules\StatsFormatterTrait;
use Illuminate\Support\Facades\Cache;

/**
 * CALCULATE SALES STATISTICS
 *
 * Cached sales metrics for dashboard analytics.
 */
class CalculateSalesStats
{
    use StatsFormatterTrait, DateFilterTrait;




    /*********************************************************************
     * HANDLE SALES STATISTICS CALCULATION
     **********************************************************************
     *
     * Cached aggregation of sales metrics.
     *
     * @param array $filters - date range filters
     * @return array - formatted sales statistics
     *
     *********************************************************************/
    public function handle(array $filters): array
    {
        // -----------------------
        // Cache Key Generation
        $cacheKey = $this->generateCacheKey('time_period_stats', $filters);


        // -----------------------
        // Cached Statistics Calculation
        return Cache::remember($cacheKey, config('pressbear.fiveMinuteCache'), function () use ($filters) {
            $statusCounts       = $this->getStatusCounts($filters);
            $totalOrders        = array_sum($statusCounts);
            $totalSales         = $this->getTotalSales($filters);
            $publisherPayments  = $this->getPublisherPayments($filters);
            $withdrawals        = $this->getWithdrawals($filters);
            $outstanding        = $this->getOutstandingBalances();


            // -----------------------
            // Format And Merge Results
            return array_merge(
                ['total' => $totalOrders, 'sales' => $this->formatCurrency($totalSales)],
                $statusCounts,
                [
                    'publisher_payments'   => $this->formatCurrency($publisherPayments),
                    'profit'               => $this->formatCurrency($totalSales - $publisherPayments),
                    'withdrawals'          => $this->formatCurrency($withdrawals / 100), //convert from cents
                    'outstanding_payments' => $this->formatCurrency($outstanding / 100), //convert from cents
                ]
            );
        });
    }

    /*********************************************************************
     * GET ORDER STATUS COUNTS
     **********************************************************************
     *
     * Ensures all enum statuses are present in results.
     *
     * @param array $filters - date range filters
     * @return array - status counts with zero defaults
     *
     *********************************************************************/
    private function getStatusCounts(array $filters): array
    {
        // -----------------------
        // Get Status Counts
        $statuses = $this->applyDateFilter(MarketplaceOrder::query(), $filters)
            ->pluck('status')
            ->countBy();


        // -----------------------
        // Ensure All Enum Statuses Present
        return collect(MarketplaceOrderStatus::cases())
            ->mapWithKeys(fn($enum) => [
                $enum->value => $statuses->get($enum->value, 0),
            ])->all();
    }

    /*********************************************************************
     * GET TOTAL SALES REVENUE
     **********************************************************************
     *
     * Sum of marketplace order payments.
     *
     * @param array $filters - date range filters
     * @return float - total sales amount
     *
     *********************************************************************/
    private function getTotalSales(array $filters): float
    {
        return (float) $this
            ->applyDateFilter(MarketplaceOrder::query(), $filters)
            ->sum('price_paid');
    }

    /*********************************************************************
     * GET PUBLISHER PAYMENTS TOTAL
     **********************************************************************
     *
     * Sum of payments made to publishers for order items.
     *
     * @param array $filters - date range filters
     * @return float - total publisher payments
     *
     *********************************************************************/
    private function getPublisherPayments(array $filters): float
    {
        return (float) MarketplaceSingleOrderItem::whereHas('order', function ($q) use ($filters) {
            $this->applyDateFilter($q, $filters);
        })->sum('publisher_payment_paid');
    }

    /*********************************************************************
     * GET WITHDRAWAL REQUESTS TOTAL
     **********************************************************************
     *
     * Sum of wallet withdrawal requests.
     *
     * @param array $filters - date range filters
     * @return float - total withdrawal amount
     *
     *********************************************************************/
    private function getWithdrawals(array $filters): float
    {
        return (float) WalletWithdrawalRequest::when(! empty($filters), fn($q) => $this->applyDateFilter($q, $filters))
            ->sum('amount');
    }

    /*********************************************************************
     * GET OUTSTANDING PUBLISHER BALANCES
     **********************************************************************
     *
     * Total outstanding balances across all publisher wallets.
     * Uses cached publisher IDs.
     *
     * @return float - total outstanding balance
     *
     *********************************************************************/
    private function getOutstandingBalances(): float
    {
        // -----------------------
        // Get Cached Publisher IDs
        $publisherIds = Cache::remember('publisher_ids', now()->addHour(), function () {
            return User::where('role', Role::Publisher->value)->pluck('id');
        });


        // -----------------------
        // Sum Publisher Wallet Balances
        return (float) Wallet::whereIn('holder_id', $publisherIds)->sum('balance');
    }
}