<?php

namespace Domain\Stats\Actions\Sales;

use App\Models\MarketplaceOrder;
use App\Models\MarketplaceSingleOrderItem;
use App\Traits\Filters\DateFilterTrait;
use Domain\Stats\Modules\StatsFormatterTrait;
use Illuminate\Support\Facades\Cache;

// Single action to calculate user-specific sales statistics
class CalculateUserSalesStats
{
    use StatsFormatterTrait, DateFilterTrait;

    /*********************************************************************
     * HANDLE
     **********************************************************************
     *
     * Calculate sales stats for specific user
     *
     * @param int $userId
     * @param array $filters
     * @return array
     *
     *********************************************************************/
    public function handle(int $userId, array $filters): array
    {
        $cacheKey = $this->generateCacheKey("user_stats_{$userId}", $filters);

        return Cache::remember($cacheKey, config('pressbear.fiveMinuteCache'), function () use ($userId, $filters) {
            // -----------------------
            // Get Data From List Class
            $userOrdersData = $this->getUserOrdersData($userId, $filters);

            // -----------------------
            // Return Formatted Stats
            return [
                'total_orders' => $userOrdersData['total_count'],
                'total_sales' => $this->formatCurrency($userOrdersData['total_sales']),
                'total_profit' => $this->formatCurrency($userOrdersData['total_sales'] - $userOrdersData['total_publisher_payments']),
            ];
        });
    }




    /*********************************************************************
     * GET USER ORDERS DATA
     **********************************************************************
     *
     * Get user orders with items data in optimized way
     *
     * @param int $userId
     * @param array $filters
     * @return array
     *
     *********************************************************************/
    private function getUserOrdersData(int $userId, array $filters): array
    {
        // -----------------------
        // Get User Orders
        $userOrders = MarketplaceOrder::where('user_id', $userId);

        // -----------------------
        // Apply Date Filters
        $this->applyDateFilter($userOrders, $filters);

        // -----------------------
        $orderAgg = $userOrders
            ->get(['price_paid'])               // fetch just the column
            ->pluck('price_paid');

        // -----------------------
        // Calculate Totals
        $totalCount = $orderAgg->count();
        $totalSales = $orderAgg->sum();


        // -----------------------
        // Get User Order Items
        $itemQuery = MarketplaceSingleOrderItem::whereHas('order', function ($q) use ($userId, $filters) {
            $q->where('user_id', $userId);
            $this->applyDateFilter($q, $filters);
        });

        // -----------------------
        $totalPublisherPayments = $itemQuery->sum('publisher_payment_paid');

        return [
            'total_count'               => $totalCount,
            'total_sales'               => $totalSales,
            'total_publisher_payments'  => $totalPublisherPayments,
        ];
    }
}
