<?php

namespace Domain\Stats\Lists;

use Domain\Stats\Actions\UserActivity\CalculateUserActivitySummary;

// User-specific statistics and performance metrics - uses Actions for calculations
class GetUserStats
{


    /*********************************************************************
     * USER ACTIVITY SUMMARY
     **********************************************************************
     *
     * Get comprehensive activity summary for user
     *
     * @param int $userId
     * @param array $filters
     * @return array
     *
     *********************************************************************/
    public function getUserActivitySummary(int $userId, array $filters): array
    {
        return (new CalculateUserActivitySummary)->handle($userId, $filters);
    }
}
