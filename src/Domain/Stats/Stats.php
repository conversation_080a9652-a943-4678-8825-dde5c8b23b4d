<?php

namespace Domain\Stats;

use Domain\Stats\Lists\GetSalesStats;
use Domain\Stats\Lists\GetUserStats;
use Domain\Stats\Lists\Outreach\GetOutreachStats;
use Domain\Stats\Lists\Outreach\getOutreachUsersWithStats;
use Illuminate\Pagination\LengthAwarePaginator;

/*********************************************************************
 * STATS DOMAIN FACADE
 *********************************************************************
 *
 * Provides a centralized interface for accessing various statistics
 * and analytics data across the marketplace system.
 *
 * This facade simplifies access to different types of statistics:
 * - Outreach statistics for marketing and engagement metrics
 * - Sales statistics for revenue and transaction data
 * - User statistics for user behavior and activity data
 *
 * Responsibilities:
 * - Centralized access to statistical data
 * - Abstraction of complex statistical calculations
 * - Consistent interface for different stat types
 *
 *********************************************************************/
class Stats
{
    /*********************************************************************
     * GET OUTREACH STATISTICS
     *********************************************************************
     *
     * Retrieves outreach and engagement statistics for a specific user.
     * Includes metrics like email campaigns, social media engagement,
     * and other marketing-related data.
     *
     * @param int $userId - User ID to get statistics for
     * @param array $filters - Optional filters to apply to the statistics
     * @return array - Array of outreach statistics
     *
     *********************************************************************/
    public static function outreach(int $userId, array $filters = []): array
    {
        return (new GetOutreachStats())($userId, $filters);
    }




    /*********************************************************************
     * GET SALES STATISTICS
     *********************************************************************
     *
     * Returns a sales statistics service instance for accessing
     * revenue, transaction, and sales-related metrics.
     *
     * @return GetSalesStats - Sales statistics service instance
     *
     *********************************************************************/
    public static function sales(): GetSalesStats
    {
        return new GetSalesStats();
    }




    /*********************************************************************
     * GET USER STATISTICS
     *********************************************************************
     *
     * Returns a user statistics service instance for accessing
     * user behavior, activity, and demographic data.
     *
     * @return GetUserStats - User statistics service instance
     *
     *********************************************************************/
    public static function user(): GetUserStats
    {
        return new GetUserStats();
    }




    /*********************************************************************
     * GET OUTREACH USERS WITH STATISTICS
     *********************************************************************
     *
     * Retrieves paginated outreach users with their performance stats.
     * Includes filtered counts for inprogress, onboarded, and rejected
     * outreaches with date-based filtering support.
     *
     * @param array $filters - Filter parameters for date ranges and pagination
     * @return LengthAwarePaginator - Paginated users with stats
     *
     *********************************************************************/
    public static function outreachUsers(array $filters = []): LengthAwarePaginator
    {
        return (new getOutreachUsersWithStats())->handle($filters);
    }
}
