<?php

namespace Domain\Writer\Traits;

use Illuminate\Database\Eloquent\Builder;

/*********************************************************************
 * WRITER FILTERS TRAIT
 *********************************************************************
 *
 * Provides filtering functionality for writer assignment queries.
 * This trait contains methods for filtering marketplace order items
 * based on assignment status, completion state, and search terms.
 *
 * Responsibilities:
 * - Filter by assignment status (assigned/unassigned, completed, upcoming)
 * - Apply search filters across writer and website data
 * - Build complex query conditions for writer management
 * - Support admin panel filtering and search functionality
 *
 *********************************************************************/
trait WriterFiltersTrait
{

    /**********************************
     * APPLY ASSIGNMENT STATUS FILTER
     ***********************************/
    private function applyAssignmentStatusFilter(Builder $query, string $status): void
    {
        match ($status) {
            'assigned_unassigned' => $this->filterAssignedOrUnassigned($query),
            'completed'           => $this->filterCompleted($query),
            'upcoming'            => $this->filterUpcoming($query),
            default               => null,
        };
    }





    /**********************************
     * FILTER ASSIGNED OR UNASSIGNED
     ***********************************/
    private function filterAssignedOrUnassigned(Builder $query): void
    {
        $query->where(function ($q) {
            $q->where(function ($inner) {
                $inner->whereHas('content', fn($c) => $c->whereNotNull('writer_id'))
                    ->whereIn('state', $this->contentStates);
            })->orWhere(function ($inner) {
                $inner->whereDoesntHave('content')
                    ->whereIn('state', $this->contentStates);
            });
        });
    }





    /**********************************
     * FILTER COMPLETED
     ***********************************/
    private function filterCompleted(Builder $query): void
    {
        $query->whereHas('content', fn($q) => $q->whereNotNull('writer_id'))
            ->whereNotIn('state', $this->contentStates);
    }





    /**********************************
     * FILTER UPCOMING
     ***********************************/
    private function filterUpcoming(Builder $query): void
    {
        $query->whereNotIn('state', $this->contentStates)
            ->where(function ($q) {
                $q->where('is_content_provided_by_customer', true)
                    ->orWhereDoesntHave('content');
            })
            ->where(function ($q) {
                $q->whereDoesntHave('content')
                    ->orWhereHas('content', fn($q) => $q->whereNull('writer_id'));
            });
    }





    /*******************************************************************
     * APPLY SEARCH FILTER
     *********************************************************************
     *
     * Applies search filtering to the query based on writer information
     * ..and website domain.
     *
     * Searches across:
     * - Writer name and email
     * - Website domain
     *
     * @param \Illuminate\Database\Eloquent\Builder $query - The query builder instance
     * @param string $searchTerm - The search term to filter by
     *
     *******************************************************************/
    private function applySearchFilter(Builder $query, string $searchTerm): void
    {
        $query->when($searchTerm, fn($q) => $q->searchByTerm($searchTerm));
    }
}
