<?php

namespace Domain\Payment\Action;

use App\Models\MarketplacePayment;

/*********************************************************************
 * CREATE PAYMENT ACTION
 *********************************************************************
 *
 * Handles the creation of payment records in the marketplace system.
 * This action is responsible for storing payment information from
 * various payment providers and checkout sessions.
 *
 * Responsibilities:
 * - Validates payment data structure
 * - Maps payment data to database fields
 * - Creates payment records with proper formatting
 * - Ensures data consistency and integrity
 *
 *********************************************************************/
class CreatePayment
{


    /*********************************************************************
     * STORE PAYMENT DATA - SAVE PAYMENT INFORMATION
     *********************************************************************
     *
     * Creates a new payment record in the database with payment details.
     * - Validates payment data structure
     * - Maps checkout session data to payment model
     * - Creates and returns payment record
     *
     * @param array $paymentData - Payment information containing user_id,
     * ..payment_intent_id, total_amount, and status
     *
     * @return MarketplacePayment - Created payment record with all details
     *
     *********************************************************************/
    public function handle(array $paymentData)
    {
        // -----------------------
        // Create Payment Record
        // Maps payment data to database fields and creates payment record
        return MarketplacePayment::create([
            'user_id' => $paymentData['user_id'],
            'external_transaction_id' => $paymentData['payment_intent_id'],
            'payment_amount' => $paymentData['total_amount'],
            'status' => $paymentData['status'],
        ]);
    }
}
