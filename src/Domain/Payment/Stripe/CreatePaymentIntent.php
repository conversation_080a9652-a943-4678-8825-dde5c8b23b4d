<?php

namespace Domain\Payment\Stripe;

use App\Models\User;
use Stripe\StripeClient;

class CreatePaymentIntent
{
    /*************************************************
     * Constructor
    /************************************************/
    private $STRIPE_CLIENT;

    public function __construct()
    {
        $this->STRIPE_CLIENT = config('services.stripe.client');
    }

    /*************************************************
     * Handle
    /************************************************/
    public function handle(string $amount, User $user): object | bool
    {
        //initiate stripe
        $stripe = new StripeClient($this->STRIPE_CLIENT);

        try {
            $StripeMetadata = [
                'amount' => $amount * 100,
                'currency' => 'usd',
                'receipt_email' => $user->email,
                'metadata' => [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'user_email' => $user->email,
                    'company' => $user->company,
                    'address' => $user->address_data['address'] ?? 'N/A',
                    'city' => $user->address_data['city'] ?? 'N/A',
                    'postal_code' => $user->address_data['postal_code'] ?? 'N/A',
                    'country_id' => $user->country_id,
                ]
            ];
            // Create a PaymentIntent with amount and currency
            $paymentIntent = $stripe->paymentIntents->create($StripeMetadata);

            return $paymentIntent;
        } catch (\Exception $e) {
            http_response_code(500);
            return false;
        }
    }
}
