<?php

namespace Domain\Payment\Stripe;

use Stripe\StripeClient;
use Illuminate\Support\Facades\Log;
use Domain\Payment\Exceptions\PaymentFailedException;

class GetPaymentByIntentID
{
    /*************************************************
     * Constructor
    /************************************************/
    private $STRIPE_CLIENT;

    public function __construct()
    {
        $this->STRIPE_CLIENT = config('services.stripe.client');
    }

    /*************************************************
     * Handle
    /************************************************/
    public function handle(string $paymentIntentId): object
    {
        try {
            // -----------------------
            // initiate stripe
            $stripe = new StripeClient($this->STRIPE_CLIENT);

            // Returning after redirecting to a payment method portal.
            $paymentData = $stripe->paymentIntents->retrieve($paymentIntentId);

            return $paymentData;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            throw new PaymentFailedException();
        }
    }
}
