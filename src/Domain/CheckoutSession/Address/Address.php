<?php

namespace Domain\CheckoutSession\Address;

use Domain\CheckoutSession\Address\Actions\UpdateAddress;
use App\Models\User;

class Address
{

    /*********************************************************************
     * ADDRESS
     **********************************************************************
     *
     * Represents the user's address information.
     *
     *********************************************************************/
    public function updateAddress(array $data, User $user): bool
    {
        return (new UpdateAddress())->handle($data, $user);
    }
}
