<?php

namespace Domain\CheckoutSession\Address\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddressUpdateRequest extends FormRequest
{

    /*********************************************************************
     * AUTHORIZE - CHECK IF USER IS AUTHORIZED TO UPDATE ADDRESS
     *********************************************************************
     *
     * Checks if the user is authorized to update their address.
     *
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }
    /*********************************************************************
     * ADDRESS UPDATE REQUEST
     **********************************************************************
     *
     * Validates the request data for updating the user's address.
     *
     * @return array
     * The validation rules for the request.
     *
     *********************************************************************/
    public function rules(): array
    {
        return [
            'data' => 'required|string|in:address',
            'company' => 'regex:/^[\pL\s\-\d]+$/u|max:100',
            'address' => 'regex:/^[\pL\s\-\d,\/]+$/u|max:300',
            'city' => 'regex:/^[\pL\s\-]+$/u|max:100',
            'postal_code' => 'required|regex:/^[\pL\s\-\d]+$/u|max:100',
            'country_id' => 'required|integer|max:1000',
            'order_memo' => 'nullable|string|max:255'
        ];
    }
}
