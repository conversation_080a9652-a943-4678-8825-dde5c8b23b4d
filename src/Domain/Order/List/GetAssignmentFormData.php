<?php

namespace Domain\Order\List;

use App\Models\MarketplaceSingleOrderItem;
use App\Enums\OrderItemStates;

class GetAssignmentFormData
{
    /*********************************************************************
     * GET ASSIGNMENT FORM DATA
     **********************************************************************
     * Retrieve data for assignment form.
     *
     * @param MarketplaceSingleOrderItem $item
     * @return array
     *********************************************************************/
    public function __invoke(
        MarketplaceSingleOrderItem $item
    ): array
    {
        // Load relationships
        $item->load(['content.writer']);


        // Get statuses
        $statuses = [
            OrderItemStates::ContentPending->value,
            OrderItemStates::ContentAssignedToWriter->value,
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ];


        // Append status attribute
        $item->append('content_status');


        return [
            'item'           => $item,
            'statuses'       => $statuses,
            'content_status' => $item->content_status,
            'writer'         => $item->content?->writer,
        ];
    }

}
