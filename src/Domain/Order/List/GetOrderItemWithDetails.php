<?php

namespace Domain\Order\List;

use App\Http\Resources\MediaResource;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\MarketplaceOrder;

class GetOrderItemWithDetails
{


    /*********************************************************************
     * GET ORDER ITEM WITH DETAILS
     **********************************************************************
     *
     * Retrieves order item with relations and formatted media
     *
     * @param MarketplaceOrder           $order  Parent order
     * @param MarketplaceSingleOrderItem $item   Order item
     * @return array                                Item details
     *********************************************************************/
    public function __invoke(MarketplaceOrder $order, MarketplaceSingleOrderItem $item): array
    {
        // -----------------------
        // Load Relations
        $item->load([
            'requirements',
            'publication',
            'content',
            'content.media',
            'website',
            'website.publisher',
            'content.writer',
        ]);


        // -----------------------
        // Load Order User
        $order->load('user');


        // -----------------------
        // Format Media
        $media = MediaResource::collection(optional($item->content)->media ?? [])->resolve();


        // -----------------------
        // Append Attributes
        $item->append('content_status');


        return [
            'order' => $order,
            'item'  => $item,
            'media' => $media,
        ];
    }
}
