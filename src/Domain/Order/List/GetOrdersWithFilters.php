<?php

namespace Domain\Order\List;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceOrder;
use App\Traits\Filters\DateFilterTrait;
use Illuminate\Pagination\LengthAwarePaginator;

class GetOrdersWithFilters
{
    use DateFilterTrait;

    

    /*********************************************************************
     * GET ORDERS WITH FILTERS
     *********************************************************************
     *
     * Retrieves orders with optional filters and pagination
     *
     * @param array                 $inputs  Filter and pagination params
     * @return LengthAwarePaginator           Paginated order results
     *********************************************************************/
    public function __invoke(array $inputs): LengthAwarePaginator
    {
        // -----------------------
        // Initialize Query
        $query = MarketplaceOrder::query()
            ->with(['user']);


        // -----------------------
        // Date Filter
        $query = $this->applyDateFilter($query, $inputs);


        // -----------------------
        // Search Filter
        if (! empty($inputs['searchTerm'] ?? null)) {
            $query->search($inputs['searchTerm']);
        }


        // -----------------------
        // Status Filter
        if (isset($inputs['orderStatus'])) {
            $query->where('status', $inputs['orderStatus']);
        }


        // -----------------------
        // Sorting
        $query->sort(
            $inputs['sortField'] ?? 'id',
            $inputs['sortOrder'] ?? 'desc'
        );


        // -----------------------
        // Item Counts
        $query->withCount([
            'orderItems',
            'orderItems as completed_order_items_count' => function ($q): void {
                $q->where('state', OrderItemStates::OrderItemCompleted->value);
            },
        ]);


        // -----------------------
        // Paginate
        return $query->paginate(
            $inputs['perPage'] ?? config('pressbear.default_pagination_10')
        );
    }

}
