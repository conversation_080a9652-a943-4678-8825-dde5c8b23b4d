<?php

namespace Domain\Order\List;

use App\Models\MarketplaceSingleOrderItem;

class GetOrderItemById
{
    /*************************************************
     * Get Order Item By Id
    /************************************************/
    public function handle(int $id, array $with = []): MarketplaceSingleOrderItem
    {
        return MarketplaceSingleOrderItem::with($with)->findOrFail($id);
    }
}
