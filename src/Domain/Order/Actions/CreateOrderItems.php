<?php

namespace Domain\Order\Actions;


use App\Models\MarketplaceCheckoutSession;
use App\Models\MarketplaceOrder;
use Domain\Cart\List\GetCartItemsByIds;
use Domain\Order\Resources\MarketplaceSingleOrderItemResource;
use App\Models\MarketplaceSingleOrderItem;

class CreateOrderItems
{
    /*********************************************************************
     * CREATE ORDER ITEMS FROM CHECKOUT SESSION
     *********************************************************************
     *
     * PURPOSE:
     * Converts cart items from a checkout session into actual order items
     * that are linked to a specific order. This bridges the gap between
     * temporary cart items and permanent order records.
     *
     * WHAT IT DOES:
     * 1. Retrieves cart items by their IDs from the checkout session
     * 2. Transforms each cart item into an order item format using a resource
     * 3. Creates database records for each order item
     * 4. Returns the created order items collection
     *
     * USAGE:
     * Called during order creation process after successful checkout to
     * convert temporary cart items into permanent order records.
     *
     *********************************************************************/
    public function handle(MarketplaceCheckoutSession $sessionData, MarketplaceOrder $order): MarketplaceSingleOrderItem|string
    {

        // -----------------------
        // Create Order Items
        try {
            $items = (new GetCartItemsByIds())->handle($sessionData->cart_items_ids, ['website']);

            $items_array = $items->map(function ($item) use ($order) {
                return (new MarketplaceSingleOrderItemResource($item, $order))->resolve();
            })->toArray();

            return collect($items_array)->map(function ($item) {
                return MarketplaceSingleOrderItem::create($item);
            });
        } catch (\Exception $e) {
            throw new \Exception('Failed to create order items');
        }
    }
}
