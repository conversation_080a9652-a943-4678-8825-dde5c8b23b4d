<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\States\ContentUpdate;
use App\States\OrderItem\ContentAssignedToWriter;

class AssignWriterToOrderItem
{
    /*********************************************************************
     * ASSIGN WRITER TO ORDER ITEM
     **********************************************************************
     * Assign writer by creating content and transitioning state.
     *
     * @param MarketplaceSingleOrderItem $item
     * @param int                        $writerId
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function __invoke(
        MarketplaceSingleOrderItem $item,
        int $writerId
    ): MarketplaceSingleOrderItem
    {
        // Check if already in ContentAssignedToWriter state
        $isAlreadyAssigned = $item->state_name === 'content-assigned-to-writer';

        // Prepare data
        $data = [
            'title'           => '',
            'content_body'    => '',
            'content_url'     => '',
            'comments'        => '',
            'content_source'  => 0,
            'writer_id'       => $writerId,
            'files_array'     => [],
            'next_state'      => $isAlreadyAssigned ? null : ContentAssignedToWriter::class,
        ];

        // Create content and transition state (only if needed)
        return (new ContentUpdate())->handle($item, $data);
    }

}
