<?php

namespace Domain\Order\Actions\States;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\States\ContentUpdate;
use Domain\Order\Actions\StateTransitions;
use Illuminate\Support\Facades\DB;

class WriterAssignment
{
    /*********************************************************************
     * HANDLE WRITER ASSIGNMENT
     **********************************************************************
     * Handle writer assignment with content and state.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * @param array                       $data
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function handle(
        MarketplaceSingleOrderItem $orderItem,
        array $data
    ): MarketplaceSingleOrderItem
    {
        DB::beginTransaction();

        try {
            $writerId  = $data['writer_id'];
            $nextState = $data['next_state'];

            // Content
            if ($writerId) {
                if (! $orderItem->content) {
                    (new ContentUpdate())->handle($orderItem, [
                        'title'          => '',
                        'content_body'   => '',
                        'content_url'    => '',
                        'comments'       => '',
                        'content_source' => 0,
                        'writer_id'      => $writerId,
                        'files_array'    => [],
                        'next_state'     => null,
                    ]);
                } else {
                    $orderItem->content->update(['writer_id' => $writerId]);
                }
            }

            $orderItem->refresh();

            // State transition
            if ($nextState && $orderItem->state_name !== $nextState) {
                $orderItem = (new StateTransitions())
                    ->handle($orderItem, $nextState);
            }

            DB::commit();

            return $orderItem;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception(
                'Error updating writer assignment: ' . $e->getMessage()
            );
        }
    }

}
