<?php

namespace Domain\Order\Actions\States;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\StateTransitions;
use Illuminate\Support\Facades\DB;


class RequirementsUpdate
{


    /*********************************************************************
     * HANDLE
     **********************************************************************
     * Create or update requirements and transition state.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * @param array                       $data
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function handle(
        MarketplaceSingleOrderItem $orderItem,
        array $data
    ): MarketplaceSingleOrderItem
    {
        DB::beginTransaction();

        try {
            // Create or update requirements
            $orderItem->requirements()->updateOrCreate(
                ['order_item_id' => $orderItem->id],
                [
                    'article_topic'        => $data['article_topic'],
                    'anchor_text'          => $data['anchor_text'],
                    'advertiser_url'       => $data['advertiser_url'],
                    'requirement_comments' => $data['requirement_comments'],
                ]
            );


            // Refresh order item
            $orderItem->refresh();


            // Optional state transition
            $nextState = $data['next_state']
                ?? OrderItemStates::RequirementAwaitingPublisherApproval->value;

            if ($nextState && $orderItem->state_name !== $nextState) {
                $orderItem = (new StateTransitions())
                    ->handle($orderItem, $nextState);
            }


            DB::commit();

            return $orderItem;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception(
                'Error updating or creating requirements: ' . $e->getMessage()
            );
        }
    }

}
