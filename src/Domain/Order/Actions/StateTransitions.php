<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;

/*********************************************************************
 * STATE TRANSITIONS ACTION
 *********************************************************************
 *
 * Handles state transitions for marketplace order items.
 * This action manages the workflow state changes for order items
 * throughout their lifecycle from creation to completion.
 *
 * Responsibilities:
 * - Validates state transitions
 * - Updates order item state
 * - Ensures proper workflow progression
 *
 *********************************************************************/
class StateTransitions
{
    /*********************************************************************
     * HANDLE STATE TRANSITION
     *********************************************************************
     *
     * Transitions an order item to a new state in the workflow.
     * This method validates the transition and updates the order item's
     * state using the state machine pattern.
     *
     * @param MarketplaceSingleOrderItem $orderItem - The order item to transition
     * @param string $state - The new state to transition to
     * @return MarketplaceSingleOrderItem - Updated order item with new state
     *
     *********************************************************************/
    public function handle(MarketplaceSingleOrderItem $orderItem, string $state): MarketplaceSingleOrderItem
    {
        $orderItem->state->transitionTo($state);
        return $orderItem;
    }
}
