<?php

namespace Domain\Order\Actions;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\StateTransitions;
use Domain\Order\Actions\States\ContentCreation;

class UpdateOrderItemState
{
    /*********************************************************************
     * UPDATE ORDER ITEM STATE
     **********************************************************************
     *
     * Routes state transitions through correct handler
     *
     * @param MarketplaceSingleOrderItem $item
     * @param string                    $newState
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function __invoke(MarketplaceSingleOrderItem $item, string $newState): MarketplaceSingleOrderItem
    {
        $hasCustomerContent = $item['is_content_provided_by_customer'];


        // -----------------------
        // Content Pending Override
        if ($item->state_name === OrderItemStates::ContentPending->value
            && $hasCustomerContent
        ) {
            return (new ContentCreation())->handle($item, $newState);
        }


        // -----------------------
        // Default Transition
        return (new StateTransitions())->handle($item, $newState);
    }

}
