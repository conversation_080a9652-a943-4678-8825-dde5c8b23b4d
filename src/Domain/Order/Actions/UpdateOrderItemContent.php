<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\States\ContentUpdate;

class UpdateOrderItemContent
{
    /*********************************************************************
     * UPDATE ORDER ITEM CONTENT
     **********************************************************************
     * Admin content update via ContentUpdate action.
     *
     * @param MarketplaceSingleOrderItem $item
     * @param array                       $contentData
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function __invoke(
        MarketplaceSingleOrderItem $item,
        array $contentData
    ): MarketplaceSingleOrderItem
    {
        // Prepare data
        $data = array_merge($contentData, [
            'content_source' => 0,
            'files_array'    => $contentData['files_array'] ?? [],
            'next_state'     => \App\States\OrderItem\ContentAdvertiserReview::class,
        ]);

        // Update content
        return (new ContentUpdate())->handle($item, $data);
    }

}
