<?php

namespace Domain\Order\Modules;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\States\{ContentUpdate, RequirementsUpdate};

trait StatesTraits
{
    /*************************************************
     * State Handlers
    /************************************************/
    protected array $stateHandlers = [
        OrderItemStates::RequirementAwaitingPublisherApproval->value => RequirementsUpdate::class,
        OrderItemStates::ContentAwaitingPublisherApproval->value => ContentUpdate::class,
        OrderItemStates::ContentAssignedToWriter->value => ContentUpdate::class,
    ];

    /*************************************************
     * Transition State
    /************************************************/
    public function transitionState(OrderItemStates $state, MarketplaceSingleOrderItem $orderItem, array $data)
    {
        $handlerClass = $this->stateHandlers[$state->value];
        return (new $handlerClass())->handle($orderItem, $data);
    }
}
