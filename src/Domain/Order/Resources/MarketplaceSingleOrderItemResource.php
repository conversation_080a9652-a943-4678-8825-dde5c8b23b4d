<?php

namespace Domain\Order\Resources;

use Illuminate\Http\Request;
use App\Enums\OrderItemStates;
use Illuminate\Http\Resources\Json\JsonResource;

class MarketplaceSingleOrderItemResource extends JsonResource
{
    public $order;

    /*************************************************
     * Order Data
    /************************************************/
    public function __construct($resource, $order = null)
    {
        parent::__construct($resource);
        $this->order = $order;
    }

    /*************************************************
     * Transform the resource into an array.
    /************************************************/
    public function toArray(Request $request): array
    {

        return [
            'order_id'                        => $this->order->id ?? $this->order_id,
            'state'                           => $this->state ?? OrderItemStates::RequirementsPending->value,
            'marketplace_website_id'          => $this->website->id,
            'price_paid'                      => $this->website->nichePrice($this->niche),
            'publisher_payment_paid'          => $this->website->nichePriceOriginal($this->niche),
            'niche'                           => $this->niche,
            'is_content_provided_by_customer' => $this->content_writing,
            'estimated_publication_date'      => $this->estimated_publication_date ?? now()->addDays($this->website->turn_around_time_in_days + 2),
            'delivery_date'                   => $this->delivery_date ?? now(),
            'created_at'                      => $this->created_at ?? now(),
            'updated_at'                      => $this->updated_at ?? now(),
        ];
    }
}
