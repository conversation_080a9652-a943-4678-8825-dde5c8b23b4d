<?php

namespace Domain\Seo\Jobs;

use App\Models\MarketplaceWebsite;
use Domain\Seo\Actions\UpdateWebsiteSeo;
use Domain\Seo\Enums\SEOSourceType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

/*********************************************************************
 * FETCH WEBSITE SEO JOB
 *********************************************************************
 *
 * Handles the asynchronous fetching of SEO data for marketplace websites.
 * This job processes SEO data retrieval from external sources and updates
 * the website's SEO statistics in the background.
 *
 * Responsibilities:
 * - Fetches SEO data from external APIs
 * - Updates website SEO statistics
 * - Handles retry logic and error recovery
 * - Processes data in background queues
 *
 *********************************************************************/
class FetchWebsiteSeo implements ShouldQueue
{
    use Dispatchable, Queueable;

    /*********************************************************************
     * CONSTRUCTOR - INITIALIZE SEO FETCH JOB
     *********************************************************************
     *
     * Sets up the job with website, source type, and data type parameters.
     * Configures the queue for SEO processing.
     *
     * @param MarketplaceWebsite $website - The website to fetch SEO data for
     * @param SEOSourceType $sourceType - The SEO data source (e.g., SimilarWeb)
     * @param string $type - The type of SEO data to fetch (domain, keyword, etc.)
     *
     *********************************************************************/
    public function __construct(private MarketplaceWebsite $website, private SEOSourceType $sourceType, private string $type = 'domain')
    {
        $this->onQueue(config('pressbear.seo_queue_name'));
    }

    /*********************************************************************
     * GET MAXIMUM RETRY ATTEMPTS
     *********************************************************************
     *
     * Returns the maximum number of retry attempts for this job.
     *
     * @return int - Maximum number of retry attempts
     *
     *********************************************************************/
    public function tries()
    {
        return config('pressbear.seo_queue_tries');
    }

    /*********************************************************************
     * GET JOB TIMEOUT
     *********************************************************************
     *
     * Returns the timeout duration for this job in seconds.
     *
     * @return int - Timeout duration in seconds
     *
     *********************************************************************/
    public function timeout()
    {
        return config('pressbear.seo_queue_timeout');
    }

    /*********************************************************************
     * HANDLE SEO DATA FETCHING
     *********************************************************************
     *
     * Executes the SEO data fetching process by delegating to the
     * UpdateWebsiteSeo action.
     *
     * @return mixed - Result of the SEO update operation
     *
     *********************************************************************/
    public function handle()
    {
        return (new UpdateWebsiteSeo($this->website, $this->sourceType, $this->type))();
    }
}
