<?php

declare(strict_types=1);

namespace Domain\Seo\Actions;

use App\Models\MarketplaceWebsite;
use Domain\Seo\Enums\SEOSourceType;
use Domain\Seo\Helpers\DataHelper;
use Domain\Seo\Services\SeoDataMapperFactory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/***************************************************************************
 * UPDATE SEO DATA ACTION
 ***************************************************************************
 * Story: Updates website SEO data using the appropriate mapper
 * based on the source type and data type.
 * 
 * Purpose: Maps and updates SEO statistics from various sources
 * to the website's SEO stats record.
 ****************************************************************************/
class UpdateSeoData
{
    /*********************************************************************
     * UPDATE SEO DATA ACTION
     *********************************************************************
     *
     * Updates the SEO data for the website.
     *
     * @param MarketplaceWebsite $website
     * @param array $data
     * @param SEOSourceType $sourceType
     * @param string $type
     * @return bool
     */
    public function __invoke(MarketplaceWebsite $website, array $data, SEOSourceType $sourceType, string $type): bool
    {
        DB::beginTransaction();

        try {
            if ($sourceType === SEOSourceType::SimilarWeb) {
                // Update the website data
                $this->updateWebsiteData($website, $data);

                // Attach Keywords
                DataHelper::attachKeywords($website, $data['seo_keywords']);
            }

            // Update the Seo Data
            $updated = $this->updateSeoData($website, $data, $sourceType);


            DB::commit();

            return $updated;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }





    /*********************************************************************
     * UPDATE WEBSITE DATA
     *********************************************************************
     *
     * Updates the website data for the website.
     *
     * @param MarketplaceWebsite $website
     * @param array $data
     * @return void
     */
    private function updateWebsiteData(MarketplaceWebsite $website, array $data): void
    {

        $domain['site_description']       = Str::limit($data['description'], 950);
        $domain['site_title']             = Str::limit($data['title'], 670);

        $domain['category_global_rank']   = $data['category_rank']['Rank'] ?? 9999999;
        $domain['main_category_id']       = DataHelper::findCategoryID($data['site_category']);


        // update website data
        $website->update($domain);
    }




    /*********************************************************************
     * UPDATE SEO DATA
     *********************************************************************
     *
     * Updates the SEO data for the website.
     *
     * @param MarketplaceWebsite $website
     * @param array $data
     * @param SEOSourceType $sourceType
     * @return bool
     */
    private function updateSeoData(MarketplaceWebsite $website, array $data, SEOSourceType $sourceType): bool
    {
        // Get the appropriate mapper for the source type
        $mapper = SeoDataMapperFactory::create($sourceType);

        // Map the data to our database structure
        $mappedData = $mapper->map($data);

        // Get or create the SEO stats model
        $seoStats = $mapper->getSeoStatsModel($website);

        // Update the SEO stats with mapped data
        return $seoStats->update($mappedData);
    }
}
