<?php

declare(strict_types=1);

namespace Domain\Seo\Interface;

use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceWebsiteSeoStat;

/***************************************************************************
 * SEO DATA MAPPER INTERFACE
 ***************************************************************************
 * Story: Different SEO sources provide data in different formats and
 * need to be mapped to our standardized database structure.
 * 
 * Purpose: Defines the contract for mapping SEO data from various sources
 * to our database columns.
 ****************************************************************************/
abstract class SeoDataMapperInterface
{
    /**
     * Map SEO data from source format to database columns
     *
     * @param MarketplaceWebsite $website
     * @param array $data Raw data from SEO source
     * @param string $type Type of SEO data (domain, keyword, etc.)
     * @return array Mapped data ready for database update
     */
    abstract function map(array $data): array;

    /**
     * Get the SEO stats model for the website
     *
     * @param MarketplaceWebsite $website
     * @return MarketplaceWebsiteSeoStat
     */
    public function getSeoStatsModel(MarketplaceWebsite $website): MarketplaceWebsiteSeoStat
    {
        return $website->seoStats()->firstOrCreate([
            'marketplace_website_id' => $website->getKey(),
        ]);
    }
}
