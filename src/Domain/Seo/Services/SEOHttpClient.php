<?php

namespace Domain\Seo\Services;

use App\Models\MarketplaceWebsite;
use Illuminate\Support\Facades\Http;
use Domain\Seo\Enums\SEOSourceType;
use Domain\Seo\Actions\UpdateLogs;

/***************************************************************************
 * SEO HTTP CLIENT SERVICE
 ***************************************************************************
 * Story: Centralized HTTP client for all SEO-related API calls and data
 * fetching operations. Handles different SEO sources, retries, caching,
 * and error handling.
 * 
 * Purpose: Provides a unified interface for making HTTP requests to various
 * SEO APIs and services with proper error handling and caching.
 ****************************************************************************/
class SEOHttpClient
{
    private string $apiUrl;
    private string $apiToken;
    private string $url = '';
    private int $tries = 0;
    /*********************************************************************
     * SEO HTTP CLIENT
     *********************************************************************
     *
     * Handles the HTTP client for the SEO API
     *********************************************************************/

    public function __construct(private MarketplaceWebsite $website, private SEOSourceType $sourceType, private string $type)
    {
        $this->apiUrl = config('pressbear.seo_api_url');
        $this->apiToken = config('pressbear.seo_api_token');
        $this->url = $this->apiUrl . '?token=' . $this->apiToken . '&domain=' . $this->website->website_domain . '&type=' . $this->type . '&client=' . $this->sourceType->value;


        // Get the number of tries
        $this->tries = $this->website->seoStatsLogs()
            ->where('source', $this->sourceType->value)
            ->where('type', $this->type)
            ->first()->tries ?? 0;
    }





    /*********************************************************************
     * SEO HTTP CLIENT
     *********************************************************************
     *
     * Handles the HTTP client for the SEO API
     *********************************************************************/
    public function handle()
    {


        try {
            $response = Http::get($this->url);
            // Check if the response is successful
            if ($response->failed()) {
                $errorMessage = 'Website: ' . $this->website->website_domain . ' SEO API request failed with status code: ' . $response->status();
                $this->logAndThrowException($errorMessage, new \Exception($errorMessage));
            } elseif ($response->body() === '') {
                $errorMessage = 'Website: ' . $this->website->website_domain . ' SEO API returned empty response';
                $this->logAndThrowException($errorMessage, new \Exception($errorMessage));
            }

            return $response;
        } catch (\Throwable $th) {

            // Update the website Seo Stats Log
            $this->logAndThrowException($th->getMessage(), $th);
        }
    }

    public function logAndThrowException($message, $th)
    {
        $data = [
            'source' => $this->sourceType->value,
            'type' => $this->type,
            'tries' => $this->tries + 1,
            'is_success' => false,
            'error_message' => json_encode($message),
        ];

        (new UpdateLogs())($this->website, $data);

        throw $th;
    }
}
