<?php

declare(strict_types=1);

namespace Domain\Seo\Services;

use Domain\Seo\Enums\SEOSourceType;
use Domain\Seo\Interface\SeoDataMapperInterface;
use Domain\Seo\Services\Mappers\{SimilarWebMapper, AhrefWebMapper};
use InvalidArgumentException;


/***************************************************************************
 * SEO DATA MAPPER FACTORY
 ***************************************************************************
 * Story: Different SEO sources require different mapping strategies
 * to convert their API responses to our database structure.
 * 
 * Purpose: Creates the appropriate mapper based on the SEO source type.
 ****************************************************************************/
class SeoDataMapperFactory
{
    /**
     * Create mapper for the given source type
     *
     * @param SEOSourceType $sourceType
     * @return SeoDataMapperInterface
     * @throws InvalidArgumentException
     */
    public static function create(SEOSourceType $sourceType): SeoDataMapperInterface
    {
        return match ($sourceType) {
            SEOSourceType::SimilarWeb => new SimilarWebMapper(),
            SEOSourceType::Ahrefs => new AhrefWebMapper(),
            default => throw new InvalidArgumentException("Unsupported SEO source type: {$sourceType->value}"),
        };
    }
}
