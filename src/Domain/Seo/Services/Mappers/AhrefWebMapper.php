<?php

declare(strict_types=1);

namespace Domain\Seo\Services\Mappers;

use App\Models\MarketplaceWebsite;
use Domain\Seo\Interface\SeoDataMapperInterface;

/***************************************************************************
 * AHREF SEO DATA MAPPER
 ***************************************************************************
 * Story: Ahref provides traffic, ranking, and engagement metrics
 * that need to be mapped to our database structure.
 * 
 * Purpose: Maps Ahref API response data to our SEO stats columns.
 ****************************************************************************/
class <PERSON><PERSON>fWebMapper extends SeoDataMapperInterface
{
    /*********************************************************************
     * MAP AHREF SEO DATA
     *********************************************************************
     *
     * Maps Ahref API response data to our SEO stats columns.
     *********************************************************************/
    public function map(array $data): array
    {
        $mappedData = [];

        // Map Ahref Data
        if (isset($data['secondary']['ahref_global_rank'])) {
            $mappedData['ahref_global_rank'] = $data['secondary']['ahref_global_rank'];
        }

        if (isset($data['primary']['ahref_domain_rank'])) {
            $mappedData['ahref_domain_rank'] = $data['primary']['ahref_domain_rank'];
        }

        if (isset($data['primary']['ahref_traffic'])) {
            $mappedData['ahref_organic_traffic'] = $data['primary']['ahref_traffic'];
        }



        return $mappedData;
    }
}
