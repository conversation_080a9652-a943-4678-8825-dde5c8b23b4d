<?php

declare(strict_types=1);

namespace Domain\Seo\Services\Mappers;

use Domain\Seo\Interface\SeoDataMapperInterface;

/***************************************************************************
 * SIMILARWEB SEO DATA MAPPER
 ***************************************************************************
 * Story: SimilarWeb provides traffic, ranking, and engagement metrics
 * that need to be mapped to our database structure.
 * 
 * Purpose: Maps SimilarWeb API response data to our SEO stats columns.
 ****************************************************************************/
class SimilarWebMapper extends SeoDataMapperInterface
{
    /*********************************************************************
     * MAP SIMILARWEB SEO DATA
     *********************************************************************
     *
     * Maps SimilarWeb API response data to our SEO stats columns.
     *********************************************************************/
    public function map(array $data): array
    {
        $mappedData = [];

        // Map 
        if (isset($data['engagement']['bounce_rate_percentage'])) {
            $mappedData['bounce_rate'] = (int) $data['engagement']['bounce_rate_percentage'];
        }

        if (isset($data['engagement']['total_vistors'])) {
            $mappedData['similarweb_traffic'] = (string) $data['engagement']['total_vistors'];
        }

        if (isset($data['engagement']['time_on_site_in_seconds'])) {
            $mappedData['avg_time_on_site'] = (int) round($data['engagement']['time_on_site_in_seconds'], 2);
        }

        if (isset($data['engagement']['time_on_site_in_seconds'])) {
            $mappedData['pages_per_visit'] = (int) $data['engagement']['pageviews_per_visit'];
        }

        if (isset($data['global_site_rank'])) {
            $mappedData['similarweb_global_rank'] = (int) $data['global_site_rank'];
        }

        if (isset($data['is_small_site'])) {
            $mappedData['is_small_site'] = (bool) $data['is_small_site'];
        }

        if (isset($data['country_site_rank'])) {
            $mappedData['similarweb_top_countries'] =  json_encode($data['country_site_rank']);
        }

        if (isset($data['traffic_last_three_months'])) {
            $mappedData['similarweb_traffic_last_three_months'] = json_encode($data['traffic_last_three_months']);
        }

        if (isset($data['traffic_souces_percentage'])) {
            $mappedData['similarweb_traffic_sources_percentage'] = json_encode($data['traffic_souces_percentage']);
        }


        return $mappedData;
    }
}
