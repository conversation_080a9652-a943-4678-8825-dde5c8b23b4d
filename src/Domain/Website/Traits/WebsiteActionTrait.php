<?php

namespace Domain\Website\Traits;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\MarketplaceAdminWebsite;

trait WebsiteActionTrait
{
    /*******************************************************************
     * STORE NEW WEBSITE WITH TOPICS
     *********************************************************************
     *
     * Creates a new marketplace website and associates it with topics.
     * - Handles topic creation if they don't exist
     * - Uses database transaction for data integrity
     * - Returns website model on success, false on failure
     *
     * @param array $websiteData
     * Array containing website information and topics
     *
     * @return MarketplaceAdminWebsite|false
     * Returns website model on success, false on failure
     *
     *******************************************************************/
    public function storeWebsite(array $websiteData)
    {
        // -----------------------
        // Begin Transaction
        // -----------------------
        DB::beginTransaction();

        try {
            // -----------------------
            // Create Website
            // -----------------------
            $website = MarketplaceAdminWebsite::create(Arr::except($websiteData, 'topics'));

            // -----------------------
            // Sync Topics
            // -----------------------
            $website->syncTopics($websiteData['topics'] ?? []);

            DB::commit();

            return $website;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }


    /*******************************************************************
     * UPDATE EXISTING WEBSITE AND TOPICS
     *********************************************************************
     *
     * Updates website information and manages topic associations.
     * - Preserves original domain for publisher users
     * - Updates outreach status based on verification and active status
     * - Handles topic changes with activity logging
     * - Returns updated website model on success, false on failure
     *
     * @param array $websiteData
     * Array containing updated website information and topics
     *
     * @param MarketplaceAdminWebsite $website
     * The website model to be updated
     *
     * @return MarketplaceAdminWebsite|false
     * Returns updated website model on success, false on failure
     *
     *******************************************************************/
    public function updateWebsite(array $websiteData, MarketplaceAdminWebsite $website)
    {

        try {
            // -----------------------
            // Update Website Attributes
            // -----------------------
            $website->update(Arr::except($websiteData, 'topics'));

            // -----------------------
            // Sync Topics
            // -----------------------
            $website->syncTopics($websiteData['topics'] ?? []);

            return $website;
        } catch (\Exception $e) {
            return false;
        }
    }
}
