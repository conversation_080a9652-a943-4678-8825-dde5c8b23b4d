<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_single_order_items', function (Blueprint $table): void {
            $table->id();

            $table->foreignId('order_id')->constrained('marketplace_orders')->onDelete('cascade');
            $table->foreignId('marketplace_website_id')->nullable()->constrained()->onDelete('set null');
            $table->string('state');
            $table->decimal('price_paid', 10, 2);
            $table->decimal('publisher_payment_paid', 10, 2);
            $table->date('estimated_publication_date')->nullable();
            // Customer Data
            $table->boolean('is_content_provided_by_customer')->default(false);
            $table->string('niche', 50)->default('general');


            // Publisher Data
            // $table->text('published_post_url')->nullable(); //legacy, saving in publication table instead
            $table->date('delivery_date')->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_single_order_items');
    }
};
