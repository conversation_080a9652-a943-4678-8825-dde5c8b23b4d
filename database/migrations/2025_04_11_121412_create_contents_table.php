<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_item_contents', function (Blueprint $table) {
            $table->id();
            $table->enum('content_source', ['customer', 'team']);
            $table->string('title');
            $table->longText('content_body');
            $table->string('content_url')->nullable();
            $table->json('files_array')->nullable();
            $table->foreignId('order_item_id')->constrained('marketplace_single_order_items')->onDelete('cascade');
            $table->foreignId('writer_id')->constrained('users')->nullable()->onDelete('cascade');
            $table->text('comments')->nullable();
            $table->text('advertiser_revision_reason')->nullable();
            $table->text('publisher_advertiser_revision_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_item_contents');
    }
};
