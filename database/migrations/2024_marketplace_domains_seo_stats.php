<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_website_seo_stats', function (Blueprint $table) {

            // ids
            $table->id();
            $table->foreignId('marketplace_website_id')
                ->constrained('marketplace_websites')
                ->onDelete('cascade');
            // ahrefs
            $table->unsignedInteger('ahref_global_rank')->nullable();
            $table->unsignedTinyInteger('ahref_domain_rank')->default(0)->index();
            $table->unsignedBigInteger('ahref_organic_traffic')->default(0)->index();

            $table->unsignedBigInteger('reffering_domains_count')->default(0);
            $table->unsignedBigInteger('total_backlinks_count')->default(0);
            $table->unsignedBigInteger('outgoing_links_count')->default(0);

            $table->foreignId('top_traffic_country_id')
                ->nullable()
                ->constrained('countries_list', 'id', 'seo_stats_top_country_fk')
                ->onDelete('cascade'); //top traffic country

            $table->unsignedBigInteger('top_country_traffic')->nullable();

            $table->foreignId('secondary_traffic_country_id')
                ->nullable()
                ->constrained('countries_list', 'id', 'seo_stats_sec_country_fk')
                ->onDelete('cascade'); //second traffic country

            $table->unsignedBigInteger('secondary_country_traffic')->nullable();


            // similarweb
            $table->unsignedBigInteger('similarweb_global_rank')->nullable();
            $table->unsignedBigInteger('similarweb_traffic')->nullable();


            $table->tinyInteger('bounce_rate')->nullable();
            $table->decimal('avg_time_on_site', 7, 2)->nullable();
            $table->decimal('pages_per_visit', 7, 2)->default(1);
            $table->boolean('is_small_site')->default(false);

            $table->json('similarweb_top_countries')->nullable();
            $table->json('similarweb_traffic_sources_percentage')->nullable();
            $table->json('similarweb_traffic_last_three_months')->nullable();



            // semrush
            $table->unsignedTinyInteger('semrush_authority_score')->default(0);


            // moz
            $table->unsignedTinyInteger('moz_domain_authority')->default(0);
            $table->tinyInteger('moz_spam_score')->default(0); //moz+bz


            // sichtbarkeits
            $table->integer('sistrix_index')->default(0);


            // google
            $table->unsignedBigInteger('index_size')->nullable();
            $table->boolean('in_google_news')->nullable(); //future


            // calculated scores - for future
            $table->tinyInteger('quality_score')->nullable();
            $table->tinyInteger('value_for_money')->nullable();


            // Meta
            $table->timestamps();



            // -------------------------------------------------------
            // Discontinued
            // $table->string('similarweb_category', 100)->nullable();
            // $table->json('similarweb_traffic_source_share')->nullable();
            // $table->unsignedBigInteger('traffic_average')->default(0);

        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_website_seo_stats');
    }
};
