<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('website_categories', function (Blueprint $table) {

            $table->id();
            $table->string('category', 100);
            $table->boolean('parent')->default(1); //parent or child category
            $table->integer('parent_category_id')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('website_categories');
    }
};
