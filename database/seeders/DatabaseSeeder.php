<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Enums\Role;
use App\Models\User;
use App\Models\Outreach;
use Illuminate\Support\Carbon;
use Illuminate\Database\Seeder;
use App\Models\MarketplaceWebsite;
use Illuminate\Support\Facades\App;
use App\Models\MarketplaceAdminWebsite;
use App\Models\MarketplaceWebsiteSeoStat;
use App\Models\Topic;

// use Database\Seeders\CountriesTableSeeder;
// use Database\Seeders\LanguagesTableSeeder;
// use Database\Seeders\CategoriesTableSeeder;

class DatabaseSeeder extends Seeder
{


    /**
     * Seed the application's database.
     * Active:
     * - Countries list: composer package
     * - Languages List: self
     */
    public function run(): void
    {

        $totalWebsitesForOutreach = 100;
        $totalMarketplaceWebsites = 300;


        $this->call([
            LanguagTableSeeder::class,
            CategoryTableSeeder::class,
            CountriesTableSeeder::class,
            UserSeeder::class,
        ]);

        if (!App::environment('production')) {

            // MarketplaceWebsite::factory(1000)->create();
            // MarketplaceWebsiteSeoStat::factory(1000)->create();


            MarketplaceWebsite::factory()
                ->count($totalMarketplaceWebsites)
                ->has(MarketplaceWebsiteSeoStat::factory(), 'seoStats')
                ->create();


            $this->call([
                OrderSeeder::class,
            ]);


            //  Outreach website seeder

            Topic::factory()->count(100)->create();

            $websites = MarketplaceAdminWebsite::doesntHave('outreach')
                ->inRandomOrder()
                ->take($totalWebsitesForOutreach)
                ->get();

            $outreachUsers = User::where('role', Role::Outreach->value)
                ->pluck('id')
                ->toArray();



            foreach ($websites as $website) {

                $status = 'inprogress';
                if ($website->publisher_user_id != 0 && $website->active) {
                    $status = 'onboarded';
                } elseif (($website->publisher_user_id > 0 && !$website->active) || ($website->publisher_user_id == 0 && !$website->active)) {
                    $status = fake()->randomElement([
                        'inprogress',
                        'rejected'
                    ]);
                }

                $createdAt = Carbon::now()->subDays(rand(30, 60));
                $onboardedAt = null;
                $rejectedAt = null;

                if ($status === 'onboarded') {
                    $onboardedAt = (clone $createdAt)->addDays(rand(1, 30));
                }
                if ($status === 'rejected') {
                    $rejectedAt = (clone $createdAt)->addDays(rand(1, 30));
                }

                Outreach::factory()->create([
                    'marketplace_website_id' => $website->id,
                    'status' => $status,
                    'user_id' => fake()->randomElement($outreachUsers),
                    'onboarded_at' => $onboardedAt,
                    'rejected_at' => $rejectedAt,
                    'created_at' => $createdAt,
                    'updated_at' => now(),
                ]);
            }
        } else {
            $this->command->warn('Skipped Seeder in production environment.');
        }

        // Create Empty Account for Outreach Testing
        $yesterday = Carbon::now()->subDay(); // Get yesterday's date
        User::create([
            "name"              => "Muqadas Outreach Testing Account",
            "email"             => "<EMAIL>",
            "password"          => bcrypt("password"),
            "role"              => Role::Outreach->value,
            "email_verified_at" => $yesterday,
        ]);
    }
}
