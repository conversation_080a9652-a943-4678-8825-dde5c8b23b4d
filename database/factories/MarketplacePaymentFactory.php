<?php

namespace Database\Factories;

use Domain\Payment\Enum\PaymentStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MarketplacePayment>
 */
class MarketplacePaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id'                  => User::inRandomOrder()->first()->id ?? User::factory(),
            'external_transaction_id' => 'pi_' . $this->faker->regexify('[A-Za-z0-9]{24}'),
            'payment_amount'           => $this->faker->numberBetween(50, 5000),
            'status'                   => $this->faker->randomElement(PaymentStatus::cases())->value,
            'internal_comment'         => $this->faker->sentence(),
        ];
    }
}
