<?php

namespace Database\Factories;

use App\Enums\MarketplaceOrderStatus;
use App\Models\MarketplaceOrder;
use App\Models\MarketplacePayment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MarketplaceOrder>
 */
class MarketplaceOrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MarketplaceOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $ourCost = $this->faker->numberBetween(50, 4500);
        $pricePaid = $ourCost * 1.6;


        return [
            // 'user_id' => User::inRandomOrder()->first()->id ?? User::factory(),
            // 'our_cost' => $ourCost,
            // 'price_paid' => $pricePaid,
            // 'payment_id' => MarketplacePayment::factory(['payment_amount' => $pricePaid]),
            'items_in_orders' => $this->faker->numberBetween(1, 5),
            'advertiser_note' => $this->faker->sentence(),
            'internal_note' => $this->faker->sentence(),
            'status' => MarketplaceOrderStatus::PENDING->value, //$this->faker->randomElement(MarketplaceOrderStatus::values()),
            'created_at' => $this->faker->dateTimeBetween('-3 months', '-15 days'),
            'updated_at' =>  $this->faker->dateTimeBetween('-15 days', 'now'), // or also random if needed
        ];
    }
}
