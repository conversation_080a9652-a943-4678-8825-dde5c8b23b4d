<?php

namespace Database\Factories;

use App\Models\MarketplaceWebsite;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class MarketplaceWebsiteSeoStatFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            "marketplace_website_id"                => MarketplaceWebsite::factory(), //rand(1, 20),
            "ahref_global_rank"                     => rand(1000, 200000),
            "ahref_domain_rank"                     => rand(1, 100),
            "ahref_organic_traffic"                 => rand(10000, 2000000),
            "reffering_domains_count"               => rand(100, 20000),
            "total_backlinks_count"                 => rand(1000, 200000),
            "outgoing_links_count"                  => rand(100, 2000),
            "semrush_authority_score"               => rand(1, 100),
            "similarweb_global_rank"                => rand(10000, 2000000),
            "similarweb_traffic"                    => rand(10000, 10000000),
            "top_traffic_country_id"                => rand(1, 20),
            "top_country_traffic"                   => rand(10000, 20000),
            "secondary_traffic_country_id"          => rand(1, 20),
            "secondary_country_traffic"             => rand(1, 5000),
            // "similarweb_category" => rand(1,20),
            // "similarweb_top_country_id" => rand(1,200),
            "similarweb_top_countries"              => '[
                                                            {
                                                                "id": 228,
                                                                "code": "TR",
                                                                "share": 100
                                                            }
                                                        ]',
            "similarweb_traffic_sources_percentage" => '{
                                                            "Mail": 0.11,
                                                            "Direct": 32.85,
                                                            "Search": 58.48,
                                                            "Social": 3.58,
                                                            "Referrals": 4.1,
                                                            "Paid Referrals": 0.83
                                                        }',
            "similarweb_traffic_last_three_months"  => '{
                                                            "2024-06-01": 192174,
                                                            "2024-07-01": 233072,
                                                            "2024-08-01": 132120
                                                        }',
            "bounce_rate"                           => rand(1, 100),
            "avg_time_on_site"                      => rand(1, 100),
            "pages_per_visit"                       => rand(1, 10),
            // "similarweb_traffic_top_five_countries" => null,
            // "similarweb_traffic_last_three_months" => null,
            // "similarweb_traffic_source_share" => null,
            "index_size"                            => rand(10, 1000),
            // "in_google_news" => 1,
            "quality_score"                         => rand(1, 100),
            "value_for_money"                       => rand(1, 100),
            "moz_spam_score"                            => rand(1, 100),
            // "traffic_average" => 0,
        ];
    }

    public function withWebsite(MarketplaceWebsite $website): static
    {
        // return $this->state([
        //     'marketplace_website_id' => $website->id,
        // ]);

        return $this->state(function (array $attributes) use ($website) {
            // If a record already exists, update it
            $existing = $website->seoStats()->first();

            if ($existing) {
                $existing->update($attributes); // Update instead of creating new
                // Return empty state so factory doesn't try to recreate
                return [];
            }

            return [
                'marketplace_website_id' => $website->id,
            ];
        });
    }
}
