<?php

namespace Database\Factories;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MarketplaceWebsite>
 */
class MarketplaceWebsiteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // 50% chance to assign a publisher
        $assignPublisher = $this->faker->boolean(50);

        // Assign a random publisher user or set to 0
        $publisherUserId = $assignPublisher
            ? (User::where('role', Role::Publisher->value)->inRandomOrder()->first()?->id ?? 0)
            : 0;

        // Active status: 1 only if publisher assigned
        $isActive = $publisherUserId !== 0 ? 1 : 0;

        return [
            'website_domain' => strtolower($this->faker->word()) . '-' . uniqid() . '.com',
            'site_language_id' => rand(1, 10),
            'main_category_id' => rand(1, 10),
            'domain_registration_date' => now(),
            'site_title' => $this->faker->words(4, true),
            'site_description' => $this->faker->sentence(),
            'turn_around_time_in_days' => rand(10, 30),

            'guest_post_price' => rand(50, 300),
            'link_insert_price' => rand(61, 300),
            'casino_post_price' => rand(100, 500),
            'adult_post_price' => rand(100, 500),
            'finance_post_price' => rand(100, 300),
            'dating_post_price' => rand(100, 300),
            'cbd_post_price' => rand(100, 300),
            'crypto_post_price' => rand(100, 300),

            'example_post_url' => $this->faker->url(),

            'publisher_user_id' => $publisherUserId,
            'active' => $isActive,
        ];
    }
}
