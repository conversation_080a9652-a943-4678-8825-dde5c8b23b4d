<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceCartItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MarketplaceCartItem>
 */
class MarketplaceCartItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MarketplaceCartItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'marketplace_website_id' => MarketplaceWebsite::factory(),
            'niche' => $this->faker->randomElement(['general', 'casino', 'adult', 'crypto', 'dating', 'cbd']),
            'content_writing' => $this->faker->boolean(),
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }
}
