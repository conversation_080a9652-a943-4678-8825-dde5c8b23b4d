<?php

namespace Database\Factories;

use App\Models\MarketplaceOrder;
use App\Models\MarketplaceWebsite;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MarketplaceSingleOrderItem>
 */
class MarketplaceSingleOrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $website = MarketplaceWebsite::inRandomOrder()->where('active', 1)->first() ?? MarketplaceWebsite::factory();
        $order = MarketplaceOrder::inRandomOrder()->first()->id ?? MarketplaceOrder::factory();
        $turnAroundTimeInDays = $website->turn_around_time_in_days + 2;
        $niche = $this->faker->randomElement(['general', 'casino', 'adult', 'crypto', 'dating', 'cbd']);
        $pricePaid = $website->nichePrice($niche);
        $pricePaidOriginal = $website->nichePriceOriginal($niche);

        return [
            'order_id' => $order,
            'marketplace_website_id' => $website->id,
            'price_paid' => $pricePaid,
            'publisher_payment_paid' => $pricePaidOriginal,
            'is_content_provided_by_customer' => $this->faker->boolean(),
            'niche' => $niche,
            // 'published_post_url' => $this->faker->url(),
            //'published_post_url' => '',
            'delivery_date' => $this->faker->date(),
            'estimated_publication_date' => now()->addDays($turnAroundTimeInDays),
        ];
    }
}
