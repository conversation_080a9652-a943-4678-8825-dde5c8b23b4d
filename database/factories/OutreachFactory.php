<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Support\Carbon;
use App\Models\MarketplaceAdminWebsite;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Outreach>
 */
class OutreachFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // $user = User::where('role', 'outreach')->inRandomOrder()->first();

        // // $website = MarketplaceAdminWebsite::inRandomOrder()->first();
        // $website = MarketplaceAdminWebsite::doesntHave('outreach')
        //     ->inRandomOrder()
        //     ->first();

        // if (!$website) {
        //     throw new \Exception('No available websites left to assign outreach.');
        // }

      

        // Status Logic
        // $status = 'inprogress';
        // if ($website->publisher_user_id > 0 && $website->active) {
        //     $status = 'onboarded';
        // } elseif ($website->publisher_user_id > 0 && !$website->active) {
        //     $status = fake()->randomElement([
        //         'inprogress',
        //         'rejected'
        //     ]);
        // } elseif ($website->publisher_user_id == 0 && !$website->active) {
        //     $status = fake()->randomElement([
        //         'inprogress',
        //         'rejected'
        //     ]);
        // } else {
        //     $status = 'inprogress';
        // }


        // if ($website->publisher_user_id > 0 && $website->active) {
        //     $status = fake()->randomElement(['onboarded', 'inprogress', 'rejected']);
        // } elseif ($website->publisher_user_id == 0 && !$website->active) {
        //     $status = 'rejected';
        // } else {
        //     $status = 'inprogress';
        // }

        // $createdAt = Carbon::now()->subDays(rand(30, 60));
        // $onboardedAt = null;
        // $rejectedAt = null;

        // if ($status === 'onboarded') {
        //     $onboardedAt = (clone $createdAt)->addDays(rand(1, 30));
        // }
        // if ($status === 'rejected') {
        //     $rejectedAt = (clone $createdAt)->addDays(rand(1, 30));
        // }

        // return [
        //     // 'marketplace_website_id' => $website->id,
        //     // 'user_id' => $user->id,
        //     // 'status' => $status,
        //     // 'onboarded_at' => $onboardedAt,
        //     // 'rejected_at' => $rejectedAt,
        //     // 'created_at' => $createdAt,
        //     // 'updated_at' => now(),
        // ];
        return [];
    }
}
