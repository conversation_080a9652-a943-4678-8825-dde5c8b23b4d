{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": "./resources/js", "paths": {"@/*": ["./*"]}, "allowSyntheticDefaultImports": true, "useDefineForClassFields": true, "noImplicitAny": false, "noImplicitThis": false, "strictNullChecks": false}, "include": ["resources/js/**/*.ts", "resources/js/**/*.d.ts"], "exclude": ["node_modules"]}