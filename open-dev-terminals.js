/**
 * Development Environment Setup Script
 * 
 * This script opens multiple terminal windows for different development services:
 * 1. Queue Worker: Handles general background jobs
 * 2. Frontend Dev: Runs the Vite development server
 * 3. Mailhog: Local email testing service
 * 4. Queue Worker Seo Stats: Handles SEO-related background jobs
 * 5. Queue Worker Orders: Handles order-related background jobs
 * 6. Stripe Webhook: Listens for Stripe events and forwards them to local webhook
 * 
 * Usage: npm run dev:all
 */

import { exec } from 'child_process';
import os from 'os';
import path from 'path';
import { fileURLToPath } from 'url';

// Display startup message
console.log('Local Development Environment Powered by PressBear');
console.log('\n🚀 Starting Development Environment...\n');
console.log('Opening terminal windows for the following services:');
console.log('1. Queue Worker: Handles general background jobs');
console.log('2. Frontend Dev: Runs the Vite development server');
console.log('3. Mailhog: Local email testing service');
console.log('4. Queue Worker Seo Stats: Handles SEO-related background jobs');
console.log('5. Queue Worker Orders: Handles order-related background jobs');
console.log('6. Stripe Webhook: Listens for Stripe events\n');
console.log('Please wait while the terminals are being opened...\n');

// Get current directory of the script
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Escape directory path for shells
const workingDir = __dirname.replace(/\\/g, '/'); // handles Windows paths too

function openTerminal(command, title = '') {
    const platform = os.platform();

    if (platform === 'win32') {
        exec(`start "${title}" cmd /k "cd /d ${workingDir} && ${command}"`);
    } else if (platform === 'darwin') {
        exec(`osascript -e 'tell app "Terminal" to do script "cd ${workingDir} && ${command}"'`);
    } else if (platform === 'linux') {
        exec(`gnome-terminal -- bash -c "cd '${workingDir}' && ${command}; exec bash"`);
    } else {
        console.error('Unsupported platform:', platform);
    }
}

// Commands to run from the project directory
openTerminal('php artisan queue:work', 'Queue Worker');
openTerminal('npm run dev', 'Frontend Dev');
openTerminal('mailhog', 'Mailhog');
openTerminal('php artisan queue:work --queue=seostats', 'Queue Worker Seo Stats');
openTerminal('php artisan queue:work --queue=orders', 'Queue Worker Orders');
openTerminal('stripe listen --forward-to https://pressbear.test/webhooks/stripe', 'Stripe Webhook');