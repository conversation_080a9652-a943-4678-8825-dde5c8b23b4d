#!/bin/bash

# Production deployment script for the application

# Exit on error
set -e

# Install/update Composer dependencies with production optimizations
composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

# Install/update NPM dependencies
npm install --production

# Build assets for production
npm run build

# Run database migrations  if needed be careful
#php artisan migrate --force

# Optimize the application for production
php artisan optimize:clear

# Restart the queue worker
php artisan queue:restart

echo "Production deployment completed successfully!" 