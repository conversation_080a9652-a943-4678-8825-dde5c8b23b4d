<?php

namespace App\Jobs\DataTransfer;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\MarketplaceOrder;
use Illuminate\Support\Facades\DB;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\MarketplacePayment;
use App\Models\User;
use App\Enums\OrderItemStates;
use App\Enums\MarketplaceOrderStatus;
use App\Enums\Role;

class Order implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private int $batchSize, private int $offset) {}

    public function handle()
    {
        // -----------------------
        // Get the orders from the old database
        // -----------------------
        DB::beginTransaction();
        try {


            $columns = [ // Order columns
                'marketplace_orders.id as order_id',
                'marketplace_orders.user_id as order_user_id',
                'marketplace_orders.payment_id as order_payment_id',
                'marketplace_orders.price_paid as order_price_paid',
                'marketplace_orders.our_cost as order_our_cost',
                'marketplace_orders.items_in_orders as order_items_in_orders',
                'marketplace_orders.advertiser_note as order_advertiser_note',
                'marketplace_orders.internal_note as order_internal_note',
                'marketplace_orders.order_completed as order_completed',
                'marketplace_orders.created_at as order_created_at',
                'marketplace_orders.updated_at as order_updated_at',

                // Payment columns
                'payments.id as payment_id',
                'payments.user_id as payment_user_id',
                'payments.payment_amount as payment_amount',
                'payments.status as payment_status',
                'payments.stripe_payment_intent_id as stripe_payment_intent_id',
                'payments.created_at as payment_created_at',
                'payments.updated_at as payment_updated_at',

                // Order items columns
                'order_items.id as order_item_id',
                'order_items.marketplace_website_id as order_item_marketplace_website_id',
                'order_items.price_paid as order_item_price_paid',
                'order_items.target_url as order_item_target_url',
                'order_items.anchor_text as order_item_anchor_text',
                'order_items.customer_comments as order_item_customer_comments',
                'order_items.is_content_provided_by_customer as order_item_is_content_provided_by_customer',
                'order_items.content_topic as order_item_content_topic',
                'order_items.content_url as order_item_content_url',
                'order_items.niche as order_item_niche',
                'order_items.content_writer as order_item_content_writer',
                'order_items.published_post_url as order_item_published_post_url',
                'order_items.publisher_comments as order_item_publisher_comments',
                'order_items.delivery_date as order_item_delivery_date',
                'order_items.internal_note as order_item_internal_note',
                'order_items.payment_proof as order_item_payment_proof',
                'order_items.order_current_status as order_item_order_current_status',
                'order_items.order_started as order_item_order_started',
                'order_items.publisher_payment_paid as order_item_publisher_payment_paid',
                'order_items.created_at as order_item_created_at',
                'order_items.updated_at as order_item_updated_at',


                // User columns
                'users.id as user_id',
                'users.name as user_name',
                'users.email as user_email',
                'users.password as user_password',
                'users.email_verified_at as user_email_verified_at',
                'users.role as user_role',
                'users.last_login_ip as user_last_login_ip',
                'users.company as user_company',
                'users.address as user_address',
                'users.city as user_city',
                'users.postal_code as user_postal_code',
                'users.country_id as user_country_id',
                'users.active as user_active',
                'users.created_at as user_created_at',
                'users.updated_at as user_updated_at',
            ];


            $orders = DB::connection('old_db')->table('marketplace_orders')
                ->join('marketplace_payments as payments', 'marketplace_orders.payment_id', '=', 'payments.id')
                ->join('marketplace_single_order_items as order_items', 'marketplace_orders.id', '=', 'order_items.order_id')
                ->join('marketplace_websites as websites', 'order_items.marketplace_website_id', '=', 'websites.id')
                ->join('users', 'marketplace_orders.user_id', '=', 'users.id')
                ->select(
                    $columns
                )
                ->offset($this->offset)
                ->limit($this->batchSize)
                ->get();

            // -----------------------
            // Transfer the orders to the new database
            // -----------------------
            foreach ($orders as $orderData) {


                // Create or find the user
                $user = User::firstOrCreate([
                    'email' => $orderData->user_email
                ], [
                    'name' => $orderData->user_name,
                    'email' => $orderData->user_email,
                    'password' => $orderData->user_password,
                    'email_verified_at' => $orderData->user_email_verified_at,
                    'role' => ($orderData->user_role == 'buyer') ? Role::Advertiser->value : $orderData->user_role,
                    'last_login_ip' => $orderData->user_last_login_ip,
                    'company' => $orderData->user_company,
                    'address' => $orderData->user_address,
                    'city' => $orderData->user_city,
                    'postal_code' => $orderData->user_postal_code,
                    'country_id' => $orderData->user_country_id,
                    'active' => $orderData->user_active,
                    'created_at' => $orderData->user_created_at,
                    'updated_at' => $orderData->user_updated_at,
                ]);

                // Create or find the payment
                $payment = MarketplacePayment::firstOrCreate([
                    'user_id' => $user->id,
                    'external_transaction_id' => $orderData->stripe_payment_intent_id,
                ], [
                    'user_id' => $user->id,
                    'payment_amount' => $orderData->payment_amount,
                    'status' => $orderData->payment_status,
                    'external_transaction_id' => $orderData->stripe_payment_intent_id,
                    'created_at' => $orderData->payment_created_at,
                    'updated_at' => $orderData->payment_updated_at,
                ]);

                // Create or find the order
                $order = MarketplaceOrder::firstOrCreate([
                    'user_id' => $user->id,
                    'payment_id' => $payment->id,
                ], [
                    'user_id' => $user->id,
                    'payment_id' => $payment->id,
                    'price_paid' => $orderData->order_price_paid,
                    'our_cost' => $orderData->order_our_cost,
                    'items_in_orders' => $orderData->order_items_in_orders,
                    'advertiser_note' => $orderData->order_advertiser_note,
                    'internal_note' => $orderData->order_internal_note,
                    'status' => $orderData->order_completed ? MarketplaceOrderStatus::COMPLETED->value : MarketplaceOrderStatus::PENDING->value,
                    'created_at' => $orderData->order_created_at,
                    'updated_at' => $orderData->order_updated_at,
                ]);



                // Create or find the order items
                MarketplaceSingleOrderItem::firstOrCreate([
                    'order_id' => $order->id,
                    'marketplace_website_id' => $orderData->order_item_marketplace_website_id,
                ], [
                    'marketplace_website_id' => $orderData->order_item_marketplace_website_id,
                    'order_id' => $order->id,
                    'price_paid' => $orderData->order_item_price_paid,
                    'target_url' => $orderData->order_item_target_url,
                    'anchor_text' => $orderData->order_item_anchor_text,
                    'customer_comments' => $orderData->order_item_customer_comments,
                    'is_content_provided_by_customer' => $orderData->order_item_is_content_provided_by_customer,
                    'content_topic' => $orderData->order_item_content_topic,
                    'content_url' => $orderData->order_item_content_url,
                    'niche' => $orderData->order_item_niche,
                    'content_writer' => $orderData->order_item_content_writer,
                    'published_post_url' => $orderData->order_item_published_post_url,
                    'publisher_comments' => $orderData->order_item_publisher_comments,
                    'delivery_date' => $orderData->order_item_delivery_date,
                    'internal_note' => $orderData->order_item_internal_note,
                    'payment_proof' => $orderData->order_item_payment_proof,
                    'state' => $orderData->order_item_order_current_status == 'completed' ? OrderItemStates::OrderItemCompleted->value : OrderItemStates::RequirementsPending->value,
                    'order_started' => $orderData->order_item_order_started,
                    'publisher_payment_paid' => $orderData->order_item_publisher_payment_paid,
                    'created_at' => $orderData->order_item_created_at,
                    'updated_at' => $orderData->order_item_updated_at,
                ]);
            }

            // -----------------------
            // Commit the transaction
            // -----------------------
            DB::commit();

            // -----------------------
            // Return the result
            // -----------------------
            return [
                'success' => true,
                'orders_count' => $orders->count(),
                'message' => 'Successfully transferred ' . $orders->count() . ' orders.'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
