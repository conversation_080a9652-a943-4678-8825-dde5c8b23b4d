<?php

namespace App\Jobs;

use App\Models\MarketplaceWebsite;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

/*********************************************************************
 * FETCH SEO STATS JOB - Handles SEO statistics creation for websites
 *********************************************************************
 *
 * Job class responsible for creating SEO statistics for marketplace
 * ..websites if they don't already exist.
 *
 * - Creates SEO stats record if none exists
 * - Uses queue system for background processing
 * - Implements ShouldQueue interface for async processing
 *
 * @param MarketplaceWebsite $website
 * The website model instance to create SEO stats for
 *
 * @return void
 * No return value as this is a job class
 *
 *********************************************************************/
class FetchSeoStatsForWebsite implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public MarketplaceWebsite $website)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // -----------------------
        // Check and Create SEO Stats
        // Creates SEO statistics record if none exists for the website
        if (!$this->website->seoStats) {
            $this->website->seoStats()->create();
        }

        return;
    }
}
