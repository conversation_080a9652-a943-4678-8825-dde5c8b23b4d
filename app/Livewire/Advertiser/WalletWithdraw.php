<?php

namespace App\Livewire\Advertiser;

use Livewire\Component;
use App\Models\WalletWithdrawalRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Notifications\NewWithdrawalRequest;

class WalletWithdraw extends Component
{
    public $amount = '';

    public $selectedRequest = null;

    public function mount() {}

    public function getBalanceProperty(): int
    {
        return Auth::user()->user_balance;
    }

    public function getWithdrawalRequestsProperty()
    {
        $user = Auth::user();
        if ($user) {
            return WalletWithdrawalRequest::where('user_id', $user->id)
                ->with('paymentMethod')
                ->orderBy('id', 'desc')
                ->get();
        }
    }

    public function withdraw()
    {
        $this->validate([
            'amount' => 'required|numeric|min:1|max:' . $this->balance,
        ]);


        try {
            $user = Auth::user();
            DB::beginTransaction();

            $amount = $this->amount * 100;
            // Check if user has sufficient balance
            if ($user->user_balance < $this->amount) {
                return back()->with('error', 'Insufficient balance for withdrawal.');
            }

            // Create withdrawal request
            $withdrawalRequest = WalletWithdrawalRequest::create([
                'user_id' => $user->id,
                'amount' => $amount,
                'status' => 'pending'
            ]);

            // Transfer balance to super admin wallet
            $user->wallet->transfer(super_admin_user()->wallet, $amount, [
                'withdrawal_request_id' => $withdrawalRequest->id,
                'reference' => 'Withdrawal Request',
                'type' => 'withdrawal_request',
                'message' => 'Withdrawal request submitted',
            ]);

            DB::commit();

            // Notify super admin
            super_admin_user()->notify(new NewWithdrawalRequest($withdrawalRequest));

            $this->js("toast('Withdrawal request submitted successfully', {type: 'success', position: 'bottom-center'})");
            $this->amount = '';
        } catch (\Exception $e) {
            DB::rollBack();
            $this->js("toast('Failed to process withdrawal: " . $e->getMessage() . "', {type: 'error', position: 'bottom-center'})");
        }
    }

    public function showRequestDetails($requestId)
    {
        $this->selectedRequest = WalletWithdrawalRequest::with(['paymentMethod', 'media'])
            ->find($requestId);
    }

    public function closeRequestDetails()
    {
        $this->selectedRequest = null;
    }

    public function render()
    {
        return view('livewire.advertiser.wallet-withdraw');
    }
}
