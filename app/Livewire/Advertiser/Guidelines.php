<?php

namespace App\Livewire\Advertiser;

use App\Models\AdvertiserGuideline;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class Guidelines extends Component
{
    public string $guidelines_for_writer = '';
    public string $guidelines_for_publisher = '';





    /**********************************************************************
     * TRUNCATE WORDS TO CONFIGURED LIMIT
     **********************************************************************
     * 
     * Truncates the given text to the maximum allowed number of words while
     * ...preserving line breaks and formatting. Ensures cleaner input formatting
     * ..for textarea without disrupting structure.
     * 
     * @param string $text
     * The raw input text from the textarea
     * 
     * @return string
     * Truncated version of the input text
     * 
     **********************************************************************/
    private function truncateWords($text)
    {
        $limit = config('pressbear.advertiser_guideline_words_limit');

        // Split on any whitespace except newlines
        $words = preg_split('/[^\S\r\n]+/', $text); // keep \r\n and \n intact

        if (count($words) > $limit) {
            $words = array_slice($words, 0, $limit);

            // Rebuild text while preserving original line breaks
            $pattern = '/((?:\r?\n)|[^\S\r\n]+)/'; // matches spaces or newlines
            $chunks = preg_split($pattern, $text, -1, PREG_SPLIT_DELIM_CAPTURE);

            $wordCount = 0;
            $output = '';

            foreach ($chunks as $chunk) {
                // Only count actual words, skip delimiters
                if (!preg_match($pattern, $chunk)) {
                    if ($wordCount >= $limit) break;
                    $wordCount++;
                }
                $output .= $chunk;
            }

            return trim($output);
        }

        return $text;
    }






    /**********************************************************************
     * MOUNT THE COMPONENT
     **********************************************************************
     * 
     * Loads the advertiser guidelines from the database and sets them to 
     * ...the component properties.
     * 
     **********************************************************************/
    public function mount()
    {
        $advertiserGuidelines = Auth::user()->advertiserGuidelines;

        if ($advertiserGuidelines) {
            $this->guidelines_for_writer = $advertiserGuidelines->guidelines_for_writer ?? '';
            $this->guidelines_for_publisher = $advertiserGuidelines->guidelines_for_publisher ?? '';
        }
    }






    /**********************************************************************
     * SAVE THE GUIDELINES
     **********************************************************************
     * 
     * Validates the input and saves the guidelines to the database.
     * 
     **********************************************************************/
    public function save()
    {

        $maxWords = config('pressbear.advertiser_guideline_words_limit');

        // Validate the input with the maximum words limit
        $this->validate([
            'guidelines_for_writer' => ['nullable', 'string', function ($attribute, $value, $fail) use ($maxWords) {
                if (str_word_count($value) > $maxWords) {
                    $fail("The $attribute may not be greater than $maxWords words.");
                }
            }],
            'guidelines_for_publisher' => ['nullable', 'string', function ($attribute, $value, $fail) use ($maxWords) {
                if (str_word_count($value) > $maxWords) {
                    $fail("The $attribute may not be greater than $maxWords words.");
                }
            }],
        ]);

        // Truncate the input to the maximum words limit
        $this->guidelines_for_writer = $this->truncateWords($this->guidelines_for_writer);
        $this->guidelines_for_publisher = $this->truncateWords($this->guidelines_for_publisher);


        // Save the guidelines to the database
        Auth::user()->advertiserGuidelines()->updateOrCreate([], [
            'guidelines_for_writer' => $this->guidelines_for_writer,
            'guidelines_for_publisher' => $this->guidelines_for_publisher,
        ]);

        session()->flash('success', 'Guidelines updated successfully.');
    }






    /**********************************************************************
     * RENDER THE COMPONENT
     **********************************************************************
     * 
     * Renders the advertiser guidelines component.
     * 
     **********************************************************************/
    public function render()
    {
        return view('livewire.advertiser.guidelines');
    }
}
