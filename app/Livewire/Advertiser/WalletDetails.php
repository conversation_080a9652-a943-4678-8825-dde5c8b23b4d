<?php

namespace App\Livewire\Advertiser;

use Livewire\Component;
use Bavix\Wallet\Models\Transaction;
use Illuminate\Support\Facades\Auth;

class WalletDetails extends Component
{
    public $id;

    public function mount($id)
    {
        $this->id = $id;
    }

    public function getTransactionProperty()
    {
        $transaction = Transaction::find($this->id);
        return $transaction;
    }



    public function render()
    {
        return view('livewire.advertiser.wallet-details');
    }
}
