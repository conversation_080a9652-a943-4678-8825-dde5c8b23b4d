<?php

namespace App\Livewire\Marketplace\Order;


use Livewire\Component;
use Livewire\WithFileUploads;
use App\Events\NewMessageSent;
use App\Services\MessageService;
use App\Events\MessageReadAtUpdated;
use Illuminate\Support\Facades\Auth;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\OrderItem;

class OrderItemChat extends Component
{
    use WithFileUploads;

    public MarketplaceSingleOrderItem $item;
    public $message = '';
    public $attachment = null;
    public $messages = [];
    public $orderItem;
    public $newMessage = '';

    protected $messageService;

    public function boot(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    public function mount()
    {
        $this->orderItem = MarketplaceSingleOrderItem::find($this->item->id);
        $this->loadMessages();
    }

    public function loadMessages()
    {
        $messages = $this->messageService->getMessages($this->orderItem);
        $this->messages = $messages->load(['sender', 'receiver'])->toArray();
    }

    public function getListeners()
    {
        return [
            "echo-private:order-item.{$this->item->id},NewMessageSent" => 'handleNewMessage',
            "echo-private:order-item.{$this->item->id},MessageReadAtUpdated" => 'handleMessageReadAtUpdated',
        ];
    }

    public function handleNewMessage($event)
    {
        $newMessage = $event['message'];
        $this->messages[] = [
            'text' => $newMessage['text'],
            'sender_id' => $newMessage['sender_id'],
            'receiver_id' => $newMessage['receiver_id'],
            'created_at' => $newMessage['created_at'],
            'is_read' => $newMessage['is_read'],
            'attachment_path' => $newMessage['attachment_path'] ?? null,
        ];
        $this->dispatch('scroll-to-bottom');
    }

    public function handleMessageReadAtUpdated($event)
    {
        $messageId = $event['message_id'];
        $readAt = $event['read_at'];

        foreach ($this->messages as &$message) {
            if ($message['id'] === $messageId) {
                $message['read_at'] = $readAt;
                $message['is_read'] = true;
                break;
            }
        }
    }

    public function sendMessage()
    {
        $this->validate([
            'message' => 'required_without:attachment|string|max:1000',
            'attachment' => 'nullable|file|max:2048|mimes:jpg,jpeg,png,doc,docx,pdf,txt',
        ]);

        $messageData = [
            'text' => $this->message,
            'order_item_id' => $this->orderItem->id,
            'sender_id' => Auth::id(),
            'receiver_id' => $this->orderItem->website->publisher->id,
        ];

        $message = $this->messageService->storeMessage($messageData, $this->attachment);
        $message->load(['sender', 'receiver']);

        $this->messages[] = [
            'id' => $message->id,
            'text' => $message->text,
            'sender_id' => $message->sender_id,
            'receiver_id' => $message->receiver_id,
            'created_at' => $message->created_at,
            'attachment_path' => $message->attachment_path,
            'sender' => [
                'id' => $message->sender->id,
                'name' => $message->sender->name,
            ],
            'receiver' => [
                'id' => $message->receiver->id,
                'name' => $message->receiver->name,
            ],
        ];

        $this->message = '';
        $this->attachment = null;

        $this->dispatch('scroll-to-bottom');
    }

    public function render()
    {
        return view('livewire.marketplace.order.order-item-chat');
    }
}
