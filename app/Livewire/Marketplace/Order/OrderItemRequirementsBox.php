<?php

namespace App\Livewire\Marketplace\Order;

use Livewire\Component;
use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Support\Facades\Notification;
use App\Notifications\States\RequirementReminderPublisher;
use Illuminate\Support\Facades\DB;

class OrderItemRequirementsBox extends Component
{
    public MarketplaceSingleOrderItem $item;
    public $showResendButton = false;
    public $showChat = false;

    public function mount()
    {
        $this->item->load('requirements');
        $this->checkResendButtonVisibility();
    }

    private function checkResendButtonVisibility()
    {
        $last24Hours = now()->subHours(24);
        $publisher = $this->item->website->publisher;
        if ($publisher) {
            $latestNotification = DB::table('notifications')
                ->where('notifiable_type', get_class($publisher))
                ->where('notifiable_id', $publisher->id)
                ->where('type', RequirementReminderPublisher::class)
                ->orderByDesc('created_at')
                ->first();

            if (!$latestNotification) {
                $this->showResendButton = $this->item->updated_at->lte($last24Hours);
            } else {
                $this->showResendButton = \Carbon\Carbon::parse($latestNotification->created_at)->lte($last24Hours);
            }
        }
    }

    public function resendEmail()
    {
        if ($this->item->state_name === 'requirement-awaiting-publisher-approval') {
            Notification::send($this->item->website->publisher, new RequirementReminderPublisher($this->item));
            $this->js("toast('Reminder email sent successfully', {type: 'success', position: 'bottom-center'})");
            $this->checkResendButtonVisibility(); // Update button visibility after sending
        }
    }

    public function openChat()
    {
        $this->showChat = true;
    }

    public function closeChat()
    {
        $this->showChat = false;
    }

    public function render()
    {
        return view('livewire.marketplace.order.order-item-requirements-box');
    }
}
