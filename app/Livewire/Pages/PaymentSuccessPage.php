<?php

namespace App\Livewire\Pages;

use Livewire\Component;
use Illuminate\Http\Request;
use App\Jobs\ProcessMarketplacePayment;

class PaymentSuccessPage extends Component
{
    public $orderId;
    public $payment_intent;
    public $rules = [
        'payment_intent' => 'required|string|min:5|max:500',
    ];

    public function mount(Request $request)
    {
        $this->orderId = null;
        $this->payment_intent = $request->payment_intent;

        // Send Job For Order Processing
        ProcessMarketplacePayment::dispatch($this->payment_intent, auth()->user()->id)->delay(config('pressbear.order_processing_delay'))->onQueue('orders');
    }

    public function render()
    {
        return view('livewire.pages.payment-success-page');
    }
}
