<?php


namespace App\Livewire\Admin;

use App\Models\MarketplaceOrder;
use App\Models\User;
use Illuminate\Http\Request;

use App\Http\Controllers\MarketplaceOrderController;

use Livewire\Component;

// use Illuminate\Support\Facades\Mail;
// use App\Mail\OrderDelivered;

use Illuminate\Support\Facades\Notification;
use App\Notifications\orderCompleted;


class SingleOrder extends Component
{

    public $order;
    public $updateMessage;
    public $orderStatus;


    /******************************
     * Initial Mount
     *******************************/
    public function mount(MarketplaceOrder $order)
    {
        $this->order  = $order;
        $this->orderStatus = $this->order->orderStatus();
    }



    /******************************
     * Mark Order As completed
     ******************************/
    public function markCompleted()
    {
        // Check Order Status
        $orderStatus = $this->order->orderStatus();

        if ($orderStatus['delivered']['status'] == true) {

            // 1. Update DB
            $this->order->update(['order_completed' => 1]);

            // 2. Ui Message
            $this->updateMessage = "Order Marked Completed";

            // 3. Send Email Notification
            Notification::send($this->order->user, new orderCompleted($this->order));
        } else {
            $this->updateMessage = "Can't Mark This As Completed As there are Pending items";
        }
    }



    /******************************
     * Render
    /******************************/
    public function render()
    {
        return view('livewire.admin.orders.single-order')->layout('layouts.admin');;
    }
}
