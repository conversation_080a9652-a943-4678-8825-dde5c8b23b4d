<?php

namespace App\Livewire\Admin;

use Livewire\Component;

use App\Models\MarketplaceAdminWebsite;
use App\Http\Controllers\MarketplaceMetaDataController;


class AdminSiteAdd extends Component
{

    public $languages;
    public $categories;
    public $site_language_id;

    public $guest_post_price;
    public $link_insert_price;
    public $casino_post_price;
    public $adult_post_price;
    public $finance_post_price;
    public $dating_post_price;
    public $cbd_post_price;
    public $crypto_post_price;

    public $site_requirements;
    public $example_post_url;
    public $article_validity_in_months;
    public $turn_around_time_in_days;

    public $indexed_article;
    public $link_relation;
    public $sponsorship_label;
    public $homepage_visible;

    public $contact_email;
    public $active;




    /*****************************
     * Initial Mount
    ******************************/
    public function mount(){

        $this->languages    = MarketplaceMetaDataController::LanguagesList();
        $this->categories   = MarketplaceMetaDataController::categoriesList();
    }



    /*****************************
     * RENDER
    ******************************/
    public function render()
    {
        return view('livewire.admin.websites.admin-site-add')->layout('layouts.admin');
    }




    /*****************************
     * Language & Category Data
    ******************************/
    public function update()
    {
        MarketplaceAdminWebsite::create([
            'website_domain'     => $this->website_domain,
            'site_language_id'   => $this->site_language_id,

            'guest_post_price'   => $this->guest_post_price,
            'link_insert_price'  => $this->link_insert_price,
            'casino_post_price'  => $this->casino_post_price,
            'adult_post_price'   => $this->adult_post_price,
            'finance_post_price' => $this->finance_post_price,
            'dating_post_price'  => $this->dating_post_price,
            'cbd_post_price'     => $this->cbd_post_price,
            'crypto_post_price'  => $this->crypto_post_price,

            'site_requirements'  => $this->site_requirements,
            'example_post_url'   => $this->example_post_url,
            'article_validity_in_months' => $this->article_validity_in_months,
            'turn_around_time_in_days'  => $this->turn_around_time_in_days,

            'indexed_article'    => $this->indexed_article,
            'link_relation'      => $this->link_relation,
            'sponsorship_label'  => $this->sponsorship_label,
            'homepage_visible'   => $this->homepage_visible,

            'contact_email'      => $this->contact_email,
            'active'             => $this->active
           
        ]);

        session()->flash('status', 'Website added successfully.');
        
        return $this->redirectRoute('admin-websites');
        
    }


}
