<?php

namespace App\Livewire\Admin;

use App\Models\MarketplaceAdminWebsite;

use Livewire\Component;
use Livewire\WithPagination;


class adminWebsites extends Component
{

    use WithPagination;

    public $search = '';


    /******************************
     * Websites List
     ******************************/
    public function getWebsites()
    {

        $search = $this->search;


        if (!empty($search)) {

            $searchTerm = '%' . $search . '%';
            $websites   = MarketplaceAdminWebsite::where('website_domain', 'like', $searchTerm)->paginate(10);
        } else {
            $websites = MarketplaceAdminWebsite::paginate(10);
        }

        return $websites;
    }



    /******************************
     * Render
     ******************************/
    public function render()
    {
        $websites = $this->getWebsites();
        return view('livewire.admin.websites.listing', ['websites' => $websites])
            ->layout('layouts.admin');
    }
}
