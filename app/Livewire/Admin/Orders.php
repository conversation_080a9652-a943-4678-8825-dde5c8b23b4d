<?php

namespace App\Livewire\Admin;
use App\Models\MarketplaceOrder;

use Livewire\Component;
use Livewire\WithPagination;


class Orders extends Component
{

    use WithPagination;


    public $search = '';


    /******************************
     * Orders List
    ******************************/
    public function getOrders(){

        $search = $this->search;

        if(!empty($search)){

            $searchTerm = '%'.$search.'%';
            $orders = Marketplaceorder::where('id', 'like' ,$searchTerm)
                                        ->orwhere('user_id', 'like' ,$searchTerm)
                                        ->with('user')
                                        ->latest()
                                        ->paginate(10);
            
        }else{
             $orders = Marketplaceorder::with('user')->latest()->paginate(10);
        }
       
        return $orders;
    }




    /******************************
     * Render
    ******************************/
    public function render()
    {
        $orders = $this->getOrders();

        return view('livewire.admin.orders.orders-list', ['orders' => $orders])
                ->layout('layouts.admin');
    }
}
