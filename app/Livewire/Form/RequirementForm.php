<?php

namespace App\Livewire\Form;

use App\Enums\OrderItemStates;
use Livewire\Component;
use Livewire\Attributes\Lazy;
use Illuminate\Support\Facades\DB;
use Domain\Order\Modules\StatesTraits;
use App\Models\MarketplaceSingleOrderItem;

#[Lazy]
class RequirementForm extends Component
{
    use StatesTraits;

    public $article_topic;

    public $anchor_text;

    public $advertiser_url;

    public $requirement_comments;

    public MarketplaceSingleOrderItem $orderItem;

    public $disable_fields = false;

    public $update_existing_requirement = false;

    public function mount()
    {


        // If we're in revision state, pre-fill the form with existing requirements
        if ($this->orderItem->requirements) {
            $this->article_topic = $this->orderItem->requirements->article_topic;
            $this->anchor_text = $this->orderItem->requirements->anchor_text;
            $this->advertiser_url = $this->orderItem->requirements->advertiser_url;
            $this->requirement_comments = $this->orderItem->requirements->requirement_comments;
        }
        // If we're in revision state, pre-fill the form with existing requirements
        if ($this->orderItem->state_name == 'requirement-revision-requested') {
            $this->update_existing_requirement = true;
        }
    }

    public function rules()
    {
        return [
            'article_topic' => 'required|string|max:255',
            'anchor_text' => 'required|string|max:255',
            'advertiser_url' => 'required|url|max:255',
            'requirement_comments' => 'nullable|string|max:1000',
        ];
    }

    public function submit()
    {
        $validated = $this->validate();


        try {
            // Update Requirements and Transition State
            $this->orderItem = $this->transitionState(OrderItemStates::RequirementAwaitingPublisherApproval, $this->orderItem, $validated);

            $this->js("toast('Requirement submitted successfully.', {type: 'success', position: 'bottom-center'})");
            // Reset form
            $this->reset(['article_topic', 'anchor_text', 'advertiser_url', 'requirement_comments']);
            // Refresh Page
            $this->js("setTimeout(function() { window.location.reload(); }, 1000);");
        } catch (\Exception $e) {
            $this->js("toast('Requirement submission failed: ' + " . json_encode($e->getMessage()) . ", {type: 'error', position: 'bottom-center'})");
            return;
        }
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div class="flex items-center justify-center min-h-[200px]">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
        HTML;
    }

    public function render()
    {
        return view('livewire.form.requirement-form');
    }
}
