<?php

namespace App\Livewire\Form;

use App\Models\OrderItemContent;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;
use App\Models\MarketplaceSingleOrderItem;
use App\States\OrderItem\ContentAssignedToWriter;
use Domain\Order\Modules\StatesTraits;
use App\Enums\OrderItemStates;

/*********************************************************************
 * CONTENT FORM COMPONENT
 *********************************************************************
 *
 * Handles content creation and updates for marketplace order items.
 * Manages both team-written and customer-provided content.
 *
 * Features:
 * - Content source selection (team/customer)
 * - Media attachment handling
 * - Content state transitions
 * - Form validation
 *
 * @property MarketplaceSingleOrderItem $orderItem
 * @property string|null $title
 * @property string|null $content_body
 * @property string|null $content_url
 * @property string|null $comments
 * @property int $content_source
 * @property array $media_ids
 * @property bool $disable_fields
 *
 *********************************************************************/
class ContentForm extends Component
{
    use WithFileUploads, StatesTraits;

    public MarketplaceSingleOrderItem $orderItem;

    public string|null $title = null;
    public string|null $content_body = null;
    public string|null $content_url = null;
    public string|null $comments = null;
    public int $content_source = 0; // 0 = team, 1 = customer
    public array $media_ids = [];

    public bool $disable_fields = false;


    /*********************************************************************
     * MOUNT COMPONENT
     *********************************************************************
     *
     * Initialize component with order item data.
     * Sets initial values for content fields if content exists.
     *
     * @return void
     *
     *********************************************************************/
    public function mount()
    {
        $this->content_source = $this->orderItem->is_content_provided_by_customer;

        if ($this->orderItem->content) {
            $this->title = $this->orderItem->content->title;
            $this->content_body = $this->orderItem->content->content_body;
            $this->content_url = $this->orderItem->content->content_url;
            $this->comments = $this->orderItem->content->comments;
            $this->media_ids = $this->orderItem->content->media()->pluck('id')->toArray();
        }
    }


    /*********************************************************************
     * VALIDATION RULES
     *********************************************************************
     *
     * Define validation rules for content form fields.
     *
     * @return array Validation rules
     *
     *********************************************************************/
    public function rules(): array
    {
        return [
            'content_source' => 'required|integer|in:0,1',
            'title' => 'required_if:content_source,1|string|max:255',
            'content_body' => 'required_if:content_source,1|string',
            'content_url' => 'nullable|url|max:255',
            'comments' => 'nullable|string|max:1000|required_if:content_source,1',
            'media_ids' => 'array',
            'media_ids.*' => 'exists:media,id',
        ];
    }


    /*********************************************************************
     * HANDLE MEDIA UPLOAD
     *********************************************************************
     *
     * Add newly uploaded media to the media_ids array.
     *
     * @param int $mediaId ID of the uploaded media
     * @return void
     *
     *********************************************************************/
    #[On('media-uploaded')]
    public function handleMediaUploaded($mediaId)
    {
        if (!in_array($mediaId, $this->media_ids)) {
            $this->media_ids[] = $mediaId;
        }
    }


    /*********************************************************************
     * HANDLE MEDIA REMOVAL
     *********************************************************************
     *
     * Remove media from the media_ids array.
     *
     * @param int $mediaId ID of the media to remove
     * @return void
     *
     *********************************************************************/
    #[On('media-removed')]
    public function handleMediaRemoved($mediaId)
    {
        $this->media_ids = array_filter($this->media_ids, function ($id) use ($mediaId) {
            return $id != $mediaId;
        });
    }


    /*********************************************************************
     * REMOVE MEDIA
     *********************************************************************
     *
     * Remove media and update content files array.
     *
     * @param int $mediaId ID of the media to remove
     * @return void
     *
     *********************************************************************/
    public function MediaRemove($mediaId)
    {
        // -----------------------
        // Remove Media ID
        $this->media_ids = array_filter($this->media_ids, function ($id) use ($mediaId) {
            return $id != $mediaId;
        });

        // -----------------------
        // Update Content Files
        if ($this->orderItem->content) {
            $this->orderItem->content->update([
                'files_array' => $this->media_ids
            ]);
        }
    }


    /*********************************************************************
     * WRITE CONTENT
     *********************************************************************
     *
     * Create initial content entry for team-written content.
     * Transitions order item to ContentAssignedToWriter state.
     *
     * @return void
     *
     *********************************************************************/
    public function writeContent()
    {
        // -----------------------
        // Create Content Entry
        $validated = [
            'title' => '',
            'content_body' => '',
            'content_url' => '',
            'media_ids' => [],
            'comments' => '',
            'content_source' => $this->content_source == 1 ? 'customer' : 'team',
            'writer_id' => 1,
        ];

        $this->transitionState(OrderItemStates::ContentAssignedToWriter, $this->orderItem, $validated);

        $this->notifySuccessAndReload('Content submitted successfully.');
    }


    /*********************************************************************
     * UPDATE CONTENT
     *********************************************************************
     *
     * Update or create content with validation.
     * Handles media attachment and state transitions.
     *
     * @return void
     *
     *********************************************************************/
    public function updateContent()
    {

        $validated = $this->validate();

        try {

            $this->transitionState(OrderItemStates::ContentAwaitingPublisherApproval, $this->orderItem, $validated);

            // -----------------------
            // Reset Form
            $this->reset(['title', 'content_body', 'content_url', 'comments', 'media_ids']);
            $this->notifySuccessAndReload('Content submitted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update content: order item id ' . $this->orderItem->id . ' ' . $e->getMessage());
            $this->js(expression: "toast('Content submission failed', {type: 'error', position: 'bottom-center'})");
        }
    }


    /*********************************************************************
     * SUBMIT CONTENT
     *********************************************************************
     *
     * Handle content submission based on source.
     * Routes to appropriate handler based on content source.
     *
     * @return void
     *
     *********************************************************************/
    public function submit()
    {
        if ($this->content_source == 0) {
            $this->writeContent();
        } else {
            $this->updateContent();
        }
    }



    /*********************************************************************
     * NOTIFY SUCCESS AND RELOAD
     *********************************************************************
     *
     * Show success toast and reload page.
     *
     * @param string $message Success message to display
     * @return void
     *
     *********************************************************************/
    private function notifySuccessAndReload(string $message): void
    {
        $this->js("toast('{$message}', {type: 'success', position: 'bottom-center'})");
        $this->js("setTimeout(() => window.location.reload(), 1000);");
    }


    /*********************************************************************
     * RENDER COMPONENT
     *********************************************************************
     *
     * Render the content form view.
     *
     * @return \Illuminate\View\View
     *
     *********************************************************************/
    public function render()
    {
        return view('livewire.form.content-form');
    }
}
