<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;


/**************************************
 * HANDLE EXCEPTIONS
/*************************************/
class Handler extends ExceptionHandler
{


    /*********************************************************************
     * PREVENT SENSITIVE DATA LEAK ON PUBLIC END
     **********************************************************************
     *
     * The list of the inputs that are never flashed to the session on 
     * validation exceptions.
     *
     * @var array<int, string>
     * 
    /*********************************************************************/
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];





    /*********************************************************************
     * REGISTER
     **********************************************************************
     *
     * Register the exception handling callbacks for the application:
     * - Sentry: send exceptions to sentry for external logging.
     * 
    /*********************************************************************/
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
        });
    }





    /*********************************************************************
     * HANDLE RENDERING
     **********************************************************************
     *
     * Handle intertia rendering.
     * 
    /*********************************************************************/
    public function render($request, Throwable $e): Response
    {
        if ($e instanceof AuthenticationException) {
            // Inertia request — force full-page reload to login
            if ($request->header('X-Inertia')) {
                return response('', 409)
                    ->header('X-Inertia-Location', route('login'));
            }

            // Normal redirect for non-Inertia requests
            return redirect()->guest(route('login'));
        }

        return parent::render($request, $e);
    }
}
