<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
// use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Mail\Mailable;
use App\Mail\OrderNeedAttentionMail; //mail class


class orderNeedAttention extends Notification implements ShouldQueue
{
    use Queueable;

    public $order;

    /*************************************************
     * Order Data
    /************************************************/
    public function __construct($orderData)
    {
        $this->order = $orderData;
    }


    /*************************************************
     * Notification Types
     *
     * @return array<int, string>
     **************************************************/
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }


    /*************************************************
     * Mailable Class
     **************************************************/
    public function toMail(object $notifiable): Mailable
    {
        return (new OrderNeedAttentionMail($this->order))
            ->to($notifiable->email);
    }


    /***************************************************
     * TO DB FOR Ui.
     ****************************************************/
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Need Attention',
            'message' => 'One of your order item need attention. Please review. #order id: ' . $this->order->id,
            'url' => url(route('advertiser.order-details', $this->order->id)),
        ];
    }
}
