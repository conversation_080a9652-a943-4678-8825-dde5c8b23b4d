<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use App\Models\WalletWithdrawalRequest;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class NewWithdrawalRequest extends Notification implements ShouldQueue
{
    use Queueable;

    protected $withdrawalRequest;

    public function __construct(WalletWithdrawalRequest $withdrawalRequest)
    {
        $this->withdrawalRequest = $withdrawalRequest;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Withdrawal Request')
            ->line('A new withdrawal request has been submitted.')
            ->line('Amount: $' . number_format($this->withdrawalRequest->amount, 2))
            ->line('User: ' . $this->withdrawalRequest->user->name)
            ->line('Payment Method: ' . ucfirst($this->withdrawalRequest->paymentMethod->key))
            ->action('View Request', route('admin.withdrawals.show', $this->withdrawalRequest->id))
            ->line('Please review this request as soon as possible.');
    }


    public function toArray($notifiable): array
    {
        return [
            'withdrawal_request_id' => $this->withdrawalRequest->id,
            'user_id' => $this->withdrawalRequest->user_id,
            'amount' => $this->withdrawalRequest->amount,
            'payment_method' => ucfirst($this->withdrawalRequest->paymentMethod->key),
        ];
    }
}
