<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
// use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

// Mail
use Illuminate\Mail\Mailable;
use App\Mail\OrderDelivered;



class orderCompleted extends Notification implements ShouldQueue
{
    use Queueable;

    public $order;

    /*************************************************
     * Order Data
    /************************************************/
    public function __construct($orderData)
    {
        $this->order = $orderData;
    }


    /*************************************************
     * Notification Types
     *
     * @return array<int, string>
     **************************************************/
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }


    /*************************************************
     * Mailable Class
     **************************************************/
    public function toMail(object $notifiable): Mailable
    {
        return (new OrderDelivered($this->order))
            ->to($notifiable->email);
    }


    /***************************************************
     * TO DB FOR Ui.
     ****************************************************/
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Order Completed',
            'message' => 'Your order #' . $this->order->id . ' is marked completed and ready for review.',
            'url' => url(route('advertiser.order-details', $this->order->id)),
        ];
    }
}
