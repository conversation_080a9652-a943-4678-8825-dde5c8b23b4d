<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\View;

class DomainVerificationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $domain;
    protected $verificationCode;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $domain, string $verificationCode)
    {
        $this->domain = $domain;
        $this->verificationCode = $verificationCode;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $data = [
            'domain' => $this->domain,
            'verification_code' => $this->verificationCode,
        ];

        return (new MailMessage)
            ->subject('Domain Verification Required - ' . $this->domain)
            ->markdown('emails.domain-verification', ['data' => $data]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'domain' => $this->domain,
            'verification_code' => $this->verificationCode,
        ];
    }
}
