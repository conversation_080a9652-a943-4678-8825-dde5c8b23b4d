<?php

namespace App\Filters;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;


/*********************************************************************
 * ADMIN DASHBOARD: WEBSITE FILTERS APPLY
 *********************************************************************
 * Applies various filters to website queries in the admin dashboard.
 * Handles filtering by: Active, Search terms
/*********************************************************************/
class AdminWebsiteFilters
{
    
    protected array $filters;

    // construct
    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }


    /*********************************************************************
     * APPLY FILTERS
     *********************************************************************
     *
     * Apply filters to the query.
     * 
     * 1. Active / Inactive Status
     * 2. Search Term (domain, site title, publisher name/email)
     * 3. Verified / Unverified
     * 4. Outreach Status
     * 5. Outreach Date Filters (preset or custom)
     * 
     * @param Builder $query
     * @param User $user
     * @return Builder
     * 
     *********************************************************************/
    public function apply(Builder $query, $user): Builder
    {
        // -----------------------
        // Active Status Filter
        // Filters websites by their active/inactive status
        if (isset($this->filters['status']) && in_array($this->filters['status'], ['0', '1'])) {
            $query->where('active', $this->filters['status']);
        }


        // -----------------------
        // Search Term Filter
        // Searches across website domain, title, and publisher details
        if (!empty($this->filters['searchTerm'])) {
            $searchTerm = $this->filters['searchTerm'];

            $query->where(function ($q) use ($searchTerm) {
                $q->where('website_domain', 'like', "%{$searchTerm}%")
                    ->orWhere('site_title', 'like', "%{$searchTerm}%")
                    ->orWhereHas('publisher', function ($q) use ($searchTerm) {
                        $q->where('name', 'like', "%{$searchTerm}%")
                            ->orWhere('email', 'like', "%{$searchTerm}%");
                    });
            });
        }


        // -----------------------
        // Verification Status Filter
        // Filters based on publisher assignment status
        if (isset($this->filters['verified'])) {
            $verified = $this->filters['verified'];

            $query->where(function ($q) use ($verified) {
                if ($verified == '1') {
                    $q->where('publisher_user_id', '!=', 0);
                } else {
                    $q->where('publisher_user_id', 0);
                }
            });
        }


        // -----------------------
        // Outreach Status Filter
        // Filters websites by their outreach status
        if (!empty($this->filters['outreach_status'])) {
            $outreachStatus = $this->filters['outreach_status'];

            if ($outreachStatus === 'unassigned') {
                $query->where('publisher_user_id', 0);
            } elseif (in_array($outreachStatus, ['inprogress', 'onboarded', 'rejected'])) {
                $query->whereHas('outreach', fn($q) => $q->where('status', $outreachStatus));
            }
        }


        // -----------------------
        // Outreach Date Range Filter
        // Applies date filters based on outreach status and selected range
        if (!empty($this->filters['outreach_status']) && in_array($this->filters['outreach_status'], ['inprogress', 'onboarded', 'rejected'])) {
            $dateColumn = match ($this->filters['outreach_status']) {
                'inprogress' => 'created_at',
                'onboarded' => 'onboarded_at',
                'rejected' => 'rejected_at',
                default => 'created_at',
            };

            // Handle preset date ranges
            if (!empty($this->filters['preset_range'] ?? null) && ($this->filters['preset_range'] ?? null) !== 'show_all') {
                $query->whereHas('outreach', function ($q) use ($dateColumn) {
                    match ($this->filters['preset_range']) {
                        'today' => $q->whereDate($dateColumn, today()),
                        'yesterday' => $q->whereDate($dateColumn, today()->subDay()),
                        'last_7_days' => $q->whereBetween($dateColumn, [now()->subDays(6)->startOfDay(), now()->endOfDay()]),
                        'last_30_days' => $q->whereBetween($dateColumn, [now()->subDays(29)->startOfDay(), now()->endOfDay()]),
                        'last_90_days' => $q->whereBetween($dateColumn, [now()->subDays(89)->startOfDay(), now()->endOfDay()]),
                        'last_12_months' => $q->whereBetween($dateColumn, [now()->subMonths(12)->startOfDay(), now()->endOfDay()]),
                        default => null,
                    };
                });
            }

            // Handle custom date range
            if (($this->filters['preset_range'] ?? null) === 'custom') {
                $query->whereHas('outreach', function ($q) use ($dateColumn) {
                    if (!empty($this->filters['start_date'])) {
                        $q->whereDate($dateColumn, '>=', $this->filters['start_date']);
                    }
                    if (!empty($this->filters['end_date'])) {
                        $q->whereDate($dateColumn, '<=', $this->filters['end_date']);
                    }
                });
            }
        }

        return $query;
    }
}
