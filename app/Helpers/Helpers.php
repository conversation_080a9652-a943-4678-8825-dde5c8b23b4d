<?php

use App\Enums\Role;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

// use m_ as prefix for custom functions

// Calculate number of years to show in frontend
if (!function_exists('m_calculateAge')) {
	function m_calculateAge($date)
	{
		$givenDate = new DateTime($date);
		$currentDate = new DateTime();
		$interval = $givenDate->diff($currentDate);

		$years = $interval->y;

		if ($years < 1) {
			$age = $interval->m . ' months';
		} else {
			$age = $years . ' years';
		}

		return $age;
	}
}

// for showing time spent on site stats
if (!function_exists('m_timeDisplay')) {
	function m_timeDisplay($seconds)
	{

		if (!$seconds && (gettype($seconds) != 'integer')) {
			return false;
		}

		if ($seconds <= 60) {
			return $seconds . 's';
		}

		$minutes = floor($seconds / 60);
		$remainingSeconds = $seconds % 60;

		return $minutes . '.' . ($remainingSeconds > 0 ? $remainingSeconds : '') . 'm';
	}
}




if (!function_exists('menu_active_link')) {
	function menu_active_link(array $routeNames, $activeClass = 'activeMenuLink')
	{
		return in_array(request()->route()->getName(), $routeNames) ? $activeClass : '';
	}
}


if (!function_exists('applyDateFilter')) {
	function applyDateFilter($query, $status, $filters)
	{
		$dateColumn = match ($status) {
			'inprogress' => 'created_at',
			'onboarded'  => 'onboarded_at',
			'rejected'   => 'rejected_at',
			default      => 'created_at',
		};

		if ($filters['preset_range'] && $filters['preset_range'] !== 'custom') {
			return match ($filters['preset_range']) {
				'today'          => $query->whereDate($dateColumn, today()),
				'yesterday'      => $query->whereDate($dateColumn, today()->subDay()),
				'last_7_days'    => $query->whereBetween($dateColumn, [now()->subDays(6)->startOfDay(), now()->endOfDay()]),
				'last_30_days'   => $query->whereBetween($dateColumn, [now()->subDays(29)->startOfDay(), now()->endOfDay()]),
				'last_90_days'   => $query->whereBetween($dateColumn, [now()->subDays(89)->startOfDay(), now()->endOfDay()]),
				'last_12_months' => $query->whereBetween($dateColumn, [now()->subMonths(12)->startOfDay(), now()->endOfDay()]),
				default          => $query,
			};
		}

		if ($filters['preset_range'] === 'custom') {
			if ($filters['start_date']) {
				$query->whereDate($dateColumn, '>=', $filters['start_date']);
			}
			if ($filters['end_date']) {
				$query->whereDate($dateColumn, '<=', $filters['end_date']);
			}
		}

		return $query;
	}
}
