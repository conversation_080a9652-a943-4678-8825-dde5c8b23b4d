<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;

/*********************************************************************
 * REQUIREMENTS PENDING STATE
 **********************************************************************
 *
 * This state represents an order item where requirements are pending
 * submission. It handles the validation and transition logic for this
 * specific state.
 *
 *********************************************************************/
class RequirementsPending extends OrderItemState
{
    public static $name = OrderItemStates::RequirementsPending->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Requirements Pending';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void {}

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            RequirementAwaitingPublisherApproval::class,
        ];
    }
}
