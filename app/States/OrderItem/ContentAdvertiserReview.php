<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use Illuminate\Support\Facades\URL;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * CONTENT ADVERTISER REVIEW STATE
 **********************************************************************
 *
 * This state represents an order item where content is being reviewed
 * by the advertiser. It handles the validation and transition logic
 * for this specific state.
 *
 *********************************************************************/
class ContentAdvertiserReview extends OrderItemState
{
    public static $name = OrderItemStates::ContentAdvertiserReview->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Content Review By Advertiser';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {

        $emailData = [];
        $emailData['customerName'] = $model->order->user->name ?? 'N/A';
        $string = 'New Content For Review';
        if ($from == OrderItemStates::ContentRevisionRequestedByAdvertiser->value) {
            $string = 'Revised Content For Review';
        }
        $emailData['emailSubject'] = $string . ' Order #' . $model->order->id;
        $emailData['statusLabel'] = $string . ' Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['content'] = $model->content;

        // Generate a signed URL that expires in 7 days
        $emailData['reviewUrl'] = URL::signedRoute('advertiser.orders.details.signed', [
            'id' => $model->id
        ], now()->addDays(7));

        // Send Notification
        $model->order->user->notify(new UserNotification(
            'New Content For Review Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.content-advertiser-review'
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            ContentAwaitingPublisherApproval::class,
            ContentRevisionRequestedByAdvertiser::class,
        ];
    }
}
