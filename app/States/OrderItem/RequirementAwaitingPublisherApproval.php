<?php

namespace App\States\OrderItem;

use Illuminate\Support\Facades\URL;
use App\Notifications\User\UserNotification;
use App\Enums\OrderItemStates;

/*********************************************************************
 * REQUIREMENT AWAITING PUBLISHER APPROVAL STATE
 **********************************************************************
 *
 * This state represents an order item that has submitted requirements
 * and is waiting for publisher approval. It handles the validation
 * and transition logic for this specific state.
 *
 *********************************************************************/
class RequirementAwaitingPublisherApproval extends OrderItemState
{

    public static $name = OrderItemStates::RequirementAwaitingPublisherApproval->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Requirements Awaiting Approval';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state. Prepares and sends an email notification to the
     * publisher about the new pitch request.
     *
     * @param MarketplaceSingleOrderItem $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->website->publisher->name ?? 'N/A';
        $string = 'New Pitch Request for';
        if ($from == OrderItemStates::RequirementRevisionRequested->value) {
            $string = 'Revised Pitch for';
        }
        $emailData['emailSubject'] = $string . ' Order #' . $model->order->id;
        $emailData['statusLabel'] = $string . ' Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['requirements'] = $model->requirements;

        // Generate a signed URL that expires in 7 days
        $emailData['reviewUrl'] = URL::signedRoute('publisher.orders.details.signed', [
            'id' => $model->id
        ], now()->addDays(7));

        // Send Notification
        $model->website->publisher->notify(new UserNotification(
            'New Pitch Request for Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.requirement-awaiting-publisher-approval'
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            OrderItemCancelled::class,
            RequirementRevisionRequested::class,
            ContentPending::class,
        ];
    }
}
