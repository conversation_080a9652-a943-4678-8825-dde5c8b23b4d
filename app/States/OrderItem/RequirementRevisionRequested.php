<?php

namespace App\States\OrderItem;

use Illuminate\Support\Facades\Mail;
use App\Notifications\User\UserNotification;
use App\Mail\States\RequirementRevisionRequestedMail;
use App\Enums\OrderItemStates;

/*********************************************************************
 * REQUIREMENT REVISION REQUESTED STATE
 **********************************************************************
 *
 * This state represents an order item where revisions to the requirements
 * have been requested. It handles the validation and transition logic
 * for this specific state.
 *
 *********************************************************************/
class RequirementRevisionRequested extends OrderItemState
{
    public static $name = OrderItemStates::RequirementRevisionRequested->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Requirement Revision Requested';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->order->user->name ?? 'N/A';
        $emailData['emailSubject'] = 'Revision Requested for Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Revision Requested for Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['requirements_advertiser_revision_reason'] = $model->requirements->advertiser_revision_reason ?? 'N/A';

        // Send Notification
        $model->order->user->notify(new UserNotification(
            'Revision Requested for Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.requirement-revision-requested'
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            RequirementAwaitingPublisherApproval::class,
        ];
    }
}
