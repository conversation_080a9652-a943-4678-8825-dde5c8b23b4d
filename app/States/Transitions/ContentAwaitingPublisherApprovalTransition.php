<?php

namespace App\States\Transitions;

use Spatie\ModelStates\Transition;
use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Validation\ValidationException;
use App\States\OrderItem\ContentAwaitingPublisherApproval;

class ContentAwaitingPublisherApprovalTransition extends Transition
{
    private MarketplaceSingleOrderItem $orderItem;
    private string $message;
    public function __construct(MarketplaceSingleOrderItem $orderItem)
    {
        $this->orderItem = $orderItem;
        $this->message = 'Content is missing. Please submit content first.';
    }

    /*********************************************************************
     * VALIDATE
     *********************************************************************/
    public function validate()
    {
        if (!$this->orderItem->content && !$this->orderItem->is_content_provided_by_customer) {
            throw ValidationException::withMessages([
                'message' => [$this->message]
            ]);
        }
    }

    /*********************************************************************
     * HANDLE
     *********************************************************************/
    public function handle()
    {
        // Validate the order item
        $this->validate();

        // Transition to the next state
        $this->orderItem->state = ContentAwaitingPublisherApproval::class;
        $this->orderItem->save();

        return $this->orderItem;
    }
}
