<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserTransitionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'user' => $this->wallet->holder,
            'order_id' => $this->meta['order_id'] ?? 'N/A',
            'amount' => $this->amount,
            'reference' => $this->meta['reference'] ?? 'N/A',
            'status' => $this->confirmed ? 'Approved' : 'Pending',
            'date' => $this->created_at->format('Y-m-d H:i:s'),
            'debit' => $this->type === 'withdraw' ? number_format($this->amount / 100, 2) : '',
            'credit' => $this->type === 'deposit' ? number_format($this->amount / 100, 2) : '',
        ];
    }
}
