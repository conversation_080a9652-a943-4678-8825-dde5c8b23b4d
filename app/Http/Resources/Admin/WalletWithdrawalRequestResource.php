<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WalletWithdrawalRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => $this->user,
            'amount' => number_format($this->amount / 100, 2),
            'status' => $this->status,
            'created_at' => $this->created_at_formatted,
            'updated_at' => $this->updated_at_formatted,
        ];
    }
}
