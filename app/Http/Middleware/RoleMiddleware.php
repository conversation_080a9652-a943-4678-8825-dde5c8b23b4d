<?php

namespace App\Http\Middleware;

use Closure;
use App\Enums\Role;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{

    /*************************************************************
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): 
     * (\Symfony\Component\HttpFoundation\Response)  $next
    /*************************************************************/
    public function handle(Request $request, Closure $next, ...$roles): Response
    {

        // active user instance
        $user = auth('web')->user();


        //************************************************************  
        // Check if the logged-in
        //************************************************************
        if (!$user) {
            //admin route so no hint is given if you access the page
            //without right roles
            abort(404, 'Not Found');
        }

        //************************************************************  
        // Check if a specific role is required for this route
        //************************************************************
        if (!empty($roles) && ! in_array($user->role, $roles)) {
            abort(404, 'Not Found');
        }


        //************************************************************  
        // Super Admin Redundancy Check: matching hard coded email
        //************************************************************
        if ($user->role === Role::SuperAdmin->value) {
            if ($user->email !== config('pressbear.super_admin_email')) {
                abort(404, 'Not Found');
            }
        }

        return $next($request);
    }
}





// =====================================================
// LEGACY POOR CODE
// Removed by: Hamza, 11 jun
// =====================================================

// This is messed up, not acceptable and I don't get it either‼️
// if ($request->is("pb-admin*")) {

//     //************************************************************  
//     // Allow publisher limited access to create and edit their websites via admin routes
//     //************************************************************

//     if (
//         $user->role === Role::Publisher->value &&
//         (
//             // Allow access to pb-admin/websites (index, store) and pb-admin/websites/{id} (edit, update)
//             $request->is('pb-admin/websites*') ||
//             $request->is('pb-admin/website*')
//         ) &&
//         in_array($request->method(), ['POST', 'PUT', 'PATCH'])
//     ) {
//         return $next($request);
//     }
// }







//************************************************************  
// Admin has full access to `/pb-admin/*`
//************************************************************

// if ($request->is('pb-admin*')) {

//     if (super_admin_access()) {
//         return $next($request); //  Admin has full access
//     }

//     if ($user->role === Role::Admin->value) {
//         return $next($request); // Admin (non-super) has full access to pb-admin
//     }





    //************************************************************  
    // COMBINED ROUTES FOR SALES AND FINANCE
    //************************************************************

    // if (
    //     (
    //         in_array($user->role, [Role::Sales->value, Role::Finance->value]) &&
    //         (
    //             $request->is('*pb-admin') ||
    //             $request->is('*pb-admin/order*') ||
    //             $request->is('*pb-admin/orders*')
    //         )
    //     )
    // ) {
    //     return $next($request);
    // }


    //************************************************************  
    // OUTREACH ROUTES
    //************************************************************

    // if (
    //     (
    //         in_array($user->role, [Role::Outreach->value]) &&
    //         (

    //             (
    //                 $request->is('*pb-admin') ||
    //                 $request->is('*pb-admin/website*') ||
    //                 $request->is('*pb-admin/websites/*') ||
    //                 $request->is('*pb-admin/outreach*')
    //             ) &&
    //             //  Block import routes
    //             ! $request->is('*pb-admin/websites/import') &&
    //             ! $request->is('*pb-admin/websites/import-csv')
    //         )
    //     )
    // ) {
    //     return $next($request);
    // }



    //************************************************************  
    // FINANCE ROUTES
    //************************************************************

    // if ($user->role === Role::Finance->value && $request->is('*pb-admin/payments', '*pb-admin/payments/*')) {
    //     return $next($request);
    // }

    //************************************************************  
    // ABORT ON OTHER ROLES
    //************************************************************

    // Block any other roles from `/pb-admin/*`

//     abort(404); // Show 404 instead of 403 for unauthorized publisher access

// }


//************************************************************  
// Publisher Routes
//************************************************************

// if ($request->is('publisher*')) {

//     if ($user->role === Role::Publisher->value) {
//         return $next($request); //  publisher has full access
//     }

//     abort(404); // Show 404 instead of 403 for unauthorized publisher access
// }
