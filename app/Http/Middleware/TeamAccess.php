<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Enums\Role;


/*********************************************************************
 * PLATFORM BACKEND TEAM ONLY ACCESS - (Default check)
 *********************************************************************/
class TeamAccess
{

    /*************************************************************
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): 
     * (\Symfony\Component\HttpFoundation\Response)  $next
    /*************************************************************/
    public function handle(Request $request, Closure $next): Response
    {

        // active user instance
        $user = auth('web')->user();

        // check
        if ($user && $this->isOurTeamMember($user)) {
            return $next($request);
        }

        //admin route so no hint is given if you access the page
        //without right roles
        abort(404, 'Not Found');
    }



    //************************************************************  
    // Check if user is have one of our team member role
    //************************************************************
    private function isOurTeamMember($user)
    {

        // These are pressbear backend team role
        return in_array($user->role,   [
            Role::SuperAdmin->value,
            Role::Admin->value,
            Role::Sales->value,
            Role::Finance->value,
            Role::Outreach->value,
            Role::Writer->value,
            Role::Publisher->value,
        ]);
    }
}
