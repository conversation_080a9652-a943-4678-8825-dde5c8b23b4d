<?php

namespace App\Http\Requests\Admin;


use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;


class AdminOrdersIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'preset_range' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'searchTerm' => 'nullable|string',
            'orderStatus' => 'nullable|string',
            'sortField' => 'nullable|string',
            'sortOrder' => 'nullable|string',
            'perPage' => 'nullable|integer|min:1|max:100',
        ];
    }
}
