<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

/*********************************************************************
 * ADMIN OUTREACH STATS REQUEST
 **********************************************************************
 *
 * Validates and structures incoming filter parameters for outreach
 * statistics dashboard. Handles date range validation, preset options,
 * and pagination parameters.
 *
 *********************************************************************/
class AdminOutreachStatsRequest extends FormRequest
{
    /*********************************************************************
     * AUTHORIZE REQUEST
     **********************************************************************
     *
     * Determine if the user is authorized to make this request.
     * Currently allows all authenticated admin users.
     *
     * @return bool
     *
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }




    /*********************************************************************
     * VALIDATION RULES
     **********************************************************************
     *
     * Define validation rules for outreach stats filtering parameters.
     * Supports preset date ranges, custom date ranges, and pagination.
     *
     * @return array<string, mixed>
     *
     *********************************************************************/
    public function rules(): array
    {
        return [
            'preset_range' => 'nullable|string|in:show_all,today,yesterday,last_7_days,last_30_days,last_90_days,last_12_months,custom',
            'start_date' => 'nullable|date|required_if:preset_range,custom',
            'end_date' => 'nullable|date|after_or_equal:start_date|required_if:preset_range,custom',
            'page' => 'nullable|integer|min:1',
        ];
    }




    /*********************************************************************
     * CUSTOM ERROR MESSAGES
     **********************************************************************
     *
     * Provide user-friendly error messages for validation failures.
     *
     * @return array<string, string>
     *
     *********************************************************************/
    public function messages(): array
    {
        return [
            'preset_range.in' => 'Please select a valid date range option.',
            'start_date.required_if' => 'Start date is required when using custom date range.',
            'end_date.required_if' => 'End date is required when using custom date range.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'page.min' => 'Page number must be at least 1.',
        ];
    }




    /*********************************************************************
     * GET VALIDATED FILTERS
     **********************************************************************
     *
     * Returns validated and structured filter data with defaults applied.
     * Provides clean interface for domain layer consumption.
     *
     * @return array
     *
     *********************************************************************/
    public function getValidatedFilters(): array
    {
        $validated = $this->validated();
        
        return [
            'preset_range' => $validated['preset_range'] ?? 'show_all',
            'start_date' => $validated['start_date'] ?? null,
            'end_date' => $validated['end_date'] ?? null,
        ];
    }
}
