<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAssignmentRequest extends FormRequest
{
    /*********************************************************************
     * DETERMINE IF USER IS AUTHORIZED
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }


    /*********************************************************************
     * GET VALIDATION RULES
     *********************************************************************/
    public function rules(): array
    {
        return [
            'writer_id' => 'nullable|exists:users,id',
            'state' => 'required|string',
        ];
    }


    /*********************************************************************
     * GET ASSIGNMENT DATA
     **********************************************************************
     *
     * Extracts and returns the validated assignment data from the request.
     *
     * @return array
     *********************************************************************/
    public function getAssignmentData(): array
    {
        return [
            'writer_id' => $this->input('writer_id'),
            'state' => $this->input('state'),
        ];
    }
}
