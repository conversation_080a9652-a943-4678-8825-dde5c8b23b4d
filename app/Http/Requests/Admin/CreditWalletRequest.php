<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreditWalletRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'integer', 'exists:users,id'],
            'amount' => ['required', 'numeric', 'min:0.01', 'max:10000'],
            'reason' => ['required', 'string', 'min:3', 'max:500'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'Please select a user to credit.',
            'user_id.exists' => 'The selected user does not exist.',
            'amount.required' => 'Please enter an amount to credit.',
            'amount.numeric' => 'The amount must be a valid number.',
            'amount.min' => 'The minimum credit amount is $0.01.',
            'amount.max' => 'The maximum credit amount is $10,000.',
            'reason.required' => 'Please provide a reason for the credit.',
            'reason.min' => 'The reason must be at least 3 characters.',
            'reason.max' => 'The reason cannot exceed 500 characters.',
        ];
    }
}
