<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;

class UpdateOrderItemRequirementsRequest extends FormRequest
{
    /*********************************************************************
     * DETERMINE IF USER IS AUTHORIZED
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }


    /*********************************************************************
     * GET VALIDATION RULES
     *********************************************************************/
    public function rules(): array
    {
        return [
            'requirements' => 'required|array',
        ];
    }


    /*********************************************************************
     * GET VALIDATED REQUIREMENTS DATA
     **********************************************************************
     *
     * Validates the nested requirements array structure
     * - with proper validation rules for requirement fields.
     *
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     *********************************************************************/
    public function getRequirementsData(): array
    {
        $requirements = $this->input('requirements');
        
        $validator = Validator::make($requirements, [
            'article_topic' => 'required|string|max:255',
            'anchor_text' => 'required|string|max:255',
            'advertiser_url' => 'required|url|max:255',
            'requirement_comments' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new \Illuminate\Validation\ValidationException($validator);
        }

        return $requirements;
    }
}
