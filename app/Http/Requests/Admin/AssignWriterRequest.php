<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class AssignWriterRequest extends FormRequest
{
    /*********************************************************************
     * DETERMINE IF USER IS AUTHORIZED
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }


    /*********************************************************************
     * GET VALIDATION RULES
     *********************************************************************/
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
        ];
    }


    /*********************************************************************
     * GET WRITER ID
     **********************************************************************
     *
     * Extracts and returns the validated writer ID from the request.
     *
     * @return int
     *********************************************************************/
    public function getWriterId(): int
    {
        return $this->input('user_id');
    }
}
