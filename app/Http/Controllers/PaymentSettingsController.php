<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\UserPaymentSetting;
use Illuminate\Support\Facades\Auth;


class PaymentSettingsController extends Controller
{
    /*********************************************************************
     * DISPLAY PAYMENT SETTINGS
     *********************************************************************
     *
     * Displays the list of payment settings for the currently authenticated user.
     * Also fetches a list of countries from the metadata controller.
     *
     * @return \Inertia\Response
     * The Inertia view with payment settings and countries list
     *
     *********************************************************************/
    public function index()
    {
        $settings = UserPaymentSetting::where('user_id', Auth::id())->get();

        return Inertia::render('PaymentSettings/PaymentSettings', [
            'settings' => $settings,
            'countries' => MarketplaceMetaDataController::countriesList()

        ]);
    }






    /*********************************************************************
     * STORE OR UPDATE PAYMENT SETTING
     *********************************************************************
     *
     * Validates and stores or updates a payment setting for the user.
     * If this is the user's first payment method, it is set as the default.
     *
     * @param \Illuminate\Http\Request $request
     * The incoming HTTP request containing payment setting data
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects back with a success message
     *
     *********************************************************************/
    public function store(Request $request)
    {
        $request->validate([
            'key' => 'required|in:paypal,bank,payoneer',
            'value' => 'required|array',
        ]);

        $paymentSetting = UserPaymentSetting::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'key' => $request->key,
            ],
            [
                'value' => $request->value,
            ]
        );

        // If user has no default payment method, set this one as default
        $hasDefaultPayment = UserPaymentSetting::where('user_id', Auth::id())
            ->where('is_default', true)
            ->exists();

        if (!$hasDefaultPayment) {
            $this->setDefault($paymentSetting->id);
        }

        return back()->with('success', 'Payment settings saved.');
    }





    /*********************************************************************
     * SET DEFAULT PAYMENT METHOD
     *********************************************************************
     *
     * Updates the user's payment settings to mark the specified setting
     * ...as the default and unset default for all other settings.
     *
     * @param int $id
     * The ID of the payment setting to mark as default
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects back with a success message
     *
     *********************************************************************/
    public function setDefault($id)
    {
        $setting = UserPaymentSetting::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        UserPaymentSetting::where('user_id', Auth::id())
            ->update(['is_default' => false]);

        $setting->update(['is_default' => true]);

        return back()
            ->with('success', 'Default payment method updated.');
    }






    /*********************************************************************
     * DELETE PAYMENT METHOD
     *********************************************************************
     *
     * Deletes a payment method by ID for the authenticated user.
     * Ensures the method belongs to the user before deletion.
     *
     * @param int $id
     * The ID of the payment setting to delete
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects back with a success message
     *
     *********************************************************************/
    public function destroy($id)
    {
        $setting = UserPaymentSetting::where('id', $id)
            ->where('user_id', auth('web')->id())
            ->first();

        if ($setting) {
            // Prevent deletion of default payment method
            if ($setting->is_default) {
                return back()->with('error', 'Cannot delete the default payment method. Please set another method as default first.');
            }

            $setting->delete();
            return back()->with('success', 'Payment method deleted.');
        }

        return back()->with('error', 'Payment method not found.');

        return back()->with('error', 'Payment method not found.');
    }
}
