<?php

namespace App\Http\Controllers\FilterQueries;

use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use App\Http\Controllers\FilterQueries\ActiveFiltersTrait;
use App\Http\Controllers\FilterQueries\ValidationsTrait;
use App\Http\Controllers\FilterQueries\QueriesTrait;


/*********************************************************************
 * FILTERS TRAIT - CORE FILTERING FUNCTIONALITY
 *********************************************************************
 *
 * Central trait that combines three specialized traits for handling
 * ..filtering functionality in controllers.
 *
 * This trait serves as a composition point for:
 * - ActiveFiltersTrait: Manages active filter states
 * - ValidationsTrait: Handles input validation
 * - QueriesTrait: Contains complex database queries
 *
 * Usage:
 * - Import and use this trait in controllers requiring filtering
 * - Each sub-trait handles a specific aspect of filtering
 * - Maintains separation of concerns while providing unified interface
 *
 *********************************************************************/
trait FiltersTrait
{
	use ActiveFiltersTrait, ValidationsTrait, QueriesTrait;


	/************************************************************
	 * EXPLAINATION
	 * 
	 * Using three treat for sub functionalities
	 * 
	 * 1. ActiveFilterTrait check for active filters
	 * 2. ValidationTrait validate each input according to requirements
	 * 3. QueriesTrait contain long size queries 
	 * 
	 * We use FiltersTrait in our main controller
	 ***********************************************************/
}
