<?php

namespace App\Http\Controllers\FilterQueries;

use Illuminate\Http\Request;

trait ActiveFiltersTrait
{
	/*********************************************************************
	 * GET ACTIVE FILTERS
	 *********************************************************************
	 *
	 * Analyzes request parameters to determine which filters are active
	 * and need to be applied to the query.
	 *
	 * - Checks for search, sort, and niche filters
	 * - Validates filter activation status
	 * - Returns array of active filter states
	 *
	 * @param Request $request
	 * HTTP request containing filter parameters
	 *
	 * @return array
	 * Associative array indicating which filters are active
	 *
	 *********************************************************************/
	public function getActiveFilters(Request $request): array
	{
		// -----------------------
		// Initialize Filter States
		$activeFilters = [
			'filtersApplied' => false,
			'search'        => false,
			'sort'          => false,
			'niche'         => false,
			'price'         => false,
			'dr'            => false,
			'traffic'       => false,
			'spamScore'     => false,
			'country'       => false,
			'language'      => false,
			'category'      => false
		];


		// -----------------------
		// Check Search Filter
		if (isset($request->search) && $request->search['active'] === true) {
			$activeFilters['search'] = true;
		}


		// -----------------------
		// Check Sort Filter
		if (isset($request->table) && $request->table['sort']['active'] === true) {
			$activeFilters['sort'] = true;
		}


		// -----------------------
		// Check Niche Filter
		if (isset($request->niche) && $request->niche['active'] === true) {
			$activeFilters['niche'] = true;
		}


		// -----------------------
		// Check Main Filter Status
		if (isset($request->filters) && $request->filters['filtersActive'] === true) {
			$activeFilters['filtersApplied'] = true;
		}


		// -----------------------
		// Process Individual Filters
		if ($activeFilters['filtersApplied']) {
			$activeFilters['price'] = $request->filters['price']['active'] === true;
			$activeFilters['dr'] = $request->filters['dr']['active'] === true;
			$activeFilters['traffic'] = $request->filters['traffic']['active'] === true;
			$activeFilters['spamScore'] = $request->filters['spamScore']['active'] === true;
			$activeFilters['country'] = $request->filters['country']['active'] === true;
			$activeFilters['language'] = $request->filters['language']['active'] === true;
			$activeFilters['category'] = $request->filters['category']['active'] === true;
		}

		return $activeFilters;
	}
}
