<?php

namespace App\Http\Controllers\Publisher\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemsResources extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->website->website_domain ?? 'No website',
            'topic' => $this->content_topic ?? 'No topic',
            'niche' => $this->niche ?? 'general',
            'price_paid' => $this->price_paid,
            'stateName' => $this->state_name ?? '',
            'stateLabel' => $this->state_label ?? 'Not assigned',
            'status' => $this->status,
            'delivery_date' => $this->estimated_publication_date_formatted,
            'customer_name' => $this->order->user->name,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d'),
        ];
    }
}
