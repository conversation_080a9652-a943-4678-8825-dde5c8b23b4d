<?php

namespace App\Http\Controllers\Publisher\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class OrderResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'topic' => $this->orderItems->first()?->content_topic ?? 'No topic',
            'niche' => $this->orderItems->first()?->niche ?? 'general',
            'price_paid' => $this->price_paid,
            'status' => $this->status,
            'delivery_date' => $this->orderItems->first()?->estimated_publication_date_formatted,
            'items_count' => $this->orderItems->count(),
            'customer_name' => $this->user->name,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d'),
        ];
    }
}
