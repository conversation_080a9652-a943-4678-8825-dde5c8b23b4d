<?php

namespace App\Http\Controllers;

use <PERSON>e\StripeClient;

class StripeMarketController extends Controller
{
    private $STRIPE_CLIENT;
    private $STRIPE_PUBLISHABLE_KEY;

    public function __construct()
    {
        $this->STRIPE_CLIENT = config('services.stripe.client');
        $this->STRIPE_PUBLISHABLE_KEY = config('services.stripe.publishable');
    }

    /*****************************************
     * 
     * PAYMENT INTENT CREATE STRIPE
     * 
     * @param amount
     * @return intent object or 500
     * 
     * Add meta data later to intent
    /*****************************************/
    public function stripePaymentIntent($amount, $user)
    {
        //initiate stripe
        $stripe = new StripeClient($this->STRIPE_CLIENT);

        try {
            $StripeMetadata = [
                'amount' => $amount * 100,
                'currency' => 'usd',
                'receipt_email' => $user->email,
                'metadata' => [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'user_email' => $user->email,
                    'company' => $user->company,
                    'address' => $user->address_data['address'] ?? 'N/A',
                    'city' => $user->address_data['city'] ?? 'N/A',
                    'postal_code' => $user->address_data['postal_code'] ?? 'N/A',
                    'country_id' => $user->country_id,
                ]
            ];
            // Create a PaymentIntent with amount and currency
            $paymentIntent = $stripe->paymentIntents->create($StripeMetadata);

            return $paymentIntent;
        } catch (\Exception $e) {
            http_response_code(500);
            return false;
        }
    }





    /*****************************************
     * 
     * CHECK PAYMENT STATUS
     * 
     * @param stripe intent id
     * @return intent object with data or 500
     * 
    /*****************************************/
    public function stripePaymentStatus($paymentIntent)
    {

        // initiate stripe
        $stripe = new StripeClient($this->STRIPE_CLIENT);

        // Returning after redirecting to a payment method portal.
        $paymentData = $stripe->paymentIntents->retrieve($paymentIntent);

        return $paymentData;
    }
}
