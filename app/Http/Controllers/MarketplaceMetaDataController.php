<?php

namespace App\Http\Controllers;

// use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

use App\Models\MarketplaceWebsiteLanguage;
use App\Models\MarketplaceWebsiteCategory;
use App\Models\MarketplaceWebsiteCountry;

class MarketplaceMetaDataController extends Controller
{
    /*********************************************************************
     * COUNTRIES LIST - Get All Countries From Database
     *********************************************************************
     *
     * Retrieves and caches all countries from the database for 30 days.
     * Used for populating country selection dropdowns and filters.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     * Collection of MarketplaceWebsiteCountry models
     *
     *********************************************************************/
    public static function countriesList()
    {
        // -----------------------
        // Cache Countries Data
        $countries = Cache::remember('countriesList', (60 * 60 * 24 * 30), function () {
            return MarketplaceWebsiteCountry::all();
        });

        return $countries;
    }





    /*********************************************************************
     * CATEGORIES LIST - Get All Categories From Database
     *********************************************************************
     *
     * Retrieves and caches all categories from the database for 30 days.
     * Used for populating category selection dropdowns and filters.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     * Collection of MarketplaceWebsiteCategory models
     *
     *********************************************************************/
    public static function categoriesList()
    {
        // -----------------------
        // Cache Categories Data
        $categories = Cache::remember('categoriesList', (60 * 60 * 24 * 30), function () {
            return MarketplaceWebsiteCategory::all();
        });

        return $categories;
    }





    /*********************************************************************
     * LANGUAGES LIST - Get All Languages From Database
     *********************************************************************
     *
     * Retrieves and caches all languages from the database for 30 days.
     * Used for populating language selection dropdowns and filters.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     * Collection of MarketplaceWebsiteLanguage models
     *
     *********************************************************************/
    public static function languagesList()
    {
        // -----------------------
        // Cache Languages Data
        $languages = Cache::remember('languagesList', (60 * 60 * 24 * 30), function () {
            return MarketplaceWebsiteLanguage::all();
        });

        return $languages;
    }
}
