<?php

namespace App\Http\Controllers\Admin;

use Domain\Stats\Stats;
use Inertia\Inertia;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminOutreachStatsRequest;
use Inertia\Response;

class AdminOutreachController extends Controller
{
    /**********************************************************************
     * OUTREACH PERFORMANCE STATS
     **********************************************************************
     *
     * Retrieves paginated outreach users with website status counts.
     * - Inprogress, onboarded, rejected.
     * - Optional date filters via domain layer.
     *
     * @param AdminOutreachStatsRequest $request Validated filters
     * @return Response \Inertia\\Response
     ********************************************************************
     */
    public function index(AdminOutreachStatsRequest $request): Response
    {
        // -----------------------
        // Get Validated Filters
        $filters = $request->getValidatedFilters();


        // -----------------------
        // Fetch User Stats
        $users = Stats::outreachUsers($filters);


        // -----------------------
        // Return Inertia Response
        return Inertia::render('Admin/Teams/Outreach/Index', [
            'users'   => $users,
            'filters' => $filters,
        ]);
    }


}
