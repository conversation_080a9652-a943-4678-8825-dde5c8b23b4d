<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Domain\Stats\Stats as StatsService;
use Illuminate\Http\Request;
use App\Enums\Role;
use Inertia\Inertia;


class AdminController extends Controller
{

    /*********************************************************************
     * DASHBOARD DISPLAY BASED ON USER ROLE
     **********************************************************************
     *
     * Determines the authenticated user's role and redirect the request
     * to the relevant dashboard view (Outreach or Admin).
     * 
     * Accepts optional date filtering inputs to be passed downstream:
     *  - `preset_range`: predefined ranges like today, last_7_days, etc.
     *  - `start_date` / `end_date`: custom date range filters
     * 
     * @param Request $request
     * @return \Inertia\Response
     *********************************************************************/
    public function dashboard(Request $request)
    {
        $filters = [
            'preset_range'  => $request->input('preset_range', 'show_all'),
            'start_date'    => $request->input('start_date'),
            'end_date'      => $request->input('end_date'),
        ];

        $user = auth('web')->user();

        switch ($user->role) {
            case Role::Admin->value:
            case Role::SuperAdmin->value:
                return $this->adminDashboard($filters);
            case Role::Outreach->value:
                $filters['user_id'] = $user->id;
                return $this->outreachDashboard($filters);
            case Role::Sales->value:
                return $this->salesDashboard($filters);
            default:
                abort(403, 'Unauthorized action.');
        }
    }




    /*********************************************************************
     * OUTREACH DASHBOARD METRICS
     **********************************************************************
     *
     * Computes outreach-specific metrics for the logged-in Outreach user.
     * Calculates count of websites by status (inprogress, onboarded, rejected),
     * and totals based on the authenticated user's outreach records.
     *
     * Applies dynamic date filtering using:
     *  - Predefined ranges (today, last_7_days, etc.)
     *  - Custom `start_date` and `end_date` if selected
     *
     * @param array $filters
     * @return \Inertia\Response
     *
     *********************************************************************/
    private function outreachDashboard(array $filters)
    {
        $userId = auth('web')->id();

        // Get outreach stats from the Stats domain
        $stats = StatsService::outreach($userId, $filters);

        return Inertia::render('Admin/Dashboard/Outreach/Index', [
            'filters' => $filters,
            'stats'   => $stats,
        ]);
    }




    /*********************************************************************
     * ADMIN DASHBOARD METRICS
     **********************************************************************
     *
     * Builds admin-level order stats for Admin + Super Admin.
     * Utilizes the Stats domain to get comprehensive sales metrics
     * including order counts, revenue, profit, and payment data.
     *
     * Supported filters include:
     *  - Preset ranges (today, last_30_days, etc.)
     *  - Custom date range using `start_date` and `end_date`
     *
     * @param array $filters
     * @return \Inertia\Response
     *
     *********************************************************************/
    private function adminDashboard(array $filters)
    {
        // Get sales stats from the Stats domain
        $orderStats = StatsService::sales()->getStatsForTimePeriod($filters);

        return Inertia::render('Admin/Dashboard/Index', [
            'filters' => $filters,
            'orderStats' => $orderStats,
        ]);
    }


    /*********************************************************************
     * SALES DASHBOARD METRICS
     **********************************************************************
     *
     * Builds sales-level order stats for Sales user.
     * Utilizes the Stats domain to get comprehensive sales metrics
     * including order counts, revenue, profit, and payment data.
     * 
     * @param array $filters
     * @return \Inertia\Response
     *
     *********************************************************************/
    private function salesDashboard(array $filters)
    {
        // Get sales stats from the Stats domain
        $orderStats = StatsService::sales()->getStatsForTimePeriod($filters);

        return Inertia::render('Admin/Teams/Sales/Index', [
            'filters' => $filters,
            'orderStats' => $orderStats,
        ]);
    }



    /*********************************************************************
     * ADMIN SETTINGS VIEW
     **********************************************************************
     *
     * Loads the settings page for administrators.
     * Currently serves as a static interface for future system configuration.
     * 
     * @return \Inertia\Response
     *********************************************************************/
    public function settings()
    {
        return Inertia::render('Admin/Settings/Index');
    }
}
