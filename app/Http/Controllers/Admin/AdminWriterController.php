<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Domain\Writer\AdminWriterService;
use App\Http\Requests\AdminAssignmentsRequest;

class AdminWriterController extends Controller
{
    public function __construct(
        private AdminWriterService $writerService
    ) {}

    /*********************************************************************
     * ADMIN - WRITER ASSIGNMENTS LISTING
     *********************************************************************
     * 
     * Displays a paginated list of order items assigned to writers,
     * unassigned items, or completed items.
     * 
     * Filters:
     * - Assignment status: assigned, unassigned, completed
     * - Content state: filter by order item state
     * - Search term: match writer name/email or website domain
     * 
     * Sorting & Pagination:
     * - Supports sorting by specific fields and pagination
     * 
     * @param Request $request
     * The HTTP request containing filters, sorting, and pagination params
     * 
     * @return \Inertia\Response
     * Returns Inertia page with filtered assignments
     *********************************************************************/
    public function assignments(AdminAssignmentsRequest $request)
    {
        $filters = $request->validated();

        $items = $this->writerService->getAssignments($filters);
        $stats = $this->writerService->getAssignmentStats();
        $statuses = $this->writerService->getContentStates();

        return Inertia::render('Admin/Teams/Writers/Assignments', [
            'items' => $items,
            'perPage' => $filters['perPage'] ?? 10,
            'filters' => $filters,
            'statuses' => $statuses,
            'stats' => $stats,
        ]);
    }





    /**********************************************************************
     * ADMIN - SEARCH WRITERS
     *********************************************************************
     * 
     * Performs a search query for writers by name or email.
     * 
     * - Only users with the 'writer' role are included
     * - Includes assignment count for each writer
     * - Limits results to 10, sorted by latest first
     * 
     * @param Request $request
     * The HTTP request containing the search term
     * 
     * @return \Illuminate\Http\JsonResponse
     * JSON response with writer records: id, name, email
     **********************************************************************/
    public function searchWriters(Request $request)
    {
        $search = $request->input('searchTerm', '');

        $users = User::writers()
            ->withCount('writerAssignments')
            ->when($search, function ($query) use ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->orderByDesc('id')
            ->take(10)
            ->get(['id', 'name', 'email']);

        return $users;
    }
}
