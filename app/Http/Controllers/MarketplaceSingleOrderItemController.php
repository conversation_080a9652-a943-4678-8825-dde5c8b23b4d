<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;



class MarketplaceSingleOrderItemController extends Controller
{



    /*****************************************
     * 
     * CREATE ORDER ITEMS
     * 
     * One order can have many order items
     * 
    /*****************************************/
    public function updateOrderItem(Request $request)
    {

        // Validation
        $data = $request->validate([
            'order_item_id'      => 'required|min:1|max:9999999999999',
            'is_content_provided_by_customer' => 'boolean',
            'content_url'        => 'required_if_accepted:is_content_provided_by_customer',
            'target_url'         => 'required_if:is_content_provided_by_customer,0|max:3000',
            'anchor_text'        => 'required_if:is_content_provided_by_customer,0|max:1000',
            'content_topic'      => 'required_if:is_content_provided_by_customer,0|max:500',
            'customer_comments'  => 'max:2000',
        ]);


        // get order item or fail if it doesn't exist
        $orderItem = Auth::user()->orderItems()->findorFail($data['order_item_id']);


        // check if update allowed at this stage
        $updateAllowed = $orderItem->customerCanUpdateRequirements();
        if ($updateAllowed === false) {
            return false;
        }

        // update order item
        $update = $orderItem->update([
            'is_content_provided_by_customer' => $data['is_content_provided_by_customer'],
            'content_url'        => $data['content_url'],
            'target_url'         => $data['target_url'],
            'anchor_text'        => $data['anchor_text'],
            'content_topic'      => $data['content_topic'],
            'customer_comments'  => $data['customer_comments'],
            'state' => 'inprogress',
        ]);


        if ($update) {
            return true;
        } else {
            return false;
        }
    }
}
