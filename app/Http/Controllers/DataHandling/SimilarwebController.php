<?php

namespace App\Http\Controllers\DataHandling;

ini_set('max_execution_time', 5000);

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;


use App\Http\Controllers\DataHandling\DataImportTrait;
use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceWebsiteSeoStat;
use App\Models\MarketplaceWebsiteCategory;
use App\Models\MarketplaceWebsiteCountry;
use App\Models\Keyword;



class SimilarwebController extends Controller
{
    //import common functionality
    use DataImportTrait;


    // Similarweb Api URL
    // public $api = 'https://softsynk.com/curlResponse.php?domain=';
    public $api = 'https://data.similarweb.com/api/v1/data?domain=';


    /*****************************************
     * SIMILARWEB TESTING
     *****************************************/
    public function test()
    {
        return $this->similarwebExtensionDomainDataFetch('bestforandroid.com');
    }



    /*****************************************
     * CURL: GET DATA FROM SIMILARWEB API
     *  
     * @param $website string
     * @return $response
     *****************************************/
    public function similarwebExtensionDomainDataFetch(string $websiteDomain)
    {
        $api    = $this->api;
        $apiURL = $api . $websiteDomain;

        $request['headers'] = $this->similarWebHeaders();

        return $this->curlRequest($apiURL, $request);
    }



    /*****************************************
     * SIMILARWEB IMPORT START
     * 
     * 1. Get site from db with seo stats
     *****************************************/
    public function similarWebDataImport()
    {

        // Website where global rank == null, sort by oldest updated
        $websites = MarketplaceWebsite::oldest('updated_at')
            ->with(['seoStats'])
            ->limit(10)
            ->get();
        $data = [];


        // Loop over all websites
        foreach ($websites as $website) {

            // get domain
            $domain = $this->getDomainFromUrl($website->website_domain);

            // update timestamps to not be prcocessed again
            $website->touch();

            // if false then skip
            if ($domain === false) {
                continue;
            }

            // API
            $APIData    = $this->similarwebExtensionDomainDataFetch($domain);

            //laravel method based
            //$APIData = $this->getWebsiteData($domain); 

            // if no data then skip and move to next
            if (!$APIData) {
                continue;
            } else {
                $APIData    = json_decode($APIData, true);
                $data[]     = $this->updateWebsitesData($website, $APIData);
            }
        }

        return $data;
    }




    /*****************************************
     * Update Website Data Rows
     * 
     * @param $website
     * @param $data ARRAY
     ******************************************/
    public function updateWebsitesData(MarketplaceWebsite $website, array $data)
    {

        // WEBSITE DATA
        $domain['site_description']       = Str::limit($data['Description'], 950);
        $domain['site_title']             = Str::limit($data['Title'], 670);

        $domain['category_global_rank']   = $data['CategoryRank']['Rank'] ?? 9999999;
        $domain['main_category_id']       = $this->findCategoryID($data['Category']);


        // update website data
        $website->update($domain);


        // ---------
        // SEO DATA
        $seoData['bounce_rate']            = $this->normaliseNumber($data['Engagments']['BounceRate']);
        $seoData['similarweb_traffic']     = $data['Engagments']['Visits'];
        $seoData['avg_time_on_site']       = round($data['Engagments']['TimeOnSite']);
        $seoData['pages_per_visit']        = round($data['Engagments']['PagePerVisit']);

        $seoData['similarweb_global_rank'] = $data['GlobalRank']['Rank'] ?? 0;
        $seoData['is_small_site']          = $data['IsSmall'];

        $seoData['similarweb_top_countries'] = $this->formatCountryData($data['TopCountryShares']) ?? null;
        $seoData['similarweb_traffic_last_three_months'] = $data['EstimatedMonthlyVisits'];

        // normalise percentage
        $seoData['similarweb_traffic_sources_percentage'] = $this->formattrafficShareData(
            $data['TrafficSources']
        );

        $website->seoStats->update($seoData);


        // Add and attach keywords
        $response['TopKeywords'] = $this->attachKeywords($website, $data['TopKeywords']);

        // large sreenshot ?
        $response['LargeScreenshot'] = $data['LargeScreenshot'];

        return $website;
    }




    /******************************************
     * NORMALISE NUMBER 
     * FROM 0-1 Range to decimal
     * 
     * @param $number INT
     ******************************************/
    public function normaliseNumber($number)
    {

        if (!$number) {
            return 0;
        }

        $normalisedNumber = round($number * 100, 2);

        return $normalisedNumber;
    }




    /*****************************************
     * FIND CATEGORY FROM DB TO ATTACH TO SITE
     * 
     * get category data from similarweb
     * find the category id from db
     *
     * @param $category string (default: other)
     *****************************************/
    public function findCategoryID($categoryText = 'other')
    {

        $categoryText   = str_replace('_', ' ', $categoryText); //replace _
        $haveSubCategory = strpos($categoryText, '/');

        if ($haveSubCategory) {
            $categorise = explode('/', $categoryText);
            $category['parent'] = $categorise[0];
            $category['child']  = $categorise[1];
        } else {
            $category['parent'] = $categoryText;
        }


        // find category by child or parent
        if ($haveSubCategory) {
            $findCategory = MarketplaceWebsiteCategory::where('category', $category['child'])->first();
        } else {
            $findCategory = MarketplaceWebsiteCategory::where('category', $category['parent'])->first();
        }

        // if no category found - (other category id)
        if (!$findCategory) {
            $findCategory = MarketplaceWebsiteCategory::where('category', 'other')->first();
        }

        $categoryID = $findCategory->id;

        return $categoryID;
    }



    /**********************************************
     * FIND COUNTRY ID
     * Cached for one hour
     * 
     * @param $countryCode STRING
     * @return $id INT
     *
     **********************************************/
    public function findCountryID($countryCode)
    {

        if (!isset($countryCode)) {
            return 0;
        }

        $cacheName = 'country_' . $countryCode;

        // db search: cached
        $country_id = Cache::remember($cacheName, 3600, function () use ($countryCode) {

            $country = MarketplaceWebsiteCountry::where('code', $countryCode)
                ->first();

            return $country ? $country->id : 0;
        });

        return $country_id;
    }



    /**********************************************
     * FORMAT COUNTRY DATA
     * 
     * @param $topCountries ARRAY
     * @return $countries JSON
     * 
     **********************************************/
    public function formatCountryData($topCountries)
    {


        if (!isset($topCountries[0])) {
            return null;
        }

        $data = [];
        $i = 0;

        foreach ($topCountries as $countrySingle) {

            $data[$i]['id']     = $this->findCountryID($countrySingle['CountryCode']);
            $data[$i]['code']   = $countrySingle['CountryCode'];
            $data[$i]['share']  = round($countrySingle['Value'] * 100, 2);

            // if its below 1% then set it to 1%
            if ($data[$i]['share'] == 0) {
                $data[$i]['share'] = 1;
            }

            $i++;
        }

        return json_encode($data);
    }






    /**********************************************
     * Normalise Traffic Share Data
     * From 0-1 range to percentage / 100
     * 
     * @param $trafficShare ARRAY
     * @return $data JSON
     * 
     **********************************************/
    public function formattrafficShareData($trafficShare)
    {

        if (!$trafficShare) {
            return false;
        }


        $data = [];

        // social
        if ($trafficShare['Social']) {
            $data['Social'] =  $this->normaliseNumber($trafficShare['Social']);
        } else {
            $data['Social'] = null;
        }

        // Paid Referrals
        if ($trafficShare['Paid Referrals']) {
            $data['Paid Referrals'] =  $this->normaliseNumber($trafficShare['Paid Referrals']);
        } else {
            $data['Paid Referrals'] = null;
        }

        // Mail
        if ($trafficShare['Mail']) {
            $data['Mail'] =  $this->normaliseNumber($trafficShare['Mail']);
        } else {
            $data['Mail'] = null;
        }

        // Referrals
        if ($trafficShare['Referrals']) {
            $data['Referrals'] =  $this->normaliseNumber($trafficShare['Referrals']);
        } else {
            $data['Referrals'] = null;
        }

        // Search
        if ($trafficShare['Search']) {
            $data['Search'] =  $this->normaliseNumber($trafficShare['Search']);
        } else {
            $data['Search'] = null;
        }

        // Direct
        if ($trafficShare['Direct']) {
            $data['Direct'] =  $this->normaliseNumber($trafficShare['Direct']);
        } else {
            $data['Direct'] = null;
        }


        return json_encode($data);
    }






    /**********************************************
     * Create & Attach Keywords
     * 
     * Create and attach topics, if exist then
     * update and attach.
     * 
     * @param $topics Array
     * @param $website MarketplaceWebsite
     * 
     * @return results
     **/
    public function attachKeywords(MarketplaceWebsite $website, $keywords)
    {

        // Insert or update keywords
        // updating because we need to update keyword volumes n cpc in future
        foreach ($keywords as $keyword) {
            Keyword::upsert(
                [
                    'name'     => $keyword['Name'],
                    'cpc'      => $this->normaliseNumber($keyword['Cpc']),
                    'volume'   => $keyword['Volume']
                ],
                ['name'], //unique
                ['cpc', 'volume']
            ); //to update
        }

        // again call and get ids
        $keywordNames   = collect($keywords)->pluck('Name');
        $KeywordIDs     = Keyword::whereIn('name', $keywordNames)->select('id')->get();


        // Attach topics
        $sync_keywords = $website->keyword_website()->sync($KeywordIDs);

        return $sync_keywords;
    }




    // Headers that work with similarweb most of the time
    public function similarWebHeaders()
    {
        return array(
            'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language: en-US,en-GB;q=0.9,en;q=0.8',
            'cache-control: no-cache',
            'dnt: 1',
            'pragma: no-cache',
            'priority: u=0, i',
            'sec-ch-ua: "Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "macOS"',
            'sec-fetch-dest: document',
            'sec-fetch-mode: navigate',
            'sec-fetch-site: none',
            'sec-fetch-user: ?1',
            'upgrade-insecure-requests: 1',
            'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
        );
    }
}
