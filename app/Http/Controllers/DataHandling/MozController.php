<?php

namespace App\Http\Controllers\DataHandling;

use App\Http\Controllers\DataHandling\DataImportTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class Moz<PERSON>ontroller extends Controller
{
    use DataImportTrait;


    /*****************************************
     * Testing Public Ended Function
    *****************************************/
    public function test(){
        return $this->mozToolbarURLStatsFetch('wikihow.com');
    }



    /*****************************************
     * MOZ COOKIE
    *****************************************/
    private $mozCookie = 'cookie:_moz_csrf=a242890a26c49035114fc6ec28c0a9dccb606ed5; mozauth=d5uzKMbsUy1xh6d2euxgA1dHroxNn1XV7wIZezB8ZwTd7M0gkRk4n5dHCmoVW9LF;';


    /*****************************************
     * MOZDATA VIA SEO TOOLBAR 
     *****************************************
     *  
     * Using Moz SEO Toolbar API to get
     * data for url + domain.
     * 
     * Requirement: Account + mozauth
     * 
     * Data: DA, PA, Spam score,
     * external follow/nofollow links
     * 
     * @param $domain string
     * @return $response | false
    *****************************************/
    public function mozToolbarURLStatsFetch(string $url)
    {
        $apiURL  = 'https://mozbar.moz.com/bartender/url-metrics';
        $headers = array(
                    'accept: application/json, text/javascript, */*; q=0.01',
                    'accept-language: en-US,en-GB;q=0.9,en;q=0.8',
                    'cache-control: no-cache',
                    'content-type: application/json; charset=UTF-8',
                    $this->mozCookie,
                    'x-bartender-version: 2'
                );

        $request['headers'] = $headers;
        $request['method']  = 'POST';
        $request['array']   = true;
        $request['post_fields'] = '["'.$url.'"]';

        // fetch
        $data = $this->curlRequest($apiURL, $request);

        // curl success check
        if($data){
            // valid data returned check
            if(isset($data[0]['domain_authority'])){
                return $data[0]; //offset 0 empty wrapper
            }    
        }else{
            return false;
        }
    }


}
