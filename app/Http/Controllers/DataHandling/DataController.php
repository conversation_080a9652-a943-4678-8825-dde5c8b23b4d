<?php

namespace App\Http\Controllers\DataHandling;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;


use App\Http\Controllers\DataHandling\SimilarwebController;
use App\Http\Controllers\DataHandling\SemrushController;
use App\Http\Controllers\DataHandling\AhrefController;
use App\Http\Controllers\DataHandling\MozController;
use App\Http\Controllers\DataHandling\ScrapingController;



class DataController extends Controller
{

    use DataImportTrait; 


    // Test function
    public function test()
    {
        $domain = 'wikihow.com';

        $semrush = new SemrushController;

        $data = $semrush->testSemrush();
       
        return $data;
    }

    

    // Get Website Data
    public function getWebsiteData(string $domain)
    {

        // ahref √
        $ahref = new AhrefController;
        $data['searcheye']       = $ahref->ahrefViaSearchEye($domain);
        $data['ahref_url_stats'] = $ahref->ahrefToolbarURLStatsFetch($domain);


        // MOZ √
        $moz = new MozController;
        $data['moz_extension']   = $moz->mozToolbarURLStatsFetch($domain);


        // SIMILARWEB √
        $similarWeb = new SimilarwebController;
        $data['similarWeb'] = $similarWeb->similarwebExtensionDomainDataFetch($domain);


        // SEMRush √
        $semrush = new SemrushController;
        $data['semrush_extension_auth']   = $semrush->semrushAuthApi($domain);
        $data['semrush_extension_public'] = $semrush->publicSemrushApi($domain);


        // Title + Meta Description √
        $site = new ScrapingController;
        $data['meta'] = $site->fetchTitleDescription($domain);

        return $data;
    }




    // Get Keyword Data
    public function getKeywordData(string $keyword)
    {
        $ahref = new AhrefController;
        return $ahref->ahrefToolbarKeywordStatsFetch($keyword);
    }

}
