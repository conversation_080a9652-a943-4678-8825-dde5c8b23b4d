<?php

namespace App\Http\Controllers\DataHandling;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use App\Http\Controllers\DataHandling\DataImportTrait;



class ScrapingController extends Controller
{

    //import common functionality
    use DataImportTrait; 



    /*****************************************
     * SITE: TITLE + DESCRIPTION FROM META
     *****************************************
     * 
     * @param string $url 
     * @param return array|false
    *******************************************/ 
    public function fetchTitleDescription(string $url = 'example.com')
    {
        // CURL REQUEST
        $response = $this->curlRequest($url);

        // check
        if($response == false){ 
            return false; 
        }

        // Parse Body
        try{
            // Use DOMDocument to parse the HTML
            $dom = new \DOMDocument();
            @$dom->loadHTML($response); // Suppress warnings from invalid HTML

            // Fetch title
            $title = $dom->getElementsByTagName('title')->item(0)->textContent ?? '';

            // Fetch meta description
            $metaDescription = '';
            $metas = $dom->getElementsByTagName('meta');
            foreach ($metas as $meta) {
                if ($meta->getAttribute('name') === 'description') {
                    $metaDescription = $meta->getAttribute('content');
                    break;
                }
            }

            return [
                'title' => $title,
                'meta_description' => $metaDescription,
            ];

        } catch (RequestException $e) {
            return false;
        }

        return false;
    }

}
