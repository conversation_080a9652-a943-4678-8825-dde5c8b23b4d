<?php

// This controller is not used for now, but it is here for future reference

namespace App\Http\Controllers\Advertiser;

use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Enums\OrderItemStates;
use App\Models\MarketplaceOrder;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Bavix\Wallet\Models\Transaction;
use Illuminate\Support\Facades\Auth;
use App\Enums\MarketplaceOrderStatus;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\WalletWithdrawalRequest;
use App\Notifications\NewWithdrawalRequest;
use App\Enums\Role;

class AdvertiserController extends Controller
{
    /*********************************************************************
     * ADVERTISER DASHBOARD (do not need dashboard for advertiser)
     **********************************************************************
     *
     * Renders the advertiser dashboard page with overview statistics
     * ..and recent activity.
     *
     * @return \Inertia\Response Renders the advertiser dashboard view
     * ..with necessary data and statistics
     *
     *********************************************************************/
    public function dashboard()
    {
        // Get total orders count
        $totalOrders = MarketplaceOrder::count();

        // Get active orders (in progress)
        $activeOrders = MarketplaceOrder::where('status', MarketplaceOrderStatus::IN_PROGRESS->value)
            ->count();

        // Get total spent
        $totalSpent = MarketplaceOrder::sum('price_paid');

        // Get orders with pending requirements
        $pendingRequirements = MarketplaceSingleOrderItem::whereIn('state', [
            OrderItemStates::RequirementsPending->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::PublicationRevisionRequestedByAdvertiser->value,
        ])
            ->without(['website', 'requirements', 'content'])
            ->count();


        // Get orders needing attention
        $attentionNeeded = MarketplaceSingleOrderItem::whereIn('state', [
            OrderItemStates::RequirementsPending->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::PublicationRevisionRequestedByAdvertiser->value,
        ])
            ->without(['website', 'requirements', 'content'])
            ->get();


        // Get recent orders
        $recentOrders = MarketplaceOrder::withCount('orderItems')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'status' => $order->status,
                    'items_count' => $order->order_items_count, // Use this instead
                    'price_paid' => number_format($order->price_paid, 2),
                    'created_at' => $order->created_at->format('Y-m-d'),
                ];
            });



        // -----------------------
        // Render Dashboard View
        return Inertia::render('Advertiser/Dashboard/Index', [
            'stats' => [
                'totalOrders' => $totalOrders,
                'activeOrders' => $activeOrders,
                'totalSpent' => number_format($totalSpent, 2),
                'pendingRequirements' => $pendingRequirements,
            ],
            'recentOrders' => $recentOrders,
            'attentionNeeded' => $attentionNeeded
        ]);
    }



    /*********************************************************************
     * WALLET
     **********************************************************************
     *
     * Renders the wallet overview page with balance and transaction history.
     * Displays current balance and recent transactions.
     *
     * @return \Inertia\Response Renders the wallet overview view
     * ..with balance and transaction data
     *
     *********************************************************************/
    public function wallet()
    {
        $user = Auth::user();

        // Get recent transactions
        $transactions = $user->transactions()
            ->with('wallet', 'wallet.holder')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'user' => $transaction->wallet->holder, // user who made the transaction
                    'order_id' => $transaction->meta['order_id'] ?? 'N/A', // order id if transaction is related to an order
                    'amount' => $transaction->amount,
                    'reference' => $transaction->meta['reference'] ?? 'N/A',
                    'status' => $transaction->confirmed ? 'Approved' : 'Pending',
                    'date' => $transaction->created_at->format('Y-m-d H:i:s'),
                    'debit' => $transaction->type === 'withdraw' ? number_format($transaction->amount / 100, 2) : '',
                    'credit' => $transaction->type === 'deposit' ? number_format($transaction->amount / 100, 2) : '',
                ];
            });


        // -----------------------
        // Render Wallet View
        return Inertia::render('Advertiser/Wallet/Index', [
            'balance' => $user->user_balance,
            'transactions' => $transactions
        ]);
    }

    /*********************************************************************
     * TRANSACTIONS DETAILS
     **********************************************************************
     *
     * Renders the transaction details page with transaction information.
     * Displays detailed transaction information.
     * 
     * @param int $id The ID of the transaction to display
     * @return \Inertia\Response Renders the transaction details view
     * ..with transaction information
     *
     *********************************************************************/
    public function transactionsDetails($id)
    {
        $transaction = Transaction::find($id);
        // -----------------------
        // Render Transaction Details View
        return Inertia::render('Advertiser/Wallet/Transactions/Index', [
            'transaction' => $transaction,
        ]);
    }



    /*********************************************************************
     * WALLET DETAILS
     **********************************************************************
     *
     * Renders the detailed wallet information page with transaction history
     * ..and detailed balance information.
     *
     * @return \Inertia\Response Renders the wallet details view
     * ..with transaction history and balance details
     *
     *********************************************************************/
    // public function walletDetails() {}


    /*********************************************************************
     * WITHDRAW
     **********************************************************************
     *
     * Renders the withdraw page with payment settings.
     *
     * @return \Inertia\Response Renders the withdraw view
     * ..with payment settings
     *
     *********************************************************************/
    public function withdraw()
    {
        $user = Auth::user();
        $paymentSettings = $user->paymentSettings;
        $withdrawalRequests = $user->withdrawalRequests()
            ->with(['paymentMethod', 'media'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($request) {
                return [
                    'id' => $request->id,
                    'amount' => $request->amount,
                    'status' => $request->status,
                    'created_at' => $request->created_at_formatted,
                    'payment_method' => $request->paymentMethod,
                    'admin_notes' => $request->admin_notes,
                    'payment_url' => $request->payment_url,
                    'media' => $request->media->map(function ($media) {
                        return [
                            'id' => $media->id,
                            'name' => $media->meta['name'],
                            'url' => $media->url,
                            'size' => $media->meta['size'],
                            'mime_type' => $media->mime_type
                        ];
                    })
                ];
            });

        return Inertia::render('Advertiser/Wallet/Withdraw/Index', [
            'paymentSettings' => $paymentSettings,
            'balance' => $user->user_balance,
            'withdrawalRequests' => $withdrawalRequests,
        ]);
    }


    /*********************************************************************
     * WITHDRAW STORE
     **********************************************************************
     *
     * Handles the submission of a withdrawal request.
     * 
     * @param Request $request The request object containing the withdrawal data
     * @return \Illuminate\Http\RedirectResponse Redirects back with a success message
     * ..or error message
     *
     *********************************************************************/
    public function withdrawStore(Request $request)
    {
        $user = Auth::user();
        $isAdvertiser = $user->role === Role::Advertiser->value;

        $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method_id' => $isAdvertiser ? 'nullable|integer|exists:payment_settings,id' : 'required|integer|exists:payment_settings,id',
        ]);


        DB::beginTransaction();

        // Check if user has sufficient balance
        if ($user->user_balance < $request->amount) {
            return back()->with('error', 'Insufficient balance for withdrawal.');
        }

        // Create withdrawal request
        $withdrawalRequest = WalletWithdrawalRequest::create([
            'user_id' => $user->id,
            'amount' => $request->amount,
            'payment_method_id' => $request->payment_method_id,
            'status' => 'pending'
        ]);

        // Transfer balance to super admin wallet
        $user->wallet->transfer(super_admin_user()->wallet, $request->amount * 100, [
            'withdrawal_request_id' => $withdrawalRequest->id,
            'reference' => 'Withdrawal Request',
            'type' => 'withdrawal_request',
            'message' => 'Withdrawal request submitted',
        ]);

        DB::commit();

        // Notify super admin
        super_admin_user()->notify(new NewWithdrawalRequest($withdrawalRequest));

        // Redirect back with success message
        return back()->with('success', 'Withdrawal request submitted successfully. We will process it shortly.');
    }
}
