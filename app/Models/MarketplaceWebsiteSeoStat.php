<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Model;
// use App\Models\MarketplaceWebsite;



class MarketplaceWebsiteSeoStat extends Model
{
  use HasFactory;

  /**
   * 
   * @property int $id
   * @property int $marketplace_website_id
   * @property int $top_traffic_country_id
   * @property \Carbon\Carbon $created_at
   * @property \Carbon\Carbon $updated_at
   * 
   * @property-read \App\Models\MarketplaceWebsite $marketplaceWebsite
   * @property-read \App\Models\MarketplaceWebsiteCountry $country
   */

  protected $guarded = [];

  protected $with = ['country'];


  public function marketplaceWebsite(): BelongsTo
  {
    return $this->belongsTo(MarketplaceWebsite::class, 'marketplace_website_id');
  }

  public function country(): BelongsTo
  {
    return $this->belongsTo(MarketplaceWebsiteCountry::class, 'top_traffic_country_id');
  }
}
