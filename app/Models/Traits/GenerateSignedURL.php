<?php

namespace App\Models\Traits;

use Illuminate\Support\Facades\URL;

trait GenerateSignedURL
{

    /********************************************************************
     * GENERATE SIGNED URL
     *********************************************************************
     * 
     * Generate a signed URL for a website.
     * 
     * @return string
     ********************************************************************/
    public function generateSignedUrl(): string
    {
        return URL::signedRoute('publisher.websites.edit.signed', [
            'id' => $this->id
        ], now()->addDays(config('pressbear.signed_url_expiry_days')));
    }
}
