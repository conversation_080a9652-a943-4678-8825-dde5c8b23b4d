<?php

namespace App\Models\Traits;

use App\Models\Topic;
use Illuminate\Support\Facades\Auth;

trait TopicTrait
{
    public function syncTopics(array $topicNames)
    {
        // -----------------------
        // Create Topics
        // -----------------------
        $topicIds = collect($topicNames)->map(function ($topicName) {
            return Topic::firstOrCreate(['name' => $topicName])->id;
        });

        // -----------------------
        // Sync Topics
        // -----------------------
        $this->topics()->sync($topicIds);

        // -----------------------
        // Log Topic Changes
        // -----------------------
        activity()
            ->performedOn($this)
            ->causedBy(Auth::user())
            ->withProperties(['topics' => $topicNames])
            ->log('Topics synced');

        return true;
    }
}
