<?php

namespace App\Models\Traits;

use App\Enums\Niche;

trait AcceptsNiche
{
    /**********************************
     * WEBSITE ACCEPTS NICHE
     * a function to check if site 
     * accepts the required niche
     ***********************************/
    public function acceptsNiche($niche)
    {
        $niche = strtolower($niche);
        $acceptsNicheList = $this->acceptsNicheList();
        return in_array($niche, $acceptsNicheList);
    }



    /**********************************
     * LIST NICHE WEBSITE ACCEPT
     * @return list array niches
     ***********************************/
    public function acceptsNicheList()
    {
        $list = [];

        foreach (Niche::cases() as $niche) {
            $priceField = $niche->value . '_post_price';
            // Special case for General niche which uses guest_post_price
            if ($niche == Niche::General) {
                $priceField = 'guest_post_price';
            } elseif ($niche == Niche::LinkInsert) {
                $priceField = 'link_insert_price';
            }

            if ($this->{$priceField} > 0) {
                $list[] = $niche->value;
            }
        }

        return $list;
    }
}
