<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Chat extends Model
{
    use HasFactory;

    /**
     * 
     * @property int $id
     * @property string $text
     * @property int $order_item_id
     * @property int $sender_id
     * @property int $receiver_id
     * @property string $attachment_path
     * @property bool $is_read
     * 
     * @property-read \App\Models\User $sender
     * @property-read \App\Models\User $receiver
     * @property-read \App\Models\MarketplaceSingleOrderItem $orderItem
     */

    protected $fillable = [
        'text',
        'order_item_id',
        'sender_id',
        'receiver_id',
        'attachment_path',
        'is_read',
    ];

    protected $casts = [
        'is_read' => 'boolean',
    ];

    /**********************************
     * Relations
     ***********************************/
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(MarketplaceSingleOrderItem::class, 'order_item_id');
    }
}
