<?php

namespace App\Models;

use App\Enums\Role;
use Illuminate\Support\Str;
use App\Enums\OrderItemStates;
use Illuminate\Support\Carbon;
use Spatie\ModelStates\HasStates;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use App\States\OrderItem\OrderItemState;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Traits\Badgeable;

/*********************
 * Order Status
 * - requirements-pending
 * - waiting-team-approval
 * - writing
 * - inprogress
 * 
 *********************/

class MarketplaceSingleOrderItem extends Model
{
    use HasFactory, HasStates, LogsActivity, Badgeable;

    /**
     * The attributes that should be appended to arrays.
     *
     * @var array
     */
    protected $with = ['website', 'requirements', 'content'];

    /**
     * The attributes that should be hidden from arrays.
     *
     * @var array
     */
    protected $fillable = [
        'marketplace_website_id',
        'order_id',
        'state',
        'estimated_publication_date',
        'state',
        'price',
        'word_count',
        'title',
        'description',
        'notes',
        'niche',
        'is_content_provided_by_customer',
        'price_paid',
        'turn_around_time_in_days',
        'is_content_provided_by_customer',
        'publisher_payment_paid',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'created_at_formatted',
        'estimated_publication_date_formatted',
        'state_name',
        'state_label',
        'time_elapsed',
        'possible_transitions',
        'updated_at_formatted',
        'content_status',
    ];
    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded  = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'state' => OrderItemState::class,
        'price_paid' => 'float',
        'price' => 'float',
        'word_count' => 'integer',
        'turn_around_time_in_days' => 'integer',
        'publisher_payment_paid' => 'boolean',
        'is_content_provided_by_customer' => 'boolean',
        'estimated_publication_date' => 'datetime',
    ];

    /**********************************
     * Boot
     ***********************************/
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('user_orders', function ($builder) {
            if (Auth::check() && Auth::user()->role === Role::Advertiser->value) {
                $builder->whereHas('order', function ($query) {
                    $query->where('user_id', Auth::id());
                });
            } elseif (Auth::check() && Auth::user()->role === Role::Publisher->value) {
                $builder->whereHas('website', function ($query) {
                    $query->where('publisher_user_id', Auth::id());
                })->where('state', '!=', OrderItemStates::RequirementsPending->value);
            }
        });
    }




    /**********************************
     * RELATIONS
     ***********************************/
    public function order(): BelongsTo
    {
        return $this->belongsTo(MarketplaceOrder::class);
    }

    public function website(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsite::class, 'marketplace_website_id');
    }

    public function requirements(): HasOne
    {
        return $this->hasOne(OrderItemRequirement::class, 'order_item_id');
    }

    public function content(): HasOne
    {
        return $this->hasOne(OrderItemContent::class, 'order_item_id');
    }

    public function publication(): HasOne
    {
        return $this->hasOne(OrderItemPublication::class, 'order_item_id');
    }



    public function messages(): HasMany
    {
        return $this->hasMany(Chat::class, 'order_item_id');
    }


    public function stateChangeLogs(): HasMany
    {
        return $this->hasMany(StateChangeLog::class, 'model_id', 'id');
    }



    /**
     * Get the state name in kebab-case format.
     *
     * @property string $state_name
     * @return string
     */
    public function getStateNameAttribute()
    {
        return Str::kebab(class_basename($this->state));
    }

    /**********************************
     * STATE LABEL
     * 
     * Get the human-readable state label.
     *
     * @property string $state_label
     * @return string
     ***********************************/
    public function getStateLabelAttribute()
    {
        return $this->state->label();
    }


    /**********************************
     * CREATED AT FORMATTED
     *
     * @return string
     * Returns the created at date in a human-readable format.
     ***********************************/
    public function getCreatedAtFormattedAttribute()
    {
        return Carbon::parse($this->created_at)->format('d M, Y');
    }


    /**********************************
     * UPDATED AT FORMATTED
     *
     * @return string
     * Returns the updated at date in a human-readable format.
     ***********************************/
    public function getUpdatedAtFormattedAttribute()
    {
        return Carbon::parse($this->updated_at)->format('d M, Y');
    }




    /**********************************
     * ESTIMATED PUBLICATION DATE
     ***********************************/
    public function getEstimatedPublicationDateFormattedAttribute()
    {
        return Carbon::parse($this->estimated_publication_date)->format('d M, Y');
    }


    /**********************************
     * TIME ELAPSED
     *
     * @return string
     * Returns the time elapsed since the order item was created.
     ***********************************/
    public function getTimeElapsedAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }




    /**********************************
     * IS DELIVERED
     *
     * @return bool
     * Returns true if the order item is delivered, false otherwise.
     ***********************************/
    public function scopeIsDelivered($query)
    {
        return $query->where('state', OrderItemStates::OrderItemCompleted->value);
    }

    /**********************************
     * SCOPE ORDER ITEM PENDING
     ***********************************/
    public function scopeOrderItemPending($query)
    {
        return $query->where('state', '!=', OrderItemStates::OrderItemCompleted->value);
    }


    /**********************************
     * WITHOUT CUSTOMER CONTENT
     ***********************************/
    public function scopeWithoutCustomerContent($query)
    {
        return $query->where('is_content_provided_by_customer', false);
    }

    /**********************************
     * SEARCH BY TERM
     ***********************************/
    public function scopeSearchByTerm(Builder $query, string $term): Builder
    {
        $term = Str::limit(strip_tags($term), 100);
        $searchPattern = '%' . trim($term) . '%';

        return $query->where(function (Builder $q) use ($searchPattern) {
            $q->whereHas('content.writer', function (Builder $writer) use ($searchPattern) {
                $writer->where('name', 'like', $searchPattern)
                    ->orWhere('email', 'like', $searchPattern);
            })->orWhereHas('website', function (Builder $website) use ($searchPattern) {
                $website->where('website_domain', 'like', $searchPattern);
            });
        });
    }



    /**********************************
     * POSSIBLE TRANSITIONS
     *
     * @return array
     * Returns an array of possible transitions from the current state.
     ***********************************/
    public function getPossibleTransitionsAttribute()
    {
        // return $this->state->possibleTransitions();\
        $stateClass = get_class($this->state);
        return collect($stateClass::possibleTransitions())
            ->map(function ($transitionClass) {
                return [
                    'class' => $transitionClass,
                    'label' => $transitionClass::label(),
                ];
            })
            ->values()
            ->toArray();
    }


    /**********************************
     * CONTENT COMPLETED
     *
     * @return bool
     * Returns true if the content is completed, false otherwise.
     ***********************************/
    public function getContentCompletedAttribute(): bool
    {
        $check_states = [
            OrderItemStates::ContentPending->value,
            OrderItemStates::ContentAssignedToWriter->value,
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ];

        if (
            $this->is_content_provided_by_customer == 0
            &&
            $this->content != null
            &&
            in_array($this->state, $check_states)
        ) {
            return false;
        }

        return true;
    }






    /**********************************
     * CONTENT STATUS
     *
     * @return string
     * Returns the content status in a human-readable format.
     ***********************************/
    public function getContentStatusAttribute(): string
    {
        if ($this->is_content_provided_by_customer == 1) {
            return "Content Provided by Customer";
        }

        if ($this->is_content_provided_by_customer == 0) {
            if ($this->state == OrderItemStates::ContentPending->value) {
                return 'Not Assigned to Writer';
            }
        }

        if ($this->is_content_provided_by_customer == 0 && $this->content != null) {
            if ($this->state == OrderItemStates::ContentAssignedToWriter->value) {
                return 'Assigned to Writer';
            }
            if ($this->state == OrderItemStates::ContentAdvertiserReview->value) {
                return 'Advertiser Review';
            }
            if ($this->state == OrderItemStates::ContentRevisionRequestedByAdvertiser->value) {
                return 'Revision Requested';
            }
            if ($this->state == OrderItemStates::ContentAwaitingPublisherApproval->value) {
                return 'Awaiting Publisher Approval';
            }
            if ($this->getContentCompletedAttribute()) {
                return 'Completed';
            }
        }
        return "Not Applicable";
    }





    /**********************************
     * CAN ASSIGN WRITER
     *
     * @return bool
     * Returns true if the writer can be assigned, false otherwise.
     ***********************************/
    public function getCanAssignWriterAttribute(): bool
    {
        $check_states = [
            OrderItemStates::ContentPending->value,
            OrderItemStates::ContentAssignedToWriter->value,
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ];

        if (($this->is_content_provided_by_customer == 0) && in_array($this->state, $check_states)) {
            return true;
        }
        return false;
    }





    /**********************************
     * ACTIVITY LOG OPTIONS
     ***********************************/
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
