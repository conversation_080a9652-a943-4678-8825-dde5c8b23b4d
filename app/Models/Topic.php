<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;


class Topic extends Model
{
    /**
     * 
     * @property int $id
     * @property string $name
     * @property \Carbon\Carbon $created_at
     * @property \Carbon\Carbon $updated_at
     * 
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\MarketplaceWebsite $websites
     */

    use HasFactory;

    protected $guarded = [];

    public function websites()
    {
        return $this->belongsToMany(MarketplaceWebsite::class, 'topic_website', 'topic_id', 'marketplace_website_id');
    }

    // public function websites(): BelongsToMany
    // {
    //     return $this->belongsToMany(MarketplaceWebsite::class, 'topic_website', 'topic_id', 'website_id');
    // }
}
