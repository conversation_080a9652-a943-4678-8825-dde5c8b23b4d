<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StateChangeLog extends Model
{
    /**
     * 
     * @property int $id
     * @property string $model_type
     * @property int $model_id
     * @property string $from_state
     * @property string $to_state
     * @property \Carbon\Carbon $created_at
     * @property \Carbon\Carbon $updated_at
     * 
     * @property-read \Illuminate\Database\Eloquent\Model $model
     * @property-read \App\Models\User $user
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    public $table = 'order_item_state_transitions';

    // FILLABLE
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'model_id',
        'model_type',
        'from_state',
        'to_state',
        'user_id',
        'details',
        'ip_address',
        'user_agent'
    ];

    // CASTS
    // -------------------------------------------------------------------------------- //
    protected $casts = [
        'details' => 'array'
    ];

    // RELATIONS
    // -------------------------------------------------------------------------------- //

    public function model()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
