<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;


class MarketplaceWebsiteCategory extends Model
{
    use HasFactory;

    /**
     * 
     * @property int $id
     * @property string $category
     * @property bool $parent
     * @property int $parent_category_id
     * 
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\MarketplaceWebsite $websites
     */

    protected $fillable = [
        'category',
        'parent',
        'parent_category_id'
    ];

    public $timestamps = false;

    protected $table = 'website_categories';

    public function websites(): HasMany
    {
        return $this->hasMany(MarketplaceWebsite::class, 'main_category_id');
    }
}
