<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserPaymentSetting extends Model
{
    use HasFactory;

    /**
     * 
     * @property int $id
     * @property int $user_id
     * @property string $key
     * @property array $value
     * @property bool $is_default
     * 
     * @property-read \App\Models\User $user
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    protected $table = 'wallet_payout_details';

    // -------------------------------------------------------------------------------- //
    // PROPERTIES
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'user_id',
        'key',
        'value',
        'is_default',
    ];

    // -------------------------------------------------------------------------------- //
    // CASTS
    // -------------------------------------------------------------------------------- //
    protected $casts = [
        'value' => 'array',
        'is_default' => 'boolean',
    ];

    // -------------------------------------------------------------------------------- //
    // RELATIONS
    // -------------------------------------------------------------------------------- //
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
