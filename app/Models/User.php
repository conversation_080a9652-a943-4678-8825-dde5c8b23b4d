<?php

namespace App\Models;

use App\Enums\Role;
use Illuminate\Support\Str;
use App\Models\MarketplaceOrder;
use App\Models\MarketplacePayment;
use Bavix\Wallet\Traits\HasWallet;
use Spatie\Activitylog\LogOptions;
use App\Models\MarketplaceCartItem;
use Bavix\Wallet\Interfaces\Wallet;
use Illuminate\Notifications\Notifiable;
use App\Models\MarketplaceWebsiteCountry;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;


/*********************************************************************
 * USER MODEL
 *********************************************************************
 *
 * Represents a user in the system with authentication and wallet capabilities.
 * Handles user properties, roles, and various relationships.
 *
 * - Manages user authentication and verification
 * - Handles wallet and payment functionality
 * - Provides role-based access control
 *
 *********************************************************************/
class User extends Authenticatable implements MustVerifyEmail, Wallet
{
    use HasFactory;
    use Notifiable;
    use TwoFactorAuthenticatable;
    use SoftDeletes;
    use HasWallet;
    use LogsActivity;


    /**
     * DB COLUMNS
     * @property int $id
     * @property string $name
     * @property string $email
     * @property string|null $phone
     * @property string|null $company
     * @property string|null $address
     * @property string|null $city
     * @property string|null $postal_code
     * @property int|null $country_id
     * @property string $role
     * @property string|null $primary_domain
     * @property \Carbon\Carbon|null $email_verified_at
     * @property string|null $last_login_ip
     * @property string $password
     * @property string|null $remember_token
     * @property bool $active
     * @property \Carbon\Carbon|null $deleted_at
     * @property \Carbon\Carbon $created_at
     * @property \Carbon\Carbon $updated_at
     */

    /****************************************
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     ****************************************/
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'company',
        'address_data',
        'country_id',
        'role',
        'primary_domain',
        'email_verified_at',
        'last_login_ip',
    ];

    /********************************************
     * The attributes that should be hidden for
     * serialization.
     *
     * @var array<int, string>
     *****************************************/
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];




    //-----------------------------
    // Appends attributes
    protected $appends = ['is_email_verified', 'address', 'city', 'postal_code'];


    /******************************************
     * The attributes that should be cast.
     *
     * @var array<string, string>
     *****************************************/
    protected $casts = [
        'password' => 'hashed',
        'email_verified_at' => 'datetime',
        'address_data' => 'array',
    ];



    /**********************************
     * RELATIONS
     ***********************************/
    public function cartItems(): HasMany
    {
        return $this->hasMany(MarketplaceCartItem::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(MarketplaceOrder::class);
    }

    public function orderItems(): HasManyThrough
    {
        return $this->hasManyThrough(MarketplaceSingleOrderItem::class, MarketplaceOrder::class, 'user_id', 'order_id');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(MarketplacePayment::class);
    }

    public function paymentSettings(): HasMany
    {
        return $this->hasMany(UserPaymentSetting::class);
    }

    public function withdrawalRequests(): HasMany
    {
        return $this->hasMany(WalletWithdrawalRequest::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteCountry::class, 'country_id');
    }

    public function websites(): HasMany
    {
        return $this->hasMany(MarketplaceAdminWebsite::class, 'publisher_user_id');
    }

    public function outreaches(): HasMany
    {
        return $this->hasMany(Outreach::class);
    }

    public function advertiserGuidelines()
    {
        return $this->hasOne(AdvertiserGuideline::class);
    }

    public function writerAssignments(): HasMany
    {
        return $this->hasMany(OrderItemContent::class, 'writer_id');
    }



    /**********************************
     * SCOPES
     ***********************************/
    /**
     * Scope to filter users by writer role
     */
    public function scopeWriters($query)
    {
        return $query->where('role', Role::Writer->value);
    }



    /**********************************
     * METHODS
     ***********************************/
    // added resolveRouteBinding to fetch soft deleted users on edit page
    public function resolveRouteBinding($value, $field = null)
    {
        return $this->withTrashed()->where($field ?? 'id', $value)->firstOrFail();
    }

    //-----------------------------
    // Check if email is verified
    public function isEmailVerified(): bool
    {
        return !is_null($this->email_verified_at);
    }
    //-----------------------------
    // Getter for email_verified_at
    public function getIsEmailVerifiedAttribute(): bool
    {
        return !is_null($this->email_verified_at);
    }

    //-----------------------------
    // Getter for balance
    public function getUserBalanceAttribute()
    {
        return number_format($this->balance / 100, 2);
    }

    //-----------------------------
    // Address accessors for backward compatibility
    public function getAddressAttribute()
    {
        return $this->address_data['address'] ?? null;
    }

    public function getCityAttribute()
    {
        return $this->address_data['city'] ?? null;
    }


    public function getPostalCodeAttribute()
    {
        return $this->address_data['postal_code'] ?? null;
    }


    // count writer assignments
    public function getWriterAssignmentsCountAttribute(): int
    {
        return $this->writerAssignments()->count();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }



    /**********************************
     * Roles Magic Methods
     ***********************************/
    public function __call($method, $parameters)
    {
        if (Str::startsWith($method, 'is') && strlen($method) > 2) {
            $roleName = Str::after($method, 'is');
            $roleKey = Str::ucfirst(Str::camel($roleName));

            $role = collect(Role::cases())
                ->first(fn($r) => strcasecmp($r->name, $roleKey) === 0);

            return $role && $this->role === $role->value;
        }

        return parent::__call($method, $parameters);
    }


    /**
     * Check if user has any of multiple roles
     */
    public function hasRole(string|Role|array $roles): bool
    {
        $value = $this->role;

        $roles = is_array($roles) ? $roles : [$roles];

        return collect($roles)
            ->map(fn($r) => $r instanceof Role ? $r->value : $r)
            ->contains($value);
    }
}
