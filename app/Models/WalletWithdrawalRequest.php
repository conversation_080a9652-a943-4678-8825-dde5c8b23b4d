<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasMediaAttachment;

class WalletWithdrawalRequest extends Model
{
    use HasMediaAttachment;

    /**
     * 
     * @property int $id
     * @property int $user_id
     * @property float $amount
     * @property int $payment_method_id
     * @property string $status
     * @property string $admin_notes
     * @property array $files_array
     * @property string $payment_url
     * @property \Carbon\Carbon $created_at
     * @property \Carbon\Carbon $updated_at
     * 
     * @property-read \App\Models\User $user
     * @property-read \App\Models\UserPaymentSetting $paymentMethod
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Media $media
     */

    // -------------------------------------------------------------------------------- //
    // TABLE
    // -------------------------------------------------------------------------------- //
    protected $table = 'wallet_withdrawal_requests';

    // -------------------------------------------------------------------------------- //
    // PROPERTIES
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'user_id',
        'amount',
        'payment_method_id',
        'status',
        'admin_notes',
        'files_array',
        'payment_url',
    ];

    // -------------------------------------------------------------------------------- //
    // CASTS
    // -------------------------------------------------------------------------------- //
    protected $casts = [
        'amount' => 'decimal:2',
        'files_array' => 'array',
    ];

    // -------------------------------------------------------------------------------- //
    // APPENDS
    // -------------------------------------------------------------------------------- //
    protected $appends = [
        'created_at_formatted',
    ];

    // -------------------------------------------------------------------------------- //
    // RELATIONS
    // -------------------------------------------------------------------------------- //

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(UserPaymentSetting::class, 'payment_method_id', 'id');
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    public function getCreatedAtFormattedAttribute(): string
    {
        return Carbon::parse($this->created_at)->format('d M, Y');
    }
}
