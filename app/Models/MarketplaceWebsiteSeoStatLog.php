<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MarketplaceWebsiteSeoStatLog extends Model
{
    protected $fillable = [
        'marketplace_website_id',
        'source',
        'type',
        'data',
        'tries',
        'is_success',
        'error_message',
        'record_updated_at',
    ];

    /**********************************
     * RELATIONS
     ***********************************/
    public function website(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsite::class);
    }
}
