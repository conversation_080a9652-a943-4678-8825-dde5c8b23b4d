<?php

namespace App\Listeners;

use App\Models\StateChangeLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use App\States\OrderItem\OrderItemState;
use App\Services\MarketplaceOrderService;
use Spatie\ModelStates\Events\StateChanged;

/*********************************************************************
 * STATE CHANGE LISTENER - HANDLES MODEL STATE TRANSITIONS
 *********************************************************************
 *
 * Processes state changes for models and performs necessary actions:
 * - Logs state transitions to database
 * - Executes state-specific handlers
 * - Updates related order status
 *
 * @param StateChanged $event
 * Contains initial state, final state, and model information
 *
 * @return void
 *
 *********************************************************************/
class StateChangeListener
{
    public function handle(StateChanged $event): void
    {

        $from = $event->initialState::$name;   // old state
        $to = $event->finalState::$name;       // new state
        $model = $event->model;                 // the model

        // Log::info(message: "Order #{$model->id} changed from $from to $to");

        // Log to database
        StateChangeLog::create([
            'model_id' => $model->id,
            'model_type' => get_class($model),
            'from_state' => $from,
            'to_state' => $to,
            'user_id' => Auth::id(),
            'details' => [
                'model_data' => $model->toArray(),
                'changed_at' => now()->toDateTimeString(),
            ],
        ]);

        // -----------------------
        // Execute State Handler
        /**  @var OrderItemState $state */
        $state = $event->finalState;
        $state->handle($model, $from, $to);

        // Update order status
        $model->order()->update(['status' => MarketplaceOrderService::getStatus($model->order)]);
    }
}
