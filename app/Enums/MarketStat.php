<?php

namespace App\Enums;


/************************************************************************
 * ENUMS: TYPE OF STATS TO GENERATE AND SAVE IN DB
 ************************************************************************
 * Story: Stats generation for showing on db and keeping records.
 * 
 * Purpose: graphs, long term storage, single compute, data
 * 
 * Usage: We generate and save stats with these keys to later reterive
 * for showing and analysis.
 * 
 * ‼️ Note: this is just for reference, final implemenation depends on
 * further discussion, planning, usefulness and feasibility. 
 * 
 * Name Structure: purpose_stat_frequency
/************************************************************************/
enum MarketStat: string
{

    //---------------------------------------
    // USER ENGAGEMENT STATS
    //---------------------------------------
    case ACTIVITY_USER_LOGIN_COUNT_DAILY = 'platform_login_count_daily'; // record +1 on every login separately every day
    case ACTIVITY_ADVERTISER_SEARCHES_COUNT_DAILY = 'advertiser_search_count_daily';


        //---------------------------------------
        // USER ACTIONS STATS
        //---------------------------------------
    case ACTION_ADD_TO_CART_COUNT_DAILY = 'add_to_cart_count_daily'; //record every additon + even when deleted from cart
    case ACTION_ORDER_PLACED_COUNT_DAILY = 'order_count_daily';
    case ACTION_ORDER_PLACED_COUNT_MONTHLY = 'order_count_monthly'; // Monthly


        //---------------------------------------
        // PLATFORM PERFORMANCE STATS
        //---------------------------------------
    case PLATFORM_TOTAL_ORDERS_MONTHLY = 'order_count_monthly'; // Monthly
    case PLATFORM_TOTAL_ORDERS_DAILY = 'order_count_monthly'; // Daily

    case PLATFORM_TOTAL_ORDERS_DELIEVERED_MONTHLY = 'PLATFORM_TOTAL_ORDERS_DELIEVERED'; // Daily
    case PLATFORM_TOTAL_ORDERS_CANCELLED_MONTHLY = 'PLATFORM_TOTAL_ORDERS_cancelled'; // Daily


        //---------------------------------------
        // FINANCIALS STATS
        //---------------------------------------
    case FINANCES_SITE_REVENUE_MONTHLY = 'site_month_revenue';
    case FINANCES_SITE_PROFIT_MONTHLY = 'site_month_profit';

    case FINANCES_TOTAL_WALLET_BALANCE_MONTHLY = 'total_wallet_balance';
    case FINANCES_TOTAL_PUBLISHER_PAYOUT_MONTHLY = 'publiser_payout_total_per_month';
    case FINANCES_TOTAL_ADVERTISER_REFUND_REQUESTS_PROCESSED_MONTHLY = 'refund_requests_total_amount';


        //---------------------------------------
        // PUBLISHER STATS
        //---------------------------------------
    case PUBLISHER_ACTIVE_WEBSITES_MONTHLY = 'active_websites_monthly'; // for admin
    case PUBLISHER_SELF_NEW_SITES_ADDED_MONTHLY = 'new_sites_added_by_publisher_monthly';


        //---------------------------------------
        // TEAM PERFORMANCE STATS
        //---------------------------------------
    case TEAM_NEW_SITES_ADDED_MONTHLY = 'new_sites_added_by_reachout_team_monthly';


        //---------------------------------------
        // BACKEND TASKS RUNs
        //---------------------------------------
    case BACKEND_SITES_SEO_METRICS_REFRESHED_COUNT_DAILY = 'sites_seo_data_updated_count_daily'; //record+count SEOstats updates
    case BACKEND_SITES_SEO_METRICS_REFRESHED_COUNT_MONTHLY = 'sites_seo_data_updated_count_daily';

    case BACKEND_EMAILS_SENT_DAILY = 'BACKEND_EMAILS_SENT_DAILY';
    case BACKEND_EMAILS_SENT_MONTHLY = 'BACKEND_EMAILS_SENT_DAILY';
}
