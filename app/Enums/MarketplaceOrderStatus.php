<?php

namespace App\Enums;

/**************************************************************************
 * ENUMS: PARENT ORDER POSSIBLE STATES
 **************************************************************************
 * Story: Parent orders can contain multiple sub-order items, each with its 
 * own completion state. Therefore, the state of the parent order is 
 * managed independently but is determined by the completion states 
 * of its child order items.
 * 
 * Purpose: These states are used to manage parent order states.
 ***************************************************************************/
enum MarketplaceOrderStatus: string
{

    // STATES
    case PENDING = 'pending';
    case IN_PROGRESS = 'inprogress';
    case DELIVERED = 'delivered';
    case LATE = 'late';
    case COMPLETED = 'completed'; //after all items have been delieved + 3days
    case CANCELLED = 'cancelled';
    case REFUNDED = 'refunded';

    case PARTIALLY_DELIVERED = 'Partially Delivered';

    // RETURN
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
