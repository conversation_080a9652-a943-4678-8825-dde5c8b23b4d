<?php

namespace App\Enums;


/************************************************************************
 * ENUMS: ORDER NICHE TYPES
 ************************************************************************
 * Story: Websites charge differently for general or specific niche 
 * content, and each website has its own policies on the types of content 
 * they accept.
 * 
 * Purpose: These niches represent the current niche types in our system.
 * 
 * Usage: Each order is assigned one of these niche types based on 
 * advertiser selection and publication acceptance.
/************************************************************************/
enum Niche: string
{
    case General = 'general';
    case Casino = 'casino';
    case Adult = 'adult';
    case Cbd = 'cbd';
    case Crypto = 'crypto';
    case Dating = 'dating';
    case Finance = 'finance';
    case LinkInsert = 'link_insert';
}
