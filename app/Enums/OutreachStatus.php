<?php

namespace App\Enums;


/**************************************************************************
 * ENUMS: OUTREACH POSSIBLE STATES
 **************************************************************************
 * Story: Outreach entries progress through defined states (in progress, onboarded, etc.).
 * Purpose: These states drive reporting in OutreachStatsService.
 ***************************************************************************/
enum OutreachStatus: string
{
    // STATES
    case PENDING       = 'pending';       // initial outreach created but not yet started
    case IN_PROGRESS   = 'inprogress';     // outreach is in progress
    case ONBOARDED     = 'onboarded';      // outreach resulted in onboarding
    case REJECTED      = 'rejected';       // outreach was rejected

    // RETURN all values
    /**
     * Get all enum values as an array of strings.
     *
     * @return string[]
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
