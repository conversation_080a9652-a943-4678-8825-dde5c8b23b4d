<?php

namespace App\Providers;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Laravel\Fortify\Fortify;
use Illuminate\Support\Facades\Auth;
use App\Actions\Fortify\CreateNewUser;
use Illuminate\Support\ServiceProvider;
use Illuminate\Cache\RateLimiting\Limit;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use Illuminate\Support\Facades\RateLimiter;
use Laravel\Fortify\Contracts\LoginResponse;
use App\Actions\Fortify\UpdateUserProfileInformation;

class FortifyServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register a custom login response for redirecting users based on role
        $this->app->singleton(LoginResponse::class, function () {
            return new class implements LoginResponse {
                public function toResponse($request)
                {
                    $user = Auth::user();

                    if (!$user) {
                        return redirect('/login'); // Redirect if no user is authenticated
                    }

                    // Update last login IP
                    $user->update([
                        'last_login_ip' => $request->ip()
                    ]);

                    // Redirect based on user role
                    return match ($user->role) {
                        'superadmin' => redirect()->route('admin.dashboard'),
                        'admin' => redirect()->route('admin.dashboard'),
                        'sales' => redirect()->route('admin.dashboard'),
                        'finance' => redirect()->route('admin.dashboard'),
                        'outreach' => redirect()->route('admin.dashboard'),
                        'publisher' => redirect()->route('publisher.dashboard'),
                        'advertiser' => redirect()->route('marketplace'),
                        'writer' => redirect()->route('admin.writer.dashboard'),
                        default => redirect()->route('marketplace'),
                    };
                }
            };
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        RateLimiter::for('login', function (Request $request) {
            $throttleKey = Str::transliterate(Str::lower($request->input(Fortify::username())) . '|' . $request->ip());

            return Limit::perMinute(5)->by($throttleKey);
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });
    }
}
