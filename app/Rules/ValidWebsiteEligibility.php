<?php

namespace App\Rules;

use App\Services\WebsiteEligibilityService;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidWebsiteEligibility implements ValidationRule
{
    protected $service;

    /*********************************************************************
     * CONSTRUCTOR
     *********************************************************************
     *
     * Initializes the rule with the website eligibility service.
     *********************************************************************/
    public function __construct()
    {
        $this->service = new WebsiteEligibilityService();
    }

    /*********************************************************************
     * VALIDATE
     *********************************************************************
     *
     * Validates the website eligibility based on the following criteria:
     * 1. The domain must be valid and match the email domain
     * 2. The domain must be in a valid format
     * 3. The domain must not be banned
     * 4. The domain must not already be in use
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     *********************************************************************/
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $isValid = $this->service->validate($value);

        if (! $isValid) {
            $fail(__("The domain is not eligible for publishing."));
        }
    }
}
