<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckSiteIsValid implements ValidationRule
{
    /*********************************************************************
     * VALIDATE
     *********************************************************************
     *
     * Validates the site is valid based on the following criteria:
     * 1. The site must be valid and match the email domain
     * 2. The site must be in a valid format
     * 3. The site must not be banned
     * 4. The site must not already be in use
     * 
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        //
    }
}
