<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NewWebsite implements ValidationRule
{
    /*********************************************************************
     * VALIDATE
     *********************************************************************
     *
     * Validates the new website based on the following criteria:
     * 1. The domain must be valid and match the email domain
     * 2. The domain must be in a valid format
     * 3. The domain must not be banned
     * 4. The domain must not already be in use
     * 
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        //
    }
}
