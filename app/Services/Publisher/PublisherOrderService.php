<?php

namespace App\Services\Publisher;

use App\Models\MarketplaceOrder;
use App\Models\MarketplaceSingleOrderItem;
use App\Http\Controllers\Publisher\Resources\OrderItemsResources as PublisherOrderItemsResources;
use App\Http\Controllers\Publisher\Resources\OrderResource as PublisherOrderResource;
use App\Http\Resources\MediaResource;

class PublisherOrderService
{
    /*********************************************************************
     * GET PENDING ORDER ITEMS
     *********************************************************************
     *
     * Get all pending order items.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     *********************************************************************/
    public function getPendingOrderItems()
    {

        $pendingOrderItems = MarketplaceSingleOrderItem::without(['content', 'requirements'])
            ->with(['order', 'order.user', 'website'])
            ->orderItemPending()
            ->orderBy('created_at', 'desc')
            ->paginate(10);


        return PublisherOrderItemsResources::collection($pendingOrderItems);
    }


    /*********************************************************************
     * GET COMPLETED ORDERS
     *********************************************************************
     *
     * Get all completed orders.
     *
     *********************************************************************/
    public function getCompletedOrders()
    {
        $completedOrders = MarketplaceOrder::orderCompleted()
            ->with(['user', 'orderItems'])
            ->orderBy('created_at', 'desc')
            ->get();

        return PublisherOrderResource::collection($completedOrders)->resolve();
    }


    /*********************************************************************
     * GET ORDER DETAILS
     *********************************************************************
     *
     * Get the details of a specific order.
     *
     *********************************************************************/
    public function getOrderDetails(MarketplaceSingleOrderItem $item)
    {

        // ------------------------------
        // Get Order Item with Relations
        $item->load([
            'order.user.advertiserGuidelines',
            'messages',
            'publication',
            'content.media',
        ]);

        return [
            'order' => $item->order,
            'item'  => $item,
            'media' => MediaResource::collection(
                optional($item->content)->media ?? []
            ),
        ];
    }
}
