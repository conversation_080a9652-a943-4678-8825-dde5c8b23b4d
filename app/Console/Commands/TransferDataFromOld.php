<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DataTransfer\TransferDataFromOldService;
use App\Jobs\DataTransferJobs\TransferDataFromOld as TransferDataFromOldJob;

class TransferDataFromOld extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:transfer-data-from-old {--batchSize=100} {--queue=default}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get values from command signature options
        $batchSize = (int) $this->option('batchSize');
        $queue = $this->option('queue');


        // Transfer data from old database to new database
        $newTransferDataFromOldService = new TransferDataFromOldService($batchSize, $queue);
        $result = $newTransferDataFromOldService->transferDataFromOld();

        // Display results in a clean, formatted way
        $this->line(json_encode($result, JSON_PRETTY_PRINT));
    }
}
