# Deploy script for the application

# Exit on error
set -e

#Remove Lock File
rm -f composer.lock

# Install/update Composer dependencies
composer install --no-interaction --prefer-dist --optimize-autoloader

# Install/update NPM dependencies
npm install

# Build assets
npm run build

# Run database migrations
php artisan migrate --force


# Run database Fresh With Seeder For Development Environment Only
 #php artisan migrate:fresh --seed



# Optimize the application
php artisan optimize:clear

# Restart the queue worker if you're using queues
php artisan queue:restart

# Storage link
php artisan storage:link

# Set proper permissions
chmod -R 775 storage bootstrap/cache
# For local development on macOS, we'll just set the permissions without changing ownership
# If you need to deploy to production, uncomment and modify the chown line below with your server's user/group
# chown -R _www:_www storage bootstrap/cache

echo "Deployment completed successfully!" 