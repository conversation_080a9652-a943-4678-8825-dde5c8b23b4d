<template>
    <Combobox v-model="selectedItem" :by="valueKey" class=" relative w-full">
        <ComboboxAnchor as-child>
            <ComboboxTrigger as-child class="w-full overflow-hidden">
                <Button variant="outline" class="justify-between w-full me-10">
                    {{ selectedItem?.[labelKey] ?? placeholder }}
                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50 absolute right-2" />
                </Button>
            </ComboboxTrigger>
        </ComboboxAnchor>

        <ComboboxList class="w-full max-h-[300px] overflow-y-auto ">
            <!-- Sticky Search Box -->
            <div class="sticky top-0 z-10 bg-white px-2 py-2">
                <div class="relative w-full items-center">
                    <ComboboxInput class="pl-9 focus-visible:ring-0 border-0 border-b rounded-none h-10 w-full"
                        :placeholder="`Search ${labelKey}...`" />
                    <span class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
                        <Search class="size-4 text-muted-foreground" />
                    </span>
                </div>
            </div>

            <ComboboxEmpty>
                No item found.
            </ComboboxEmpty>

            <ComboboxGroup class="w-full">
                <ComboboxItem v-for="item in items" :key="item[valueKey]" :value="item">
                    {{ item[labelKey] }}
                    <ComboboxItemIndicator>
                        <Check class="ml-auto h-4 w-4" />
                    </ComboboxItemIndicator>
                </ComboboxItem>
            </ComboboxGroup>
        </ComboboxList>
    </Combobox>
</template>

<script setup>
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
    ComboboxTrigger,
} from '@/Components/ui/combobox'

import { Button } from '@/Components/ui/button'
import { ChevronsUpDown, Check, Search } from 'lucide-vue-next'
import { ref, watch } from 'vue'

// Props
const props = defineProps({
    items: { type: Array, required: true },
    modelValue: [String, Number, null],
    labelKey: { type: String, default: 'name' },
    valueKey: { type: String, default: 'id' },
    placeholder: { type: String, default: 'Select item' }
})

const emit = defineEmits(['update:modelValue'])

// Selected item reference
const selectedItem = ref(
    props.items?.find(item => item[props.valueKey] === props.modelValue) || null
)

watch(() => props.modelValue, (newVal) => {
    selectedItem.value = props.items?.find(item => item[props.valueKey] === newVal) || null
})

watch(selectedItem, (val) => {
    emit('update:modelValue', val?.[props.valueKey] ?? null)
})
</script>
