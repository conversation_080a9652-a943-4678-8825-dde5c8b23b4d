<template>
  <Dialog :open="open" @update:open="(val) => emit('update:open', val)">
    <DialogContent class="sm:max-w-lg h-[500px] flex flex-col">
      <!-- Header -->
      <DialogHeader>
        <DialogTitle>Assign Publisher</DialogTitle>
        <DialogDescription class="flex flex-col gap-2">
          <div>
            Select a publisher for <strong>{{ website.website_domain }}</strong>.
          </div>
          <hr />
          <div class="text-sm text-indigo-600 cursor-pointer hover:underline" @click="showCreateDialog = true">
            + Create a new Publisher and Assign Website
          </div>

          <CreateAndAssignPublisherDialog :open="showCreateDialog" :website="website" @update:open="(val) => showCreateDialog = val" @created="handleCreatedPublisher" />


        </DialogDescription>

      </DialogHeader>

      <!-- Selected Publisher Info -->
      <div v-if="currentPublisherId !== 0 && selectedUser" class="mb-0 p-2 rounded bg-gray-100 flex flex-row items-center justify-between">

        <div>
          <div class="text-sm font-medium flex flex-row gap-1 items-center ">
            <Check class="w-4 h-4" /> {{ selectedUser.name }}
          </div>
          <div class="text-xs text-gray-600">{{ selectedUser.email }} - ID: {{ selectedUser.id }}</div>

        </div>

        <div>
          <button @click="unassignPublisher" class="text-red-500 hover:text-red-600 flex flex-row items-center gap-1">
            <UserMinus class="w-4 h-4" />

            <span>
              Unassign
            </span>
          </button>
        </div>

      </div>

      <!-- Search Box -->
      <div class="mb-0">
        <input v-model="search" type="text" class="w-full px-3 py-2 border rounded" placeholder="Search publisher by name or email" />
      </div>

      <!-- Scrollable Results -->
      <div class="flex-1 overflow-y-auto border rounded mb-4">
        <div v-if="loading" class="p-4 text-sm text-gray-500 text-center">Searching...</div>
        <ul v-else>
          <li v-if="users.length === 0" class="px-4 py-2 text-gray-500 text-sm text-center">
            No Publisher Found
          </li>
          <li v-for="user in users" :key="user.id" @click="selectedUser = user" :class="[
            'px-4 py-2 cursor-pointer transition-colors',
            selectedUser?.id === user.id
              ? 'bg-indigo-100 hover:bg-indigo-100'
              : 'hover:bg-gray-100',
            user.id === currentPublisherId && selectedUser?.id !== user.id
              ? 'bg-indigo-200'
              : ''
          ]">
            <div class="font-medium flex flex-row gap-1 items-center">
              <Check v-if="user.id === currentPublisherId" class="w-4 h-4" /> {{ user.name }}
            </div>
            <div class="text-xs text-gray-500">
              {{ user.email }} | ID: {{ user.id }}
            </div>
          </li>
        </ul>
      </div>

      <!-- Footer -->
      <DialogFooter>
        <DialogClose as-child>
          <button class="btn btn-light">Cancel</button>
        </DialogClose>
        <button @click="assignPublisher" class="btn btn-indigo transition-opacity" :class="{
          'opacity-50 cursor-not-allowed': isDisabled
        }" :disabled="isDisabled">
          Assign
        </button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup>
// ==========================
// Imports
// ==========================
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/Components/ui/dialog'

import { ref, watch, computed } from 'vue'
import axios from 'axios'
import { debounce } from 'lodash'
import { Check, UserMinus } from 'lucide-vue-next'
import CreateAndAssignPublisherDialog from './CreateAndAssignPublisherDialog.vue'

// ==========================
// Props & Emits
// ==========================
const props = defineProps({
  open: Boolean,
  website: Object,
  currentPublisherId: Number,
  publisher: Object
})

const emit = defineEmits(['update:open', 'assigned'])

// ==========================
// State
// ==========================
const showCreateDialog = ref(false)
const search = ref('')
const users = ref([])
const loading = ref(false)
const selectedUser = ref(null)

// ==========================
// Computed
// ==========================
// Disable Assign button if no valid change
const isDisabled = computed(() => {
  return (
    loading.value ||
    !selectedUser.value ||
    selectedUser.value.id === props.currentPublisherId
  )
})

// ==========================
// Watchers
// ==========================
// Handle dialog open/close state
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    fetchUsers()
    if (props.website?.publisher_user_id && props.website?.publisher) {
      selectedUser.value = {
        id: props.website.publisher_user_id,
        ...props.website.publisher
      }
    }
  } else {
    debouncedFetchUsers.cancel()
    search.value = ''
    users.value = []
    selectedUser.value = null
  }
})

// Sync selected publisher when props change
watch(
  () => [props.currentPublisherId, props.website],
  ([id, website]) => {
    if (website && id && website.publisher) {
      selectedUser.value = {
        id,
        ...website.publisher
      }
    } else {
      selectedUser.value = null
    }
  },
  { immediate: true }
)

// Debounced search trigger
const debouncedFetchUsers = debounce((keyword) => {
  fetchUsers(keyword)
}, 300)

// Trigger fetch on search input change
watch(search, (val) => {
  selectedUser.value = null
  if (val.length === 0) {
    fetchUsers('')
  } else if (val.length >= 1) {
    debouncedFetchUsers(val)
  }
})

// ==========================
// Methods
// ==========================
// Fetch publisher users based on keyword
async function fetchUsers(keyword = '') {
  loading.value = true
  try {
    const response = await axios.get(route('admin.websites.search-publishers'), {
      params: { searchTerm: keyword }
    })
    users.value = response.data
  } catch (error) {
    console.error('Error fetching users:', error)
    users.value = []
  } finally {
    loading.value = false
  }
}

// Assign selected user as publisher to current website
function assignPublisher() {
  axios.put(route('admin.websites.assign-publisher', props.website.id), {
    user_id: selectedUser.value?.id
  }).then(() => {
    emit('assigned', selectedUser.value)
    emit('update:open', false)
  })
}

// Remove publisher from current website
function unassignPublisher() {
  if (confirm('Are you sure you want to unassign the publisher?')) {
    axios.put(route('admin.websites.unassign-publisher', props.website.id), {
      user_id: 0
    }).then(() => {
      selectedUser.value = null
      emit('assigned', null)
      emit('update:open', false)
    })
  }
}

// Handle new publisher created from nested modal
function handleCreatedPublisher(user) {
  selectedUser.value = user
  emit('assigned', user)
}
</script>
