<template>
    <div class="text-xs">

        <span v-bind="attrs" :class="[...badgeClass, attrs.class]">
            {{ status }}
        </span>
    </div>

</template>

<script setup>
import { computed, useAttrs } from 'vue'
defineOptions({ inheritAttrs: false });

const attrs = useAttrs()

const props = defineProps({
    status: {
        type: String,
        required: true,
    }
})

// Make badgeClass return an array
const badgeClass = computed(() => {
    const base = [
        'inline-flex',
        'items-center',
        'px-2.5',
        'py-0.5',
        'rounded-full',
        'font-medium',
        'whitespace-nowrap'
    ];

    const statusColors = {
        'Not Assigned to Writer': ['bg-red-100', 'text-red-800'],
        'Assigned to Writer': ['bg-blue-100', 'text-blue-800'],
        'Advertiser Review': ['bg-green-100', 'text-green-800'],
        'Revision Requested': ['bg-purple-100', 'text-purple-800'],
        'Awaiting Publisher Approval': ['bg-indigo-100', 'text-indigo-800'],
        'Completed': ['bg-green-100', 'text-green-800'],
    };

    return [...base, ...(statusColors[props.status] || ['bg-gray-100', 'text-gray-800'])];
});
</script>
