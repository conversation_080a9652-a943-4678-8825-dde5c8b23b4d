<template>
    <Dialog :open="open" @update:open="val => emit('update:open', val)">
        <DialogContent class="sm:max-w-[425px] bg-white">
            <DialogHeader>
                <DialogTitle>{{ title }}</DialogTitle>
                <DialogDescription>{{ description }}</DialogDescription>
            </DialogHeader>

            <DialogFooter class="flex justify-end gap-2 mt-4">
                <DialogClose as-child>
                    <button @click="handleClose" class="px-4 py-2 rounded bg-gray-100 hover:bg-gray-200 text-sm">
                        {{ cancelText }}
                    </button>
                </DialogClose>

                <button @click="handleConfirm" class="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 text-sm">
                    {{ confirmText }}
                </button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>


<script setup>
// ==========================
// Imports
// ==========================
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogFooter,
    DialogTitle,
    DialogDescription,
    DialogClose
} from '@/Components/ui/dialog'

// ==========================
// Props & Emits
// ==========================
const props = defineProps({
    open: Boolean,
    title: String,
    description: String,
    cancelText: {
        type: String,
        default: 'Cancel'
    },
    confirmText: {
        type: String,
        default: 'Continue'
    }
})

const emit = defineEmits(['update:open', 'confirm', 'cancel'])

// ==========================
// Methods
// ==========================
// Handle close button or cancel action
function handleClose() {
    emit('update:open', false)
    emit('cancel')
}

// Handle confirmation action
function handleConfirm() {
    emit('confirm')
    emit('update:open', false)
}
</script>
