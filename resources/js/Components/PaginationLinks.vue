<template>
    <div v-if="links.length > 1" class="flex justify-center mt-4">

        <nav class="inline-flex shadow-sm">
            <template v-for="(link, index) in links" :key="index">
                <button v-if="link.url" @click.prevent="changePage(link)"
                    class="px-3 py-1 border border-gray-300 text-gray-700 bg-gray-0 hover:bg-gray-100"
                    :class="{ 'font-bold bg-indigo-600 text-white hover:bg-indigo-700': link.active }"
                    v-html="link.label" />
                <span v-else class="px-3 py-1 border border-gray-200 text-gray-300 cursor-default"
                    v-html="link.label" />
            </template>
        </nav>
    </div>
</template>


<script setup>
// ==========================
// Imports
// ==========================
import { router } from '@inertiajs/vue3'

// ==========================
// Props
// ==========================
const props = defineProps({
    links: [Array, Object],   // Pagination links from backend response
    filters: Object // Current filter state to preserve during navigation
})

// ==========================
// Methods
// ==========================
// Handle page change while preserving current filters and scroll state
const changePage = (link) => {
    const url = new URL(link.url) // Parse URL to extract page number
    const page = url.searchParams.get('page') // Get 'page' param from query string

    const query = {
        ...props.filters, // Merge existing filters
        page,             // Override only the page number
    }

    router.get(url.pathname, query, {
        preserveScroll: true, // Avoid jumping to top
        preserveState: true,  // Maintain component state
    })
}
</script>
