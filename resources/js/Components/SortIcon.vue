<template>
    <span class="inline-block transition-transform duration-300">
        <ChevronsUpDown v-if="!isActive" class="w-4 h-4 text-gray-400" />
        <ChevronsUp v-else :class="[
            'w-4 h-4',
            direction === 'asc' ? 'rotate-180 text-indigo-600' : 'text-indigo-600'
        ]" />
    </span>
</template>

<script setup>
import { ChevronsUp, ChevronsUpDown } from 'lucide-vue-next';

defineProps({
    isActive: {
        type: Boolean,
        default: false,
    },
    direction: {
        type: String,
        default: 'asc', // or 'desc'
    },
});
</script>