<template>
    <div class="flex flex-col items-center justify-center my-4">
        <OctagonAlert class="text-red-500 w-10 h-10" />
        <p class="text-lg font-medium text-red-500 mt-3">
            {{ message1 }}
        </p>
        <p class="text-sm text-gray-500 mt-1">
            {{ message2 }}
        </p>
    </div>
</template>

<script setup>
import { OctagonAlert } from 'lucide-vue-next';

defineProps({
    message1: {
        type: String,
        required: true
    },
    message2: {
        type: String,
        required: true
    }
});
</script>
