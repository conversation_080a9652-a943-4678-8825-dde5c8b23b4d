<template>
    <Dialog :open="open" @update:open="(val) => emit('update:open', val)">
        <template v-if="item">
            <DialogContent class="sm:max-w-lg h-[500px] flex flex-col">
                <DialogHeader>
                    <DialogTitle>Assign Writer</DialogTitle>
                    <DialogDescription>
                        Assign a writer to <strong>Order Item #{{ item.id }}</strong>.
                    </DialogDescription>
                </DialogHeader>

                <!-- Current Writer -->
                <div v-if="currentWriterId && selectedUser"
                    class="mb-2 p-2 rounded bg-gray-100 flex justify-between items-center">
                    <div>
                        <div class="text-sm font-medium flex items-center gap-1">
                            <Check class="w-4 h-4" /> {{ selectedUser.name }}
                        </div>
                        <div class="text-xs text-gray-600">{{ selectedUser.email }} - ID: {{ selectedUser.id }}</div>
                    </div>
                    <!-- <button @click="unassignWriter" class="text-red-500 hover:text-red-600 flex items-center gap-1">
                        <UserMinus class="w-4 h-4" />
                        Unassign
                    </button> -->
                </div>

                <!-- Search Box -->
                <input v-model="search" type="text" class="w-full px-3 py-2 border rounded"
                    placeholder="Search writer by name or email" />

                <!-- Results -->
                <div class="flex-1 overflow-y-auto border rounded mt-2">
                    <div v-if="loading" class="p-4 text-sm text-gray-500 text-center">Searching...</div>
                    <ul v-else>
                        <li v-if="users.length === 0" class="px-4 py-2 text-center text-sm text-gray-500">No writers
                            found</li>
                        <li v-for="user in users" :key="user.id" @click="selectedUser = user" :class="[
                            'px-4 py-2 cursor-pointer',
                            selectedUser?.id === user.id ? 'bg-indigo-100' : 'hover:bg-gray-100'
                        ]">
                            <div class="flex items-center justify-between gap-1">

                                <div>
                                    <div class="font-medium flex items-center gap-1">
                                        <Check v-if="selectedUser?.id === user.id" class="w-4 h-4" />
                                        {{ user.name }}
                                    </div>
                                    <div class="text-xs text-gray-500">{{ user.email }} - ID: {{ user.id }}</div>

                                </div>
                                <div>
                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{{
                                        user.writer_assignments_count }}</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Footer -->
                <DialogFooter>
                    <DialogClose as-child>
                        <button class="btn btn-light">Cancel</button>
                    </DialogClose>
                    <button @click="assignWriter" class="btn btn-indigo transition-opacity duration-200"
                        :class="{ 'opacity-50 cursor-not-allowed': isDisabled }" :disabled="isDisabled">
                        Assign
                    </button>
                </DialogFooter>
            </DialogContent>
        </template>
    </Dialog>
</template>

<script setup>
import {
    Dialog, DialogContent, DialogHeader, DialogTitle,
    DialogDescription, DialogFooter, DialogClose
} from '@/Components/ui/dialog';

import { ref, watch, computed } from 'vue';
import axios from 'axios';
import { debounce } from 'lodash';
import { Check, UserMinus } from 'lucide-vue-next';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    open: Boolean,
    item: Object,
    currentWriterId: Number,
    writer: Object,


});

const emit = defineEmits(['update:open', 'assigned']);

// State
const search = ref('');
const users = ref([]);
const selectedUser = ref(null);
const loading = ref(false);

// Disable if nothing selected or already assigned
const isDisabled = computed(() =>
    loading.value || !selectedUser.value || selectedUser.value.id === props.currentWriterId
);

// Watch for dialog open
watch(() => props.open, (isOpen) => {
    if (isOpen) {
        fetchUsers();
        if (props.writer) {
            selectedUser.value = { id: props.currentWriterId, ...props.writer };
        }
    } else {
        debouncedFetch.cancel();
        users.value = [];
        search.value = '';
        selectedUser.value = null;
    }
});

// Watch for search input
watch(search, (val) => {
    selectedUser.value = null;
    if (val.length === 0) {
        fetchUsers('');
    } else if (val.length >= 1) {
        debouncedFetch(val);
    }
});

const debouncedFetch = debounce((val) => {
    fetchUsers(val);
}, 300);

// Methods
function fetchUsers(keyword = '') {
    loading.value = true;
    axios.get(route('admin.writers.search'), { params: { searchTerm: keyword } })
        .then((res) => {
            users.value = res.data;
        })
        .catch((err) => {
            console.error('Error fetching writers:', err);
            users.value = [];
        })
        .finally(() => loading.value = false);
}

function assignWriter() {
    axios.put(route('admin.orders.assign-writer', props.item.id), {
        user_id: selectedUser.value?.id
    }).then(() => {
        emit('assigned', selectedUser.value);
        emit('update:open', false);

        router.visit(route('admin.orders.assignments.edit', props.item.id), {
            preserveScroll: true,
            preserveState: false // Set to false to force reload data
        });
    });
}

// function unassignWriter() {
//     if (confirm('Are you sure you want to unassign this writer?')) {
//         axios.put(route('admin.orders.unassign-writer', props.item.id), {
//             user_id: 0
//         }).then(() => {
//             // Reload the same page
//             emit('assigned', null);
//             emit('update:open', false);

//             router.visit(route('admin.orders.assignments.edit', props.item.id), {
//                 preserveScroll: true,
//                 preserveState: false // Set to false to force reload data
//             });
//         });
//     }
// }
</script>