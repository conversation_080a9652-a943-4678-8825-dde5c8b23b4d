<template>
    <div class="flex flex-col sm:flex-row gap-x-4 items-center">

        <div v-if="showQuickButtons" class="date-range-buttons">

            <div class="flex flex-row gap-x-2">

                <div class="">
                    <label class="text-xs text-gray-400">&nbsp;</label>
                    <button @click="form.preset_range = 'show_all'"
                        :class="form.preset_range === 'show_all' ? 'btn-gray-selected' : 'btn-gray'"
                        class="whitespace-nowrap">
                        <span class="text-xs">All</span>
                    </button>
                </div>
                <div class="">
                    <label class="text-xs text-gray-400">&nbsp;</label>
                    <button @click="form.preset_range = 'today'"
                        :class="form.preset_range === 'today' ? 'btn-gray-selected' : 'btn-gray'"
                        class="whitespace-nowrap">
                        <span class="text-xs">Today</span>
                    </button>
                </div>

                <div>
                    <label class="text-xs text-gray-400">&nbsp;</label>
                    <button @click="form.preset_range = 'yesterday'"
                        :class="form.preset_range === 'yesterday' ? 'btn-gray-selected' : 'btn-gray'"
                        class="whitespace-nowrap">
                        <span class="text-xs">Yesterday</span>
                    </button>
                </div>

                <div>
                    <label class="text-xs text-gray-400">&nbsp;</label>
                    <button @click="form.preset_range = 'last_7_days'"
                        :class="form.preset_range === 'last_7_days' ? 'btn-gray-selected' : 'btn-gray'"
                        class="whitespace-nowrap">
                        <span class="text-xs">Last 7 Days</span>
                    </button>
                </div>


                <div>
                    <label class="text-xs text-gray-400">&nbsp;</label>
                    <button @click="form.preset_range = 'last_30_days'"
                        :class="form.preset_range === 'last_30_days' ? 'btn-gray-selected' : 'btn-gray'"
                        class="whitespace-nowrap">
                        <span class="text-xs">Last 30 Days</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-x-4 items-center w-full mt-4 sm:mt-0">
            <div class="col-span-6">
                <div class="flex flex-col  w-full">
                    <label class="text-xs text-gray-400">Filter by Date</label>
                    <div>
                        <select v-model="form.preset_range"
                            class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                            <option value="show_all">Show All</option>
                            <option value="custom">Custom Range</option>
                            <option value="today">Today</option>
                            <option value="yesterday">Yesterday</option>
                            <option value="last_7_days">Last 7 Days</option>
                            <option value="last_30_days">Last 30 Days</option>
                            <option value="last_90_days">Last 90 Days</option>
                            <option value="last_12_months">Last 12 Months</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-span-6 sm:col-span-3 mt-4 sm:mt-0" v-if="form.preset_range === 'custom'">
                <div class="flex flex-col  w-full">
                    <label class="text-xs text-gray-400">Start Date</label>
                    <input type="date" v-model="form.start_date"
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
                </div>
            </div>
            <div class="col-span-6 sm:col-span-3 mt-4 sm:mt-0" v-if="form.preset_range === 'custom'">
                <div class="flex flex-col  w-full">
                    <label class="text-xs text-gray-400">End Date</label>
                    <input type="date" v-model="form.end_date"
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
                </div>
            </div>
        </div>



    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { useForm, router } from '@inertiajs/vue3';
import { ref, watch, toRefs } from 'vue';
import debounce from 'lodash/debounce';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    presetRange: { type: String, default: 'show_all' },
    startDate: { type: String, default: '' },
    endDate: { type: String, default: '' },
    showQuickButtons: { type: Boolean, default: true },

});

const emit = defineEmits(['update:filters']);

const { presetRange, startDate, endDate } = toRefs(props);


// ==========================================================
// Form State
// Initializes the filter form with date values
// ==========================================================
const form = ref({
    preset_range: presetRange.value,
    start_date: startDate.value,
    end_date: endDate.value,
});

// ==========================================================
// Methods - Apply Filters
// Triggers router GET request to fetch filtered stats
// ==========================================================
const emitFilters = debounce(() => {
    if (form.value.preset_range !== 'custom') {
        form.value.start_date = '';
        form.value.end_date = '';
    }

    emit('update:filters', {
        preset_range: form.value.preset_range,
        start_date: form.value.start_date,
        end_date: form.value.end_date,
    });
}, 300);


// ==========================================================
// Watchers
// Reactively apply filters when range or dates change
// ==========================================================
watch(() => form.value.preset_range, emitFilters);
watch(() => form.value.start_date, emitFilters);
watch(() => form.value.end_date, emitFilters);
</script>