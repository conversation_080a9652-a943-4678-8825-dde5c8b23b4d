<template>
    <Dialog :open="open" @update:open="(val) => emit('update:open', val)">
        <DialogContent class="sm:max-w-md">
            <DialogHeader>
                <DialogTitle>Create and Assign Publisher</DialogTitle>
                <DialogDescription>
                    Create a new user and assign as publisher for <strong>{{ website.website_domain }}</strong>
                </DialogDescription>
            </DialogHeader>

            <div class="mt-2 space-y-4">
                <div>
                    <label class="block text-sm font-medium">Name</label>
                    <input v-model="name" type="text" class="input w-full px-3 py-2 border rounded" />
                </div>
                <div>
                    <label class="block text-sm font-medium">Email</label>
                    <input v-model="email" type="email" class="input w-full px-3 py-2 border rounded" />
                    <p v-if="error" class="text-sm text-red-500 mt-1">{{ error }}</p>
                </div>
            </div>

            <DialogFooter class="mt-6 flex gap-2 justify-end">
                <DialogClose as-child>
                    <button class="btn btn-light">Cancel</button>
                </DialogClose>
                <button class="btn btn-indigo" @click="createPublisher" :disabled="loading">
                    {{ loading ? 'Creating...' : 'Create Account and Assign' }}
                </button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

<script setup>
// ==========================
// Imports
// ==========================
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
    DialogClose
} from './ui/dialog'

import { ref, watch } from 'vue'
import axios from 'axios'
import { XCircle } from 'lucide-vue-next'

// ==========================
// Props & Emits
// ==========================
const props = defineProps({
    open: Boolean,
    website: Object
})

const emit = defineEmits(['update:open', 'created'])

// ==========================
// State
// ==========================
const name = ref('')
const email = ref('')
const error = ref('')
const loading = ref(false)

// ==========================
// Watchers
// ==========================
// Prefill name/email when dialog opens
watch(() => props.open, (isOpen) => {
    if (isOpen && props.website) {
        name.value = props.website.website_domain || ''
        email.value = props.website.contact_email || ''
        error.value = ''
    }
})

// ==========================
// Methods
// ==========================
// Create publisher and assign to website
async function createPublisher() {
    error.value = ''
    loading.value = true

    try {
        const response = await axios.post(route('admin.users.create-publisher'), {
            name: name.value,
            email: email.value,
            website_id: props.website.id,
        })

        emit('created', response.data.user)
        emit('update:open', false)
    } catch (err) {
        if (err.response && err.response.status === 422) {
            error.value = err.response.data.errors.email?.[0] || 'Validation failed.'
        } else {
            error.value = 'Something went wrong.'
        }
    } finally {
        loading.value = false
    }
}
</script>
