<template>
    <div class="flex flex-col sm:flex-row gap-x-4 items-center">

        <div v-if="showQuickButtons" class="date-range-buttons">

            <div class="flex flex-row gap-x-2">

                <div class="">
                    <button @click="form.preset_range = 'show_all'"
                        :class="form.preset_range === 'show_all' ? 'btn-search-text btn-search-text-selected' : 'btn-search-text'"
                        class="whitespace-nowrap">
                        <span>All</span>
                    </button>
                </div>
                <div class="">
                    <button @click="form.preset_range = 'today'"
                        :class="form.preset_range === 'today' ? 'btn-search-text btn-search-text-selected' : 'btn-search-text'"
                        class="whitespace-nowrap">
                        <span>Today</span>
                    </button>
                </div>
                <div>
                    <button @click="form.preset_range = 'last_7_days'"
                        :class="form.preset_range === 'last_7_days' ? 'btn-search-text btn-search-text-selected' : 'btn-search-text'"
                        class="whitespace-nowrap">
                        <span>Last 7 Days</span>
                    </button>
                </div>


                <div>
                    <button @click="form.preset_range = 'last_30_days'"
                        :class="form.preset_range === 'last_30_days' ? 'btn-search-text btn-search-text-selected' : 'btn-search-text'"
                        class="whitespace-nowrap">
                        <span>Last 30 Days</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-x-4 items-center w-full mt-4 sm:mt-0">
            <div class="col-span-12 sm:col-span-6">
                <div class="flex flex-col w-full">
                    <div>
                        <select v-model="form.preset_range"
                            class="block w-full rounded-md bg-white text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                            <option value="show_all">Show All</option>
                            <option value="custom">Custom Range</option>
                            <option value="today">Today</option>
                            <option value="yesterday">Yesterday</option>
                            <option value="last_7_days">Last 7 Days</option>
                            <option value="last_30_days">Last 30 Days</option>
                            <option value="last_90_days">Last 90 Days</option>
                            <option value="last_12_months">Last 12 Months</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-span-6 sm:col-span-3 mt-4 sm:mt-0" v-if="form.preset_range === 'custom'">
                <div class="flex flex-col w-full">
                    <input type="date" v-model="form.start_date"
                        class=" block w-full rounded-md bg-white px-1 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
                </div>
            </div>
            <div class="col-span-6 sm:col-span-3 mt-4 sm:mt-0" v-if="form.preset_range === 'custom'">
                <div class="flex flex-col w-full">
                    <input type="date" v-model="form.end_date"
                        class=" block w-full rounded-md bg-white px-1 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
                </div>
            </div>
        </div>



    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { useForm, router } from '@inertiajs/vue3';
import { ref, watch, toRefs } from 'vue';
import debounce from 'lodash/debounce';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    presetRange: { type: String, default: 'show_all' },
    startDate: { type: String, default: '' },
    endDate: { type: String, default: '' },
    showQuickButtons: { type: Boolean, default: true },

});

const emit = defineEmits(['update:filters']);

const { presetRange, startDate, endDate } = toRefs(props);


// ==========================================================
// Form State
// Initializes the filter form with date values
// ==========================================================
const form = ref({
    preset_range: presetRange.value,
    start_date: startDate.value,
    end_date: endDate.value,
});

// ==========================================================
// Methods - Apply Filters
// Triggers router GET request to fetch filtered stats
// ==========================================================
const emitFilters = debounce(() => {
    if (form.value.preset_range !== 'custom') {
        form.value.start_date = '';
        form.value.end_date = '';
    }

    emit('update:filters', {
        preset_range: form.value.preset_range,
        start_date: form.value.start_date,
        end_date: form.value.end_date,
    });
}, 300);


// ==========================================================
// Watchers
// Reactively apply filters when range or dates change
// ==========================================================
watch(() => form.value.preset_range, emitFilters);
watch(() => form.value.start_date, emitFilters);
watch(() => form.value.end_date, emitFilters);
</script>

<style scoped>
.btn-search-text {
    @apply text-slate-600 order-last flex w-full gap-x-8 text-sm font-semibold sm:order-none sm:w-auto sm:border-l sm:border-gray-200 sm:pe-2 sm:ps-3;
}

.btn-search-text-selected {
    @apply text-orange-600;
}
</style>