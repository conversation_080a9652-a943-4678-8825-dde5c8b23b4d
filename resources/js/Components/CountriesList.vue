<template>
    <Combobox v-model="selectedCountry" by="id">
        <ComboboxAnchor as-child>
            <ComboboxTrigger as-child>
                <Button variant="outline" class="justify-between w-full">
                    {{ selectedCountry?.name ?? 'Select a country' }}
                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </ComboboxTrigger>
        </ComboboxAnchor>

        <ComboboxList>
            <div class="relative w-full max-w-sm items-center">
                <ComboboxInput class="pl-9 focus-visible:ring-0 border-0 border-b rounded-none h-10" placeholder="Search countries..." />
                <span class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
                    <Search class="size-4 text-muted-foreground" />
                </span>
            </div>

            <ComboboxEmpty>
                No country found.
            </ComboboxEmpty>

            <ComboboxGroup>
                <ComboboxItem v-for="country in countries" :key="country.id" :value="country">
                    {{ country.name }}
                    <ComboboxItemIndicator>
                        <Check class="ml-auto h-4 w-4" />
                    </ComboboxItemIndicator>
                </ComboboxItem>
            </ComboboxGroup>
        </ComboboxList>
    </Combobox>
</template>


<script setup>
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
    ComboboxTrigger,
} from '@/components/ui/combobox'

import { Button } from '@/Components/ui/button'
import { ChevronsUpDown, Check, Search } from 'lucide-vue-next'
import { ref, watch, computed } from 'vue'

// Props
const props = defineProps({
    countries: {
        type: Array,
        required: true
    },
    modelValue: {
        type: [String, Number, null],
        default: null
    }
})

// Emit
const emit = defineEmits(['update:modelValue'])

// Setup
const selectedCountry = ref(
    props.countries.find(c => c.id === props.modelValue) || null
)

// Watch external changes
watch(() => props.modelValue, (newVal) => {
    selectedCountry.value = props.countries.find(c => c.id === newVal) || null
})

// Emit on change
watch(selectedCountry, (val) => {
    emit('update:modelValue', val?.id || null)
})
</script>
