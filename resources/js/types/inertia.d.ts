import { Page, PageProps } from "@inertiajs/core";

// Define the User type
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

// Define the expected structure for Inertia's shared props
export interface CustomProps extends PageProps {
  auth?: {
    user?: User | null;
  };
}

// Extend Inertia's `PageProps` globally to include `auth`
declare module "@inertiajs/vue3" {
  interface PageProps extends Record<string, any>, CustomProps {}
}

declare module "@inertiajs/core" {
  interface Page<CustomProps> {
    props: CustomProps;
  }
}

// Ensure TypeScript loads this file
export {};
