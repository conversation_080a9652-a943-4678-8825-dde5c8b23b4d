/********************************************
************ MARKETPLACE JS *****************
* 1. Global Store Declared
* 2. Global store initialised
* 3. Global Store reset to default function
********************************************/


/*****************************************/
//1. Alpine Global Store declartion only
/*****************************************/
window.marketAlpineDataStore = function(){
  Alpine.store('filters', {
    
    filtersActive: false,

    dr: {
        active: false,
        min: null,
        max: null
    },

    traffic: {
      active: false,
        min: null,
        max: null
    },

    price: {
      active: false,
        min: null,
        max: null,
    },

    spamScore: {
      active: false,
        min: null,
        max: null,
    },

    category: {
      active: false,
        selected: [],
    },

    country: {
      active: false,
        selected: [],
    },

    language: {
      active: false,
        selected: [],
    },

  });

  Alpine.store('search', {
    active: false,
    term: null,
  });

  Alpine.store('niche', {
    active: false,
    type: 'general'
  });

  Alpine.store('table', {
    sort: {
      active: false,
      column: null,
      order: null,
    },
  });
}



/*****************************************/
//2. Declare AlpineStore first time
/*****************************************/
document.addEventListener('alpine:init', () => {marketAlpineDataStore()});



/*****************************************/
//3. Reset Alpine Store Filters To Default 
//   via reset button
/*****************************************/
window.resetStoreFilters = function(){marketAlpineDataStore()}



/***********************************************************************/
//4. Reset Global store when navigating internally via livewire
//   using livewire navigate event to listen and reset before page load
/***********************************************************************/
document.addEventListener('livewire:navigate', (event) => {marketAlpineDataStore()})



/*****************************************/
// Main Function to handle table updates
// Update url is set in header file
/*****************************************/
window.updateTable = function(requestData = false){

   // show progressbar
   progressbarToggle();

   let el    = document.querySelector('#marketplace-main-table')
   let html  = axios.post(tableUpdateURL, {
               filters: Alpine.store('filters'),
               search:  Alpine.store('search'),
               niche:   Alpine.store('niche'),
               table:   Alpine.store('table'),
               data:    requestData, //any additional data
           })
             .then(function(response){
                if(response.status == 200){
                  Alpine.morph(el, response.data)
                }
               // toast('Table Updated', {type: 'success', position: 'bottom-center'})
           })
     // handle errors
}



/*****************************************/
// paginate table
/*****************************************/
window.paginateTable = function(url){

  // show progressbar
  progressbarToggle();

  // extract page number
  let pageNumber    = url = new URL(url);
  let pageValue     = url.searchParams.get('page');
  let paginatedUrl  = tableUpdateURL + '?page=' + pageValue;

  // select table
  let el    = document.querySelector('#marketplace-main-table');
  let html  = axios.post(paginatedUrl, {
                          filters: Alpine.store('filters'),
                          search:  Alpine.store('search'),
                          niche:   Alpine.store('niche'),
                          table:   Alpine.store('table'),
                        })
                          .then(function(response){
                            if(response.status == 200){
                              Alpine.morph(el, response.data)
                            }
                        })
    // handle errors
}



/*****************************************/
// Sorting functionality
/*****************************************/
window.sortUpdate = function(column){

  let previousColumn = Alpine.store('table').sort.column;

  if(previousColumn !== column){
    Alpine.store('table').sort.column = column;
    Alpine.store('table').sort.active = true;
    Alpine.store('table').sort.order = 'desc';

  } else {

    if(Alpine.store('table').sort.order == 'desc'){
      Alpine.store('table').sort.order = 'asc';
      Alpine.store('table').sort.active = true;
    }else{
      Alpine.store('table').sort.order = null;
      Alpine.store('table').sort.active = false;
      Alpine.store('table').sort.column = null
    }
  }

  // update table
  updateTable();
}



/*****************************************/
// Update the top cart preview icon and
// numbers on top of table
// For Marketplace Homepage
/*****************************************/
window.updateMarketCartPreview = function(){

    let cartIcon        = document.querySelector('#cartPreviewTop')

    let tableCount      = document.querySelector('#marketTableStatCount')
    let tablePrice      = document.querySelector('#marketTableStatPrice')
    let topBarCartCount = document.querySelector('#stat-number-cart-top')

    let html = axios.get(fragmentCartPreviewURL)
                    .then(function(response){
                        
                        if(response.status == 200){
                          Alpine.morph(cartIcon, response.data['cartHTML'])
                          tableCount.innerHTML      = response.data['data']['itemsCount']
                          tablePrice.innerHTML      = response.data['data']['totalPrice']
                          topBarCartCount.innerHTML = response.data['data']['itemsCount']
                        }
                    })
  }



/*****************************************/
// Cart Page Updates
// update the top cart preview icon 
// specifially for cart page
/*****************************************/
window.updateCartIcon = function(){

    let cartIcon  = document.querySelector('#cartPreviewTop')

    let html      = axios.get(fragmentCartPreviewURL)
                          .then(function(response){
                              // console.log(response.data)
                              if(response.status == 200){
                                Alpine.morph(cartIcon, response.data['cartHTML'])
                              }
                          })
}



/*****************************************/
// FragmentUpdate General Function,
// pass fragmentURL and cssID
/*****************************************/
window.fragmentUpdate = function(fragmentURL, sectionCssId){

  let section = document.querySelector(sectionCssId);

  let html    = axios.get(fragmentURL)
                      .catch(function(error){
                          toast('Update Error', {type: 'warning', position: 'bottom-center'})
                        })
                      .then(function(response){
                          // console.log(response.data)
                         if(response.status == 200){
                          Alpine.morph(section, response.data)
                         }else{
                          toast('Error', {type: 'warning', position: 'bottom-center'})
                          return false;
                         }
                      })
  return true;
}



/*****************************************/
// ProgressBar Toggle
/*****************************************/
window.progressbarToggle = function() {

  let el = document.querySelector('#table-progress-bar');

  // Get the computed style of the element
  let displayStyle = window.getComputedStyle(el).display;

  // Toggle display
  if (displayStyle === "block") {
    el.style.display = "none";
  } else {
    el.style.display = "block";
  }
};