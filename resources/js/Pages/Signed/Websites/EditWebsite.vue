<template>
    <div class="max-w-4xl mx-auto px-4 py-8">
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">
                Update Website
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Update your website settings and pricing
            </p>
        </div>

        <!-- Progress Steps -->
        <div class="mb-8">
            <nav aria-label="Progress">
                <ol role="list" class="space-y-4 md:flex md:space-x-8 md:space-y-0">
                    <li v-for="(step, index) in steps" :key="step.id" class="md:flex-1">
                        <div class="group flex flex-col border-l-4 py-2 pl-4 md:border-l-0 md:border-t-4 md:pb-0 md:pl-0 md:pt-4"
                            :class="[
                                currentStep > index ? 'border-indigo-600' :
                                    currentStep === index ? 'border-indigo-600' : 'border-gray-200'
                            ]">
                            <span class="text-sm font-medium" :class="[
                                currentStep > index ? 'text-indigo-600' :
                                    currentStep === index ? 'text-indigo-600' : 'text-gray-500'
                            ]">
                                Step {{ index + 1 }}
                            </span>
                            <span class="text-sm font-medium" :class="[
                                currentStep > index ? 'text-indigo-600' :
                                    currentStep === index ? 'text-indigo-600' : 'text-gray-500'
                            ]">
                                {{ step.name }}
                            </span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>

        <form @submit.prevent="submit" class="space-y-8">
            <!-- Step 1: Website Information -->
            <div v-show="currentStep === 0" class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Website Information</h3>
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Website Domain
                    </label>
                    <input v-model="form.website_domain" type="text" placeholder="example.com" readonly
                        class="mt-2 block w-full rounded-md border-gray-200 bg-gray-50 px-4 py-2.5 text-sm text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 cursor-not-allowed" />
                    <p v-if="form.errors.website_domain" class="mt-2 text-sm text-red-600">
                        {{ form.errors.website_domain }}
                    </p>
                </div>
            </div>

            <!-- Step 2: Niche Pricing -->
            <div v-show="currentStep === 1" class="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Niche Pricing</h3>
                    <p class="mt-1 text-sm text-gray-500">Set prices for different types of content</p>
                </div>

                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Guest Post -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">
                                Guest Post Price ($) <span class="text-red-500">*</span>
                            </label>
                            <div class="mt-2">
                                <input v-model="form.guest_post_price" type="number" min="0"
                                    placeholder="Enter Price in USD"
                                    class="field-guest_post_price w-full block px-4 py-2.5 text-sm rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    :class="[
                                        'bg-white border',
                                        validationErrors.guest_post_price ? 'border-red-500' : 'border-gray-300'
                                    ]" />
                            </div>
                            <p v-if="form.errors.guest_post_price" class="mt-2 text-sm text-red-600">
                                {{ form.errors.guest_post_price }}
                            </p>
                        </div>

                        <!-- Other Pricing Fields -->
                        <div class="space-y-4">
                            <template v-for="(field, index) in [
                                { key: 'link_insert_price', label: 'Link Insert Price' },
                                { key: 'crypto_post_price', label: 'Crypto Post Price' },
                                { key: 'finance_post_price', label: 'Finance Post Price' },
                                { key: 'casino_post_price', label: 'Casino Post Price' },
                                { key: 'adult_post_price', label: 'Adult Post Price' },
                                { key: 'dating_post_price', label: 'Dating Post Price' },
                                { key: 'cbd_post_price', label: 'CBD Post Price' }
                            ]" :key="field.key">
                                <div class="flex items-center gap-4">
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center gap-4">
                                                    <div class="w-1/2">
                                                        <div class="flex items-center gap-2">
                                                            <Popover class="relative">
                                                                <PopoverButton
                                                                    class="inline-flex items-center cursor-pointer">
                                                                    <input type="checkbox" class="sr-only peer"
                                                                        :checked="priceToggles[field.key]"
                                                                        @change="disablePrice(field.key)" />
                                                                    <div
                                                                        class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600">
                                                                    </div>
                                                                </PopoverButton>
                                                                <PopoverPanel
                                                                    class="absolute z-10 mt-2 w-64 rounded-md bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5">
                                                                    <div class="text-sm text-gray-500">
                                                                        Toggle to enable/disable this pricing option
                                                                    </div>
                                                                </PopoverPanel>
                                                            </Popover>
                                                            <label class="text-sm font-medium text-gray-700">
                                                                {{ field.label }} ($)
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="w-1/2">
                                                        <div class="mt-1 relative">
                                                            <input v-model="form[field.key]" type="number" min="0"
                                                                :disabled="!priceToggles[field.key]"
                                                                placeholder="Enter Price in USD" :class="[
                                                                    'field-' + field.key + ' w-full block px-4 py-2.5 text-sm rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500',
                                                                    'bg-white border',
                                                                    !priceToggles[field.key] && 'opacity-40 cursor-not-allowed',
                                                                    validationErrors[field.key] ? 'border-red-500' : 'border-gray-300'
                                                                ]" />
                                                            <div v-if="!priceToggles[field.key]"
                                                                class="absolute inset-0 bg-gray-50/50 rounded-md cursor-not-allowed">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <p v-if="form.errors[field.key]" class="mt-2 text-sm text-red-600">
                                                    {{ form.errors[field.key] }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Publishing -->
            <div v-show="currentStep === 2" class="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Publishing Settings</h3>
                    <p class="mt-1 text-sm text-gray-500">Configure your publishing preferences and requirements</p>
                </div>

                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Example Post URL -->
                        <div class="flex items-center gap-4">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Example Post URL
                                </label>
                                <div class="flex items-center gap-2">
                                    <input v-model="form.example_post_url" type="text"
                                        placeholder="https://www.example.com"
                                        class="field-example_post_url w-full block px-4 py-2.5 text-sm rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                        :class="[
                                            'bg-white border',
                                            validationErrors.example_post_url ? 'border-red-500' : 'border-gray-300'
                                        ]" />
                                    <a :href="form.example_post_url" target="_blank"
                                        class="inline-flex items-center justify-center p-2 text-indigo-600 hover:text-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <ExternalLink class="w-5 h-5" />
                                    </a>
                                </div>
                                <p v-if="form.errors.example_post_url" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.example_post_url }}
                                </p>
                            </div>
                        </div>

                        <!-- Site Requirements -->
                        <div class="flex items-center gap-4">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Site Requirements
                                </label>
                                <div class="relative">
                                    <textarea v-model="form.site_requirements" rows="4"
                                        placeholder="Enter site requirements"
                                        class="field-site_requirements w-full block px-4 py-2.5 text-sm rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                        :class="[
                                            'bg-white border',
                                            validationErrors.site_requirements ? 'border-red-500' : 'border-gray-300'
                                        ]"></textarea>
                                </div>
                                <p v-if="form.errors.site_requirements" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.site_requirements }}
                                </p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Turnaround Time -->
                            <div class="flex items-center gap-4">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Turnaround Publishing Time (Days) <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <input v-model="form.turn_around_time_in_days" type="number" min="0"
                                            placeholder="Number of days required for publishing"
                                            class="field-turn_around_time_in_days w-full block px-4 py-2.5 text-sm rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                            :class="[
                                                'bg-white border',
                                                validationErrors.turn_around_time_in_days ? 'border-red-500' : 'border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.turn_around_time_in_days" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.turn_around_time_in_days }}
                                    </p>
                                </div>
                            </div>

                            <!-- Article Validity -->
                            <div class="flex items-center gap-4">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Article Validity (Months)
                                    </label>
                                    <div class="flex items-center gap-2">
                                        <div class="relative flex-1">
                                            <input v-model="form.article_validity_in_months" type="number" min="0"
                                                :max="36" :disabled="articleValidityToggle"
                                                @input="limitArticleValidity" placeholder="Number of Months"
                                                class="field-article_validity_in_months w-full block px-4 py-2.5 text-sm rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                                :class="[
                                                    'bg-white border',
                                                    articleValidityToggle ? 'opacity-40 cursor-not-allowed' : '',
                                                    validationErrors.article_validity_in_months ? 'border-red-500' : 'border-gray-300'
                                                ]" />
                                            <div v-if="articleValidityToggle"
                                                class="absolute inset-0 bg-gray-50/50 rounded-md cursor-not-allowed">
                                            </div>
                                        </div>
                                        <Popover class="relative">
                                            <PopoverButton class="inline-flex items-center gap-2 cursor-pointer">
                                                <input type="checkbox" class="sr-only peer"
                                                    v-model="articleValidityToggle" />
                                                <div
                                                    class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600">
                                                </div>
                                                <span class="text-sm font-medium text-gray-700">Unlimited</span>
                                            </PopoverButton>
                                            <PopoverPanel
                                                class="absolute z-10 mt-2 w-64 rounded-md bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5">
                                                <div class="text-sm text-gray-500">
                                                    Toggle to set unlimited article validity
                                                </div>
                                            </PopoverPanel>
                                        </Popover>
                                    </div>
                                    <p v-if="form.errors.article_validity_in_months" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.article_validity_in_months }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Data -->
            <div v-show="currentStep === 3" class="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Data Settings</h3>
                    <p class="mt-1 text-sm text-gray-500">Configure your data preferences and requirements</p>
                </div>

                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Link Relation -->
                        <div class="flex items-center gap-4">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Link Relation
                                </label>
                                <select v-model.number="form.link_relation"
                                    class="w-full bg-white rounded-md border border-gray-300 px-4 py-2.5 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="dofollow">DoFollow</option>
                                    <option value="nofollow">NoFollow</option>
                                    <option value="sponsored">Sponsored</option>
                                </select>
                                <p v-if="form.errors.link_relation" class="mt-2 text-sm text-red-600">
                                    {{ form.errors.link_relation }}
                                </p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <!-- Indexed Article -->
                            <div class="flex items-center gap-4">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Indexed Article
                                    </label>
                                    <div class="flex items-center gap-2">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer"
                                                :checked="form.indexed_article === 1"
                                                @change="form.indexed_article = $event.target.checked ? 1 : 0" />
                                            <div
                                                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600">
                                            </div>
                                            <span class="ms-3 text-sm font-medium text-gray-700">
                                                {{ form.indexed_article === 1 ? 'Yes' : 'No' }}
                                            </span>
                                        </label>
                                    </div>
                                    <p v-if="form.errors.indexed_article" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.indexed_article }}
                                    </p>
                                </div>
                            </div>

                            <!-- Sponsorship Label -->
                            <div class="flex items-center gap-4">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Sponsorship Label
                                    </label>
                                    <div class="flex items-center gap-2">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer"
                                                :checked="form.sponsorship_label === 1"
                                                @change="form.sponsorship_label = $event.target.checked ? 1 : 0" />
                                            <div
                                                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600">
                                            </div>
                                            <span class="ms-3 text-sm font-medium text-gray-700">
                                                {{ form.sponsorship_label === 1 ? 'Yes' : 'No' }}
                                            </span>
                                        </label>
                                    </div>
                                    <p v-if="form.errors.sponsorship_label" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.sponsorship_label }}
                                    </p>
                                </div>
                            </div>

                            <!-- Homepage Visible -->
                            <div class="flex items-center gap-4">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">
                                        Homepage Visible
                                    </label>
                                    <div class="flex items-center gap-2">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer"
                                                :checked="form.homepage_visible === 1"
                                                @change="form.homepage_visible = $event.target.checked ? 1 : 0" />
                                            <div
                                                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600">
                                            </div>
                                            <span class="ms-3 text-sm font-medium text-gray-700">
                                                {{ form.homepage_visible === 1 ? 'Yes' : 'No' }}
                                            </span>
                                        </label>
                                    </div>
                                    <p v-if="form.errors.homepage_visible" class="mt-2 text-sm text-red-600">
                                        {{ form.errors.homepage_visible }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between gap-4 pt-4">
                <button type="button" v-if="currentStep > 0" @click="previousStep"
                    class="px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Previous
                </button>
                <div class="flex gap-4 ml-auto">
                    <button v-if="currentStep < steps.length - 1" type="button" @click="nextStep"
                        class="px-4 py-2.5 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Next
                    </button>
                    <button v-else type="submit" :disabled="form.processing"
                        class="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
                        <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                        <Save v-else class="h-4 w-4" />
                        {{ form.processing ? "Saving..." : "Save Changes" }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</template>

<script setup>
import { useForm } from "@inertiajs/vue3";
import { Save, LoaderCircle, ExternalLink, Search, Check } from 'lucide-vue-next';
import { inject, ref, watch } from "vue";
import debounce from 'lodash/debounce';
import { Combobox, ComboboxInput, ComboboxOptions, ComboboxOption } from '@headlessui/vue';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue';

const props = defineProps({
    website: Object,
});

const notify = inject("$notify");

const currentStep = ref(0);
const steps = [
    { id: 'website', name: 'Website Information' },
    { id: 'pricing', name: 'Niche Pricing' },
    { id: 'publishing', name: 'Publishing' },
    { id: 'data', name: 'Data' },
];

const form = useForm({
    website_domain: props.website?.website_domain || "",
    guest_post_price: props.website?.guest_post_price || "",
    link_insert_price: props.website?.link_insert_price || "",
    crypto_post_price: props.website?.crypto_post_price || "",
    casino_post_price: props.website?.casino_post_price || "",
    adult_post_price: props.website?.adult_post_price || "",
    finance_post_price: props.website?.finance_post_price || "",
    dating_post_price: props.website?.dating_post_price || "",
    cbd_post_price: props.website?.cbd_post_price || "",
    example_post_url: props.website?.example_post_url || "",
    site_requirements: props.website?.site_requirements || "",
    turn_around_time_in_days: props.website?.turn_around_time_in_days || "",
    article_validity_in_months: props.website?.article_validity_in_months || "",
    link_relation: props.website?.link_relation || "dofollow",
    indexed_article: props.website?.indexed_article || 0,
    sponsorship_label: props.website?.sponsorship_label || 0,
    homepage_visible: props.website?.homepage_visible || 0,
});

const priceToggles = ref({
    link_insert_price: !!props.website?.link_insert_price,
    crypto_post_price: !!props.website?.crypto_post_price,
    casino_post_price: !!props.website?.casino_post_price,
    adult_post_price: !!props.website?.adult_post_price,
    finance_post_price: !!props.website?.finance_post_price,
    dating_post_price: !!props.website?.dating_post_price,
    cbd_post_price: !!props.website?.cbd_post_price,
});

const validationErrors = ref({});

const articleValidityToggle = ref(props.website?.article_validity_toggle || false);

const disablePrice = (field) => {
    priceToggles.value[field] = !priceToggles.value[field];
    if (!priceToggles.value[field]) {
        form[field] = "";
    }
};

const limitArticleValidity = () => {
    if (form.article_validity_in_months > 36) {
        form.article_validity_in_months = 36;
    }
};

watch(articleValidityToggle, (newValue) => {
    if (newValue) {
        form.article_validity_in_months = "";
    }
});

const nextStep = () => {
    if (currentStep.value < steps.length - 1) {
        currentStep.value++;
    }
};

const previousStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
    }
};

const submit = () => {
    // Reset validation errors
    validationErrors.value = {};

    // Validate required fields
    if (!form.guest_post_price) {
        validationErrors.value.guest_post_price = "Guest post price is required";
    }

    // Only validate enabled price fields
    Object.keys(priceToggles.value).forEach(field => {
        if (priceToggles.value[field] && !form[field]) {
            validationErrors.value[field] = `${field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} is required`;
        }
    });

    // If there are validation errors, don't submit
    if (Object.keys(validationErrors.value).length > 0) {
        const firstErrorKey = Object.keys(validationErrors.value)[0];
        notify(validationErrors.value[firstErrorKey], { type: "error" });
        scrollToField(firstErrorKey);
        return;
    }

    // Set disabled price fields to 0
    Object.keys(priceToggles.value).forEach(field => {
        if (!priceToggles.value[field]) {
            form[field] = 0;
        }
    });

    form.put(route('websites.update', { id: props.website.id }), {
        id: props.website.id,
        preserveScroll: true,
        onSuccess: () => {
            notify("Website updated successfully!", { type: "success" });
        },
        onError: () => {
            const firstErrorKey = Object.keys(form.errors)[0];
            notify(form.errors[firstErrorKey], { type: "error" });
            scrollToField(firstErrorKey);
        }
    });
};

const scrollToField = (field) => {
    const el = document.querySelector(`.field-${field}`);
    if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
        setTimeout(() => el.focus(), 300);
    }
};
</script>