<template>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Account Holder Name -->
        <div>
            <label class="block text-sm font-medium text-gray-700">Account Holder Name</label>
            <input v-model="form.account_name" type="text" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div>

        <!-- Account Number -->
        <div>
            <label class="block text-sm font-medium text-gray-700">Account Number</label>
            <input v-model="form.account_number" type="text" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div>

        <!-- Bank Name -->
        <div>
            <label class="block text-sm font-medium text-gray-700">Bank Name</label>
            <input v-model="form.bank_name" type="text" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div>

        <!-- IBAN -->
        <div>
            <label class="block text-sm font-medium text-gray-700">IBAN</label>
            <input v-model="form.iban" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div>

        <!-- SWIFT Code -->
        <div>
            <label class="block text-sm font-medium text-gray-700">SWIFT/BIC Code</label>
            <input v-model="form.swift_code" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div>

        <!-- Bank Address -->
        <div>
            <label class="block text-sm font-medium text-gray-700">Bank Address</label>
            <input v-model="form.bank_address" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div>

        <!-- Country (spans full width) -->
        <!-- <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Country</label>
            <input v-model="form.country" type="text" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" />
        </div> -->

        <div class="">
            <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
            <BaseCombobox v-model="form.country" :items="countries" labelKey="name" valueKey="name" placeholder="Select Country" class="border rounded-md" />
        </div>
    </div>
</template>

<script setup>
import BaseCombobox from '@/Components/BaseCombobox.vue';

const props = defineProps({
    form: Object,
    countries: Array
});

</script>
