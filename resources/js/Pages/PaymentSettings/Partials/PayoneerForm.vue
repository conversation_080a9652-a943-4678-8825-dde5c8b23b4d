<template>
    <div>
        <label>Payoneer Email</label>
        <input v-model="form.email" type="email" required class="w-full border p-2 rounded" />
        <label class="block mt-2">Payoneer Customer ID (optional)</label>
        <input v-model="form.account_id" type="text" class="w-full border p-2 rounded" />
    </div>
</template>
<script setup>
const props = defineProps({ form: Object });
</script>