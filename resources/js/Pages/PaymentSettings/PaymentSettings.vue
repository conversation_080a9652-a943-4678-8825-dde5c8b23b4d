<template>
    <div class="space-y-6">

        <div class="sm:flex sm:items-center mb-4">
            <div class="sm:flex-auto">
                <h2 class="title-1">Payment Settings</h2>
                <p class="subtitle-1">Manage your payment methods and settings here.</p>
            </div>
        </div>


        <div class="mt-10">
            <h3 class="text-lg font-semibold" v-if="settings.length > 0">Saved Methods</h3>
            <ul class="mt-4 space-y-3">
                <li v-for="setting in settings" :key="setting.id" class="flex justify-between items-center border p-3 rounded" :class="setting.is_default ? 'border-green-300 bg-green-50' : ''">
                    <div>
                        <strong class="uppercase">{{ setting.key }}</strong>
                        <span v-if="setting.is_default" class="ml-2 px-2 py-0.5 text-xs bg-green-200 rounded-full text-green-800">
                            Default
                        </span>
                    </div>

                    <div class="flex items-center gap-4">
                        <button @click="makeDefault(setting.id)" class="text-sm" :class="setting.is_default ? 'text-gray-400 cursor-not-allowed' : 'text-indigo-600 hover:underline'" :disabled="setting.is_default">
                            <div class="flex flex-row items-center gap-1">
                                <component :is="setting.is_default ? Check : Plug" class="w-4 h-4" />
                                <span>{{ setting.is_default ? 'Selected as Default' : 'Set as Default' }}</span>
                            </div>
                        </button>


                        <span class="text-gray-300">|</span>

                        <!-- Edit Button -->
                        <button @click="editSetting(setting)" class="text-sm text-yellow-600 hover:underline">
                            <div class="flex flex-row items-center gap-1 ">
                                <span>Edit</span>
                            </div>
                        </button>


                        <span class="text-gray-300">|</span>

                        <!-- Delete Button -->
                        <button
                            @click="deleteSetting(setting.id)"
                            class="text-sm"
                            :class="setting.is_default ? 'text-gray-400 cursor-not-allowed' : 'text-red-600 hover:underline'"
                            :disabled="setting.is_default"
                            :title="setting.is_default ? 'Cannot delete default payment method' : 'Delete payment method'"
                        >
                            <div class="flex flex-row items-center gap-1 ">
                                <span class="">
                                    <Trash class="w-4 h-4" />
                                </span>
                                <span>
                                    Delete
                                </span>
                            </div>
                        </button>
                    </div>
                </li>
            </ul>
        </div>

        <div class="w-full rounded-lg border bg-card text-card-foreground shadow-sm p-12">
            <h3 class="text-lg font-semibold mb-5">Add New Payment Method</h3>

            <form @submit.prevent="submit" ref="formSection">
                <!-- Key Selector -->
                <div>
                    <label class="block text-sm font-medium">Select Payment Method</label>
                    <select v-model="form.key" class="mt-1 border p-2 rounded w-full">
                        <option value="paypal">PayPal</option>
                        <option value="bank">Bank Account</option>
                        <option value="payoneer">Payoneer</option>
                    </select>
                </div>

                <!-- Dynamic Form Inputs -->
                <div class="space-y-4 mt-4">
                    <component :is="currentComponent" v-model:form="form.value" :countries="props.countries" />
                </div>

                <button type="submit" class="btn-indigo mt-4">Save Payment Method</button>
            </form>
        </div>



    </div>
</template>
<script setup>
import { ref, watch, computed, onMounted, inject, nextTick } from 'vue';
import { router, useForm } from '@inertiajs/vue3';
import PaypalForm from './Partials/PayPalForm.vue';
import BankForm from './Partials/BankForm.vue';
import PayoneerForm from './Partials/PayoneerForm.vue';

import { Plug, Trash, Check } from "lucide-vue-next"

const props = defineProps({
    settings: Array,
    countries: Array,
});

const defaultValues = {
    paypal: { email: '' },
    bank: {
        account_name: '',
        account_number: '',
        bank_name: '',
        iban: '',
        swift_code: '',
        bank_address: '',
        country: '',
    },
    payoneer: {
        email: '',
        account_id: '',
    },
};

const notify = inject('$notify');
const formSection = ref(null);


// Initialize with PayPal as default
const form = useForm({
    key: 'paypal',
    value: { ...defaultValues.paypal },
});

const componentMap = {
    paypal: PaypalForm,
    bank: BankForm,
    payoneer: PayoneerForm
};

const currentComponent = computed(() => componentMap[form.key]);

// On mount: preload values from props if exists
// onMounted(() => {
//     preloadValues(form.key);
// });

onMounted(() => {
    const defaultSetting = props.settings.find(s => s.is_default);

    if (defaultSetting) {
        form.key = defaultSetting.key;
        form.value = { ...defaultSetting.value };
    } else {
        // fallback to PayPal if no default
        form.key = 'paypal';
        form.value = { ...defaultValues.paypal };
    }
});


// Watch for key change and load values
watch(() => form.key, (newKey) => {
    preloadValues(newKey);
});

function preloadValues(key) {
    const found = props.settings.find(s => s.key === key);
    form.value = found ? { ...found.value } : { ...defaultValues[key] };
}

const editSetting = (setting) => {
    form.key = setting.key;
    form.value = { ...setting.value };

    nextTick(() => {
        formSection.value?.scrollIntoView({ behavior: 'smooth' });
    });
};

const submit = () => {
    form.post(route('payment-settings.store'), {
        preserveScroll: true,
        onSuccess: () => {
            // Reset form to default values after successful submission
            form.key = 'paypal';
            form.value = { ...defaultValues.paypal };
            form.clearErrors();

            notify('Payment method saved successfully.', { type: 'success' });
        },
        onError: () => {
            const firstError = Object.values(form.errors)[0];
            notify(firstError || 'Failed to save payment method.', { type: 'error' });
        },
    });
};

const makeDefault = (id) => {
    const setting = props.settings.find(s => s.id === id);

    if (!setting) return;

    router.post(route('payment-settings.set-default', id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            form.key = setting.key;
            form.value = { ...setting.value };

            notify('Default payment method updated.', { type: 'success' });
        },
    });
};

const deleteSetting = (id) => {
    const setting = props.settings.find(s => s.id === id);

    // Prevent deletion of default payment method
    if (setting && setting.is_default) {
        notify('Cannot delete the default payment method. Please set another method as default first.', { type: 'error' });
        return;
    }

    if (confirm('Are you sure you want to delete this payment method?')) {
        router.delete(route('payment-settings.destroy', id), {
            preserveScroll: true,
            onSuccess: () => {
                notify('Payment method deleted successfully.', { type: 'success' });
            },
        });
    }
};
</script>