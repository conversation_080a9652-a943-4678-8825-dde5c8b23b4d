<template>
    <div class="space-y-10">
        <!-- Header -->
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h2
                    class="flex justify-between items-center text-2xl text-gray-900 font-bold gap-1 mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                    <div class="flex items-center gap-1">
                        <Link :href="route('admin.users.index')" class="link-indigo-icon">
                        <CircleChevronLeft class="h-6 w-6" />
                        </Link>
                        <span>User Details</span>
                    </div>
                </h2>
                <p class="text-gray-700 text-sm mt-2">View detailed information for this user.</p>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="py-0">
            <div class="px-4 sm:px-0 mb-4">
                <h2 class="text-base text-gray-900 font-semibold">Basic Information</h2>
            </div>

            <div class="md:col-span-2 flex flex-col gap-y-6">
                <div class="bg-gray-50 shadow-md ring-1 ring-gray-900/5 sm:rounded-xl">
                    <div class="px-4 py-6 sm:p-8">
                        <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Name</label>
                                <p class="mt-2 text-gray-700">{{ user.name }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Email</label>
                                <p class="mt-2 text-gray-700">{{ user.email }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Role</label>
                                <p class="mt-2 text-gray-700 capitalize">{{ user.role }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Status</label>
                                <p class="mt-2 text-gray-700">{{ user.active ? 'Active' : 'Inactive' }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Phone</label>
                                <p class="mt-2 text-gray-700">{{ user.phone ?? '-' }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Address</label>
                                <p class="mt-2 text-gray-700">{{ user.address_data?.address ?? '-' }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Country</label>
                                <p class="mt-2 text-gray-700">{{ user.country?.name ?? '-' }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Created At</label>
                                <p class="mt-2 text-gray-700">{{ formatDate(user.created_at) }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Updated At</label>
                                <p class="mt-2 text-gray-700">{{ formatDate(user.updated_at) }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Email Verified</label>
                                <p class="mt-2 text-gray-700">{{ user.email_verified_at ?
                                    formatDate(user.email_verified_at) : 'Not Verified' }}</p>
                            </div>
                            <div class="sm:col-span-3">
                                <label class="text-gray-400 text-sm block font-medium">Notes</label>
                                <p class="mt-2 text-gray-700 whitespace-pre-line">{{ user.notes ?? '-' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wallet Info -->
        <div class="bg-white p-6 rounded shadow space-y-4">
            <h3 class="text-base font-semibold text-gray-900">Wallet</h3>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-700">
                <div class="bg-white border border-gray-200 shadow-sm rounded-lg p-4">
                    <div class="font-medium text-gray-500">Balance</div>
                    <div class="mt-1">${{ balance }}</div>
                </div>
                <div class="bg-white border border-gray-200 shadow-sm rounded-lg p-4">
                    <div class="font-medium text-gray-500">Total Deposits</div>
                    <div class="mt-1">${{ stats.total_deposits }}</div>
                </div>
                <div class="bg-white border border-gray-200 shadow-sm rounded-lg p-4">
                    <div class="font-medium text-gray-500">Total Withdrawals</div>
                    <div class="mt-1">${{ stats.total_withdrawals }}</div>
                </div>
                <div class="bg-white border border-gray-200 shadow-sm rounded-lg p-4">
                    <div class="font-medium text-gray-500">Pending Transactions</div>
                    <div class="mt-1">{{ stats.pending_transactions }}</div>
                </div>
            </div>

            <div>
                <h4 class="font-medium text-sm mt-4">Recent Transactions</h4>
                <table class="min-w-full divide-y divide-gray-200 mt-2">
                    <thead>
                        <tr>
                            <th
                                class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date</th>
                            <th
                                class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type</th>
                            <th
                                class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status</th>
                            <th
                                class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="txn in transactions" :key="txn.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ txn.date }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ txn.type }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ txn.status }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${{ txn.amount / 100 }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Cart Items -->
        <div v-if="cart_items.length" class="bg-white p-6 rounded shadow">
            <h3 class="text-base font-semibold text-gray-900 mb-4">In-Cart Items</h3>
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ID</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Price</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Website</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date Created</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="item in cart_items" :key="item.id">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ item.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${{ item.website.guest_post_price
                            }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${{ item.website.website_domain }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ formatDate(item.created_at) }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Advertiser Stats -->
        <div v-if="advertiser_order_stats && Object.keys(advertiser_order_stats).length"
            class="bg-white p-6 rounded shadow">
            <h3 class="text-base font-semibold text-gray-900 mb-2">Advertiser Order Stats</h3>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-700">
                <Link :href="orderLink()" class="card-link">Total Orders: {{ advertiser_order_stats.total }}</Link>
                <Link :href="orderLink('pending')" class="card-link">Pending: {{ advertiser_order_stats.pending }}
                </Link>
                <Link :href="orderLink('cancelled')" class="card-link">Cancelled: {{ advertiser_order_stats.cancelled }}
                </Link>
            </div>
        </div>

        <!-- Publisher Stats -->
        <div v-if="publisher_order_stats && Object.keys(publisher_order_stats).length"
            class="bg-white p-6 rounded shadow">
            <h3 class="text-base font-semibold text-gray-900 mb-2">Publisher Order Stats</h3>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-700">
                <div v-for="(count, key) in publisher_order_stats" :key="key" class="bg-gray-50 border p-4 rounded">
                    <div class="text-xs uppercase text-gray-500">{{ key.replaceAll('-', ' ') }}</div>
                    <div class="font-semibold text-lg">{{ count }}</div>
                </div>
            </div>
        </div>

        <!-- Publisher Websites -->
        <div v-if="websites.length" class="bg-white p-6 rounded shadow">
            <h3 class="text-base font-semibold text-gray-900 mb-4">Websites (Publisher) Displaying latest websites</h3>
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ID</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Domain</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="site in websites" :key="site.id">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ site.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ site.website_domain }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ site.active ? 'Active' :
                            'Inactive' }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <Link :href="route('admin.websites.edit', site.id)"
                                class="text-indigo-600 hover:text-indigo-900">Edit</Link>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="mt-4">
                <Link
                    :href="`/pb-admin/websites?outreach_status=all&page=1&perPage=10&searchTerm=${user.email}&sortField=id&sortOrder=desc&status=&verified=`"
                    class="text-indigo-600 hover:text-indigo-900">View All Websites</Link>
            </div>
        </div>

        <!-- Internal Notes -->
        <!-- <div class="bg-white p-6 rounded shadow">
            <h3 class="text-base font-semibold text-gray-900 mb-2">Internal Notes</h3>
            <p class="text-sm text-gray-700 whitespace-pre-line">
                {{ user.internal_note || 'No internal notes available.' }}
            </p>
        </div> -->
    </div>
</template>

<script setup>
import { Link, usePage } from '@inertiajs/vue3'
import { CircleChevronLeft } from 'lucide-vue-next'
import { format } from 'date-fns'

const {
    user,
    balance,
    stats,
    transactions,
    cart_items,
    advertiser_order_stats,
    publisher_order_stats,
    websites
} = usePage().props

const formatDate = (dateStr) => {
    return dateStr ? format(new Date(dateStr), 'PPP p') : '-'
}

const orderLink = (status = '') => {
    return route('admin.orders.index', {
        user_id: user.id,
        ...(status ? { orderStatus: status } : {})
    })
}
</script>

<style scoped>
.card-link {
    @apply block bg-gray-50 border hover:bg-gray-100 p-4 rounded transition;
}
</style>
