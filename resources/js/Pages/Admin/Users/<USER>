<template>
    <div class="mt-6">
        <!-- Header with toggle -->
        <div class="flex flex-row items-center justify-between bg-gray-100 p-4 rounded-md">
            <h2 class="text-lg font-semibold text-gray-800">Activity Logs
                <span class="badge-green ms-1">{{ logs?.total ?? 0 }}</span>
            </h2>
            <button type="button" class="btn-gray-selected" @click="showLogs = !showLogs">
                <ChevronUp v-if="showLogs" class="w-4 h-4" />
                <ChevronDown v-else class="w-4 h-4" />
            </button>
        </div>

        <!-- Loading indicator -->
        <div v-if="loading" class="flex items-center gap-2 text-gray-500 text-sm mt-2">
            <svg class="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
            </svg>
            Loading activity logs...
        </div>

        <!-- Logs table -->
        <div v-if="showLogs">
            <table v-if="logs.data?.length" class="mt-4 min-w-full divide-y divide-gray-200 border rounded shadow-sm">
                <thead class="bg-gray-50 text-sm">
                    <tr>
                        <th class="text-left px-4 py-2">ID</th>
                        <th class="text-left px-4 py-2">Description</th>
                        <th class="text-left px-4 py-2">Causer</th>
                        <th class="text-left px-4 py-2">Changes</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    <tr v-for="log in logs?.data || []" :key="log.id" class="hover:bg-gray-50">
                        <td class="px-4 py-2 text-sm">{{ log.id }}</td>
                        <td class="px-4 py-2 text-sm">{{ log.description }}</td>
                        <td class="px-4 py-2 text-sm">
                            <div>{{ log?.causer?.name || 'System / Unknown' }}</div>
                            <div class="text-xs text-gray-400">ID: {{ log?.causer?.id ?? 'N/A' }}</div>
                        </td>
                        <td class="px-4 py-2 text-xs whitespace-pre-wrap">
                            <div v-if="log?.properties?.attributes">
                                <div v-for="(val, key) in log.properties.attributes" :key="key" class="flex gap-1">
                                    <span class="font-medium">{{ key }}:</span>
                                    <span class="text-green-600">{{ val }}</span>
                                    <span v-if="log.properties.old?.[key]" class="text-gray-400">
                                        (was <span class="text-red-500">{{ log.properties.old[key] }}</span>)
                                    </span>
                                </div>
                            </div>
                            <div v-else>-</div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Pagination links -->
            <PaginationLinks v-if="logs.links?.length" :links="logs.links" class="mt-4" />

            <div v-else class="text-gray-500 text-sm mt-2">No activity logs found for this user.</div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, defineExpose } from 'vue'
import axios from 'axios'
import PaginationLinks from '@/Components/PaginationLinks.vue'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'

const props = defineProps({
    userId: { type: Number, required: true }
})

const showLogs = ref(false)
const logs = ref({ data: [], links: [], total: 0 })
const loading = ref(true)

async function fetchLogs(page = 1) {
    loading.value = true
    try {
        const res = await axios.get(route('admin.users.logs', { user: props.userId, page }))
        logs.value = res.data.props.logs
    } catch (e) {
        console.error('Failed to fetch logs', e)
    } finally {
        loading.value = false
    }
}

onMounted(() => fetchLogs())
defineExpose({ fetchLogs })

// Optional: Refetch when userId changes
watch(() => props.userId, () => fetchLogs())
</script>

<style scoped>
.badge-green {
    background-color: #d1fae5;
    color: #065f46;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
}
</style>
