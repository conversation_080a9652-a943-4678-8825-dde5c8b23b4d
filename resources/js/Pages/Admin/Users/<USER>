<template>
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">

            <h2
                class="flex justify-start text-2xl text-gray-900 font-bold gap-1 items-center mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                <Link :href="route('admin.users.index')" class="link-indigo-icon">
                <CircleChevronLeft class="h-6 w-6" />
                </Link>
                <span>
                    Update User
                </span>
            </h2>
            <p class="text-gray-700 text-sm mt-2">Update user information.</p>
        </div>
    </div>

    <form @submit.prevent="submit">
        <div class="divide-gray-900/10 divide-y">
            <div class="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-3 py-10">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base text-gray-900 font-semibold">User Information</h2>
                    <p class="text-gray-600 text-sm mt-1">Please complete all required fields before submitting.</p>
                </div>

                <div class="bg-gray-50 shadow-md md:col-span-2 ring-1 ring-gray-900/5 sm:rounded-xl">
                    <div class="px-4 py-6 sm:p-8">
                        <div class="grid grid-cols-1 gap-x-6 gap-y-8 max-w-2xl sm:grid-cols-6">
                            <!-- Name Field -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">Full name <span
                                        class="text-red-500">*</span></label>
                                <input v-model="form.name" type="text"
                                    class="field-name bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.name" class="text-red-500 text-sm">{{ form.errors.name }}</p>
                            </div>

                            <!-- Role Dropdown -->
                            <!-- <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">Role <span class="text-red-500">*</span></label>
                                <select v-model="form.role" class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm">
                                    <option value="admin">Admin</option>
                                    <option value="sales">Sales</option>
                                    <option value="finance">Finance</option>
                                    <option value="publisher">Publisher</option>
                                    <option value="advertiser">Advertiser</option>
                                    <option value="outreach">Outreach</option>
                                </select>
                                <p v-if="form.errors.role" class="text-red-500 text-sm">{{ form.errors.role }}</p>
                            </div> -->

                            <!-- Role Combobox -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium mb-2">Role <span
                                        class="text-red-500">*</span></label>
                                <BaseCombobox v-model="form.role" :items="roles" labelKey="name" valueKey="id"
                                    placeholder="Select a Role" class="border rounded-md border-gray-500" />
                                <p v-if="form.errors.role" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.role }}
                                </p>
                            </div>

                            <!-- Email Field -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">Email Address <span
                                        class="text-red-500">*</span></label>
                                <input v-model="form.email" type="email" autocomplete="off"
                                    class="field-email bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.email" class="text-red-500 text-sm">{{ form.errors.email }}</p>
                            </div>

                            <!-- Password Field -->
                            <div class="sm:col-span-3" :class="mode == 'create' ? '' : 'hidden'">
                                <label class="text-gray-900 text-sm block font-medium">Password
                                    <span class="text-green-600 text-xs italic"> - Leave blank for no change</span>
                                </label>
                                <input v-model="form.password" type="password" autocomplete="new-password"
                                    autocorrect="off" spellcheck="false" aria-autocomplete="none"
                                    class="field-password bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.password" class="text-red-500 text-sm">{{ form.errors.password }}
                                </p>
                            </div>

                            <!-- Phone Field -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">Phone</label>
                                <input v-model="form.phone" type="text"
                                    class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.phone" class="text-red-500 text-sm">{{ form.errors.phone }}</p>
                            </div>

                            <!-- Country Dropdown -->
                            <!-- <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">Country</label>
                                <select v-model="form.country_id" class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm">
                                    <option v-for="country in countries" :key="country.id" :value="country.id">
                                        {{ country.name }}
                                    </option>
                                </select>
                                <p v-if="form.errors.country_id" class="text-red-500 text-sm">{{ form.errors.country_id }}</p>
                            </div> -->

                            <!-- Country Combobox -->
                            <!-- <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium mb-2">Country</label>
                                <CountriesList v-model="form.country_id" :countries="props.countries"  class="border rounded-md border-gray-500"/>
                                <p v-if="form.errors.country_id" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.country_id }}
                                </p>
                            </div> -->

                            <!-- Country Selector -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium mb-2">Country</label>
                                <BaseCombobox v-model="form.country_id" :items="props.countries" labelKey="name"
                                    valueKey="id" placeholder="Select a Country"
                                    class="border rounded-md border-gray-500 " />
                                <p v-if="form.errors.country_id" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.country_id }}
                                </p>
                            </div>



                            <!-- Company Field -->
                            <div class="col-span-full">
                                <label class="text-gray-900 text-sm block font-medium">Company</label>
                                <input v-model="form.company" type="text"
                                    class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.company" class="text-red-500 text-sm">{{ form.errors.company }}</p>
                            </div>

                            <!-- Address Field -->
                            <div class="col-span-full">
                                <label class="text-gray-900 text-sm block font-medium">Street address</label>
                                <input v-model="form.address" type="text"
                                    class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.address" class="text-red-500 text-sm">{{ form.errors.address }}</p>
                            </div>

                            <!-- City Field -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">City</label>
                                <input v-model="form.city" type="text"
                                    class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.city" class="text-red-500 text-sm">{{ form.errors.city }}</p>
                            </div>

                            <!-- Postal Code Field -->
                            <div class="sm:col-span-3">
                                <label class="text-gray-900 text-sm block font-medium">ZIP / Postal Code</label>
                                <input v-model="form.postal_code" type="text"
                                    class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                <p v-if="form.errors.postal_code" class="text-red-500 text-sm">{{
                                    form.errors.postal_code }}</p>
                            </div>

                            <div class="sm:col-span-3">
                                <label class="inline-flex items-center cursor-pointer">
                                    <input v-model="form.email_verified" type="checkbox" class="sr-only peer">
                                    <div
                                        class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600">
                                    </div>
                                    <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Email
                                        Verified</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Buttons -->
            <div class="flex flex-row items-center justify-between">
                <div>
                    <div v-if="user && user.deleted_at" class="flex flex-row items-center gap-2">
                        <button type="button"
                            v-if="user && user.role != page.props.roleEnums.SuperAdmin && user.role != page.props.roleEnums.Admin"
                            @click="confirmRestore(user.id)" class="btn-green">
                            <RotateCcw class="size-4" />
                            <span>Restore User</span>
                        </button>
                        <!-- <button type="button"
                            v-if="user && user.role != page.props.roleEnums.SuperAdmin && user.role != page.props.roleEnums.Admin"
                            @click="confirmDelete(user.id)" class="hidden">
                            <Trash2 class="size-4" />
                            <span>Permanently Delete User</span>
                        </button> -->
                    </div>
                    <div v-if="user && !user.deleted_at">
                        <button type="button"
                            v-if="user && user.role != page.props.roleEnums.SuperAdmin && user.role != page.props.roleEnums.Admin"
                            @click="confirmTrash(user.id)" class="btn-red">
                            <Trash2 class="size-4" />
                            <span>Move To Trash</span>
                        </button>
                    </div>


                </div>

                <div class="flex justify-end gap-x-6 px-4 py-4 sm:px-8">
                    <button type="button" @click="$inertia.visit(route('admin.users.index'))"
                        class="text-gray-900 text-sm font-semibold">Cancel</button>
                    <button type="submit" class="btn-indigo" :disabled="form.processing">
                        <span v-if="form.processing">
                            <LoaderCircle class="h-5 w-5 animate-spin" />
                        </span>
                        <span v-if="!form.processing">
                            <Save class="h-5 w-5" />
                        </span>
                        <span>
                            {{ form.processing ? (props.mode === 'edit' ? "Saving..." : "Creating...") : (props.mode ===
                                'edit' ? "Save Changes" : "Create User") }}
                        </span>
                    </button>
                </div>
            </div>
            <div>
                <div v-if="props.mode === 'edit'">
                    <UserLogs :user-id="props.user.id" />
                </div>
            </div>
        </div>
    </form>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import { useForm, router } from "@inertiajs/vue3";
import { inject } from "vue";
// import CountriesList from '@/Components/CountriesList.vue'
import BaseCombobox from '@/Components/BaseCombobox.vue'
import UserLogs from './UserLogs.vue'

import {
    CircleChevronLeft,
    Save,
    LoaderCircle,
    Trash2,
    RotateCcw
} from 'lucide-vue-next';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    user: Object,
    countries: Array,
    mode: {
        type: String,
        default: 'create'
    }
});

const page = inject("page");

// ==========================================================
// Injects
// ==========================================================
const notify = inject("$notify");

// ==========================================================
// Form Initialization
// ==========================================================
const form = useForm({
    name: props.user?.name || "",
    email: props.user?.email || "",
    password: "", // optional field for create
    phone: props.user?.phone || "",
    role: props.user?.role || "advertiser",
    country_id: props.user?.country_id || "",
    company: props.user?.company || "",
    address: props.user?.address || "",
    city: props.user?.city || "",
    postal_code: props.user?.postal_code || "",
    email_verified: props.user?.email_verified_at ? true : false,
});

const roles = [
    // { id: page.props.roleEnums.SuperAdmin, name: "Super Admin" },
    { id: page.props.roleEnums.Admin, name: "Admin" },
    { id: page.props.roleEnums.Sales, name: "Sales" },
    { id: page.props.roleEnums.Finance, name: "Finance" },
    { id: page.props.roleEnums.Publisher, name: "Publisher" },
    { id: page.props.roleEnums.Advertiser, name: "Advertiser" },
    { id: page.props.roleEnums.Outreach, name: "Outreach" },
    { id: page.props.roleEnums.Writer, name: "Writer" },
];

// ==========================================================
// Helpers - Scroll To First Invalid Field
// ==========================================================
const scrollToField = (field) => {
    const el = document.querySelector(`.field-${field}`);
    if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
        setTimeout(() => el.focus(), 300);
    }
};

// ==========================================================
// Submit Handler
// ==========================================================
const submit = () => {
    const routeName = props.mode === 'edit'
        ? 'admin.users.update'
        : 'admin.users.store';

    if (props.mode === 'edit') {
        form.put(route(routeName, props.user?.id), {
            preserveScroll: true,
            onSuccess: () => {
                notify(
                    "User updated successfully!",
                    { type: "success" }
                );
            },
            onError: () => {
                const firstErrorKey = Object.keys(form.errors)[0];
                notify(form.errors[firstErrorKey], { type: "error" });
                scrollToField(firstErrorKey);
            }
        });
    } else {
        form.post(route(routeName), {
            preserveScroll: true,
            onSuccess: () => {
                notify(
                    "User created successfully!",
                    { type: "success" }
                );
            },
            onError: () => {
                const firstErrorKey = Object.keys(form.errors)[0];
                notify(form.errors[firstErrorKey], { type: "error" });
                scrollToField(firstErrorKey);
            }
        });
    }
};

// ==========================================================
// Methods - User Actions: Trash / Restore / Delete
// ==========================================================
const confirmTrash = (userId) => {
    if (confirm("Are you sure you want to move this user to trash?")) {
        router.delete(route("admin.users.destroy", userId), {}, {
            preserveScroll: true,
            onSuccess: () => notify("User moved to trash successfully!", { type: "success" }),
            onError: (errors) => alert(errors),
        });
    }
};

const confirmDelete = (userId) => {
    if (confirm("Are you sure you want to permanently delete this user?")) {
        router.post(route("admin.users.delete", userId), {}, {
            preserveScroll: true,
            onSuccess: () => notify("User deleted successfully!", { type: "success" }),
            onError: (errors) => alert(errors),
        });
    }
};

const confirmRestore = (userId) => {
    if (confirm("Are you sure you want to restore this user?")) {
        router.post(route("admin.users.restore", userId), {}, {
            preserveScroll: true,
            onSuccess: () => notify("User restored successfully!", { type: "success" }),
            onError: (errors) => alert(errors),
        });
    }
};
</script>
