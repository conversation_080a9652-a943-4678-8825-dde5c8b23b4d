<template>
    <div class="grid grid-cols-7 gap-x-4 items-center">


        <div class="col-span-3 relative flex items-center">

            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Search by Keyword</label>
                <div class="relative flex items-center">

                    <input v-model="form.keyword" type="text" placeholder="Type to search..."
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm pr-10" />

                    <!-- Clear button: Visible only when input has text -->
                    <button v-if="form.keyword" @click="clearSearch" class="absolute right-1 top-1/3  text-gray-400 hover:text-gray-600 flex items-center justify-center">
                        <XCircle class="w-5 h-5" />
                    </button>

                </div>
            </div>
        </div>
        <div class="col-span-1">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">User Roles</label>
                <div>
                    <select v-model="form.role" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                        <option value="">Show All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="sales">Sales</option>
                        <option value="finance">Finance</option>
                        <option value="publisher">Publisher</option>
                        <option value="advertiser">Advertiser</option>
                        <option value="outreach">Outreach</option>
                        <option value="writer">Writer</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Trash Status Filter -->
        <div class="col-span-1">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">User Status</label>
                <div>
                    <select v-model="form.trashed" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                        <option value="all">Show All</option>
                        <option value="active">Show Active</option>
                        <option value="trashed">Show Trashed</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Paginate Total -->
        <div class="col-span-1">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Display Users</label>
                <div>
                    <select v-model="form.perPage" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                        <option value="5">5 per page</option>
                        <option value="10">10 per page</option>
                        <option value="20">20 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import { useForm } from "@inertiajs/vue3";
import { router } from "@inertiajs/vue3";
import { watch } from "vue";
import debounce from "lodash/debounce";
import { XCircle } from 'lucide-vue-next';

// ==========================================================
// Props - Sync Form with Incoming Props
// ==========================================================
const props = defineProps({
    keyword: String,
    role: String,
    perPage: String,
    trashed: String,
});

// ==========================================================
// Form Initialization - Sync Form with Incoming Props
// ==========================================================
const form = useForm({
    keyword: props.keyword || "",
    role: props.role || "",
    perPage: props.perPage || "10",
    trashed: props.trashed || "all",
});

// ==========================================================
// Helpers - Clear Search
// ==========================================================
const clearSearch = () => {
    form.keyword = "";
};

// ==========================================================
// Methods - Apply Filters
// ==========================================================
const applyFilters = debounce(() => {
    const query = {
        keyword: form.keyword.trim() || undefined,
        role: form.role.trim() || undefined,
        trashed: form.trashed.trim() || undefined,
        perPage: form.perPage,
    };

    router.get(route("admin.users.index"), query, {
        preserveState: true,
        preserveScroll: true,
    });
}, 300);

// ==========================================================
// Watchers - Apply Filters
// ==========================================================
watch(() => form.keyword, applyFilters);
watch(() => form.role, applyFilters);
watch(() => form.perPage, applyFilters);
watch(() => form.trashed, applyFilters);
</script>
