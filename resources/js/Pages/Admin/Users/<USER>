<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h2 class="title-1">
                    Users
                </h2>
                <p class="subtitle-1">
                    A list of all the users in your account including their name, title, email, and role.
                </p>
                <div class="text-sm text-gray-500">
                    Displaying {{ showingCount }} of {{ users.total }} users
                </div>
            </div>
            <div class="mt-4 sm:flex-none sm:ml-16 sm:mt-0">
                <Link :href="route('admin.users.create')" class="btn-indigo">
                <UserPlus class="h-5 w-5" />
                <span>Add User</span>
                </Link>
            </div>
        </div>
        <div class="mt-5">
            <Filters :keyword="filters?.keyword" :role="filters?.role" />
        </div>


        <div class="border-b border-gray-200 flow-root mt-8">
            <div class="-mx-4 -my-2 lg:-mx-8 overflow-x-auto sm:-mx-6">
                <div class="align-middle inline-block lg:px-8 min-w-full py-2 sm:px-6">
                    <table v-if="users.data.length" class="divide-gray-300 divide-y min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="text-gray-900  text-sm px-3 py-3.5 relative sm:pr-0 text-start">Edit</th>
                                <th @click="sort('id')" scope="col" class="text-gray-900 text-left text-sm cursor-pointer font-semibold px-3 py-3.5">
                                    ID
                                    <ChevronsUp v-if="filters.sortField === 'id'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortDirection === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                                </th>
                                <th @click="sort('name')" scope="col" class="text-gray-900 text-left text-sm cursor-pointer font-semibold px-3 py-3.5">
                                    Name
                                    <ChevronsUp v-if="filters.sortField === 'name'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortDirection === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                                </th>
                                <th @click="sort('email')" scope="col" class="text-gray-900 text-left text-sm cursor-pointer font-semibold px-3 py-3.5">
                                    Email
                                    <ChevronsUp v-if="filters.sortField === 'email'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortDirection === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                                </th>
                                <th @click="sort('role')" scope="col" class="text-gray-900 text-left text-sm cursor-pointer font-semibold px-3 py-3.5">
                                    Role
                                    <ChevronsUp v-if="filters.sortField === 'role'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortDirection === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                                </th>
                                <!-- <th scope="col" class="text-gray-900  text-sm px-3 py-3.5 relative sm:pr-0">Delete</th> -->
                            </tr>
                        </thead>

                        <tbody class="bg-white divide-gray-200 divide-y">
                            <tr v-for="user in users.data" :key="user.id" :class="{ 'bg-red-50': user.deleted_at }">

                                <td class="text-sm font-medium pr-4 py-5">
                                    <div class="flex  relative">
                                        <Link :href="route('admin.users.edit', user.id)" class="flex text-indigo-600 gap-1 hover:text-indigo-900 items-center whitespace-nowrap">
                                        <UserPen class="size-4" />
                                        <span>Edit</span>
                                        <span class="sr-only">, {{ user.name }}</span>
                                        </Link>
                                    </div>
                                </td>
                                <td class="text-sm py-5 sm:pl-0 ">
                                    <div class="text-gray-900 font-medium px-3">{{ user.id }}</div>
                                </td>
                                <td class="text-sm py-5 sm:pl-0 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="px-3">
                                            <div class="text-gray-900 font-medium">

                                                <Link v-if="user.role == roleEnums.Advertiser || user.role == roleEnums.Publisher" :href="route('admin.users.show', user.id)" class="text-indigo-600">
                                                {{ user.name }}
                                                </Link>
                                                <span v-else>
                                                    {{ user.name }}
                                                </span>

                                                <span v-if="user.deleted_at" class="ms-2 inline-flex items-center rounded-md bg-red-400/10 px-2 py-1 text-xs font-medium text-red-400 ring-1 ring-inset ring-red-400/20">
                                                    Trash
                                                </span>
                                            </div>
                                            <div class="flex text-gray-500 gap-2 items-center mt-1">

                                                <span v-if="user.phone" class="flex gap-1 items-center">
                                                    <Phone class="h-3 w-3" />
                                                    {{ user.phone }}
                                                </span>
                                                <span v-if="user.country" class="flex gap-2 items-center">

                                                    - {{ user.country?.name }}
                                                </span>

                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-gray-500 text-sm px-3 py-5 whitespace-nowrap">{{ user.email }}</td>
                                <td class="text-sm px-3 py-5 whitespace-nowrap">
                                    <span :class="['px-2 py-1 rounded text-white text-xs font-semibold', roleClass(user.role)]">
                                        {{ formatRole(user.role) }}
                                    </span>
                                </td>

                                <!-- <td class="text-sm font-medium pl-3 pr-4 py-5">
                                    <div class="flex justify-center relative">
                                        <button v-if="user.role !== 'admin'" @click="confirmDelete(user.id)" class="flex text-red-600 gap-1 hover:text-red-900 items-center whitespace-nowrap">
                                            <Trash2 class="size-4" />
                                            <span>Delete</span>
                                        </button>
                                    </div>
                                </td> -->
                            </tr>
                        </tbody>
                    </table>
                    <div v-if="!users.data.length" class="flex justify-center gap-2 my-4">
                        <NoDataMessage message1="Sorry, no results were found." message2="Try adjusting your filters." />
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination Component -->
        <PaginationLinks v-if="users.data.length" :links="users.links" />
    </div>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import PaginationLinks from '@/Components/PaginationLinks.vue';
import Filters from "./Filters.vue";
import { useForm, router } from "@inertiajs/vue3";
import {
    UserPen, UserPlus, Phone,
    OctagonAlert, ChevronsUp, ChevronsUpDown,
    Trash2
} from 'lucide-vue-next';
import { inject, computed } from "vue";
import { usePage } from '@inertiajs/vue3';

import NoDataMessage from '@/Components/NoDataMessage.vue';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    users: Object,
    filters: Object
});

// ==========================================================
// Refs & Injects
// ==========================================================
const notify = inject("$notify");

// ==========================================================
// Computed (None required)
// ==========================================================

const page = computed(() => {
    const queryPage = usePage().url.split('?')[1]?.split('&').find(param => param.startsWith('page='));
    return queryPage ? Number(queryPage.split('=')[1]) : 1;
});

const pageProps = inject("page");

const roleEnums = pageProps.props.roleEnums;

const showingCount = computed(() => {
    const perPage = props.users.per_page || 10; // fallback to 10
    const currentPage = page.value || 1;
    const itemsOnPage = props.users.data.length;

    const totalShown = (currentPage - 1) * perPage + itemsOnPage;
    return totalShown > props.users.total ? props.users.total : totalShown;
});

// ==========================================================
// Watchers (None required)
// ==========================================================

// ==========================================================
// Methods - Sorting
// Handle table column sorting with direction toggle
// ==========================================================
const sort = (field) => {
    const direction = props.filters.sortField === field &&
        props.filters.sortDirection === 'asc' ? 'desc' : 'asc';

    router.get(route('admin.users.index'), {
        ...props.filters,
        sortField: field,
        sortDirection: direction
    }, {
        preserveState: true,
        preserveScroll: true
    });
};

// ==========================================================
// Methods - Delete User
// Confirm and delete a non-admin user
// ==========================================================
const confirmDelete = (userId) => {
    if (confirm("Are you sure you want to delete this user?")) {
        router.post(route("admin.users.delete", userId), {}, {
            preserveScroll: true,
            onSuccess: () => notify("User deleted successfully!", { type: "success" }),
            onError: (errors) => alert(errors),
        });
    }
};

// ==========================================================
// Helpers
// Style badge color based on role
// Format role label with first-letter capitalization
// ==========================================================
function roleClass(role) {
    switch (role) {
        case roleEnums.SuperAdmin: return 'bg-red-600';
        case roleEnums.Admin: return 'bg-red-600';
        case roleEnums.Publisher: return 'bg-orange-600';
        case roleEnums.Advertiser: return 'bg-green-600';
        case roleEnums.Sales: return 'bg-blue-600';
        case roleEnums.Finance: return 'bg-amber-900';
        case roleEnums.Outreach: return 'bg-purple-900';
        default: return 'bg-gray-500';
    }
}

function formatRole(role) {
    return role.charAt(0).toUpperCase() + role.slice(1);
}

// ==========================================================
// Initialize Sort Defaults (fallbacks in case of missing values)
// ==========================================================
props.filters.sortField = props.filters.sortField || 'id';
props.filters.sortDirection = props.filters.sortDirection || 'desc';
</script>
