<template>
    <div>
  <!--       <div class="lg:flex lg:items-center lg:justify-between">
            <div class="min-w-0 flex-1">

                <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    {{ capitalizedRole }} Dashboard
                </h2>
                <p class="subtitle-1">Displaying stats for the selected date range.</p>

            </div>
        </div> -->
        <div class="flex flex-col gap-y-8 mt-8">


            <div class="relative isolate overflow-hidden  bg-white rounded-xl border">
                <!-- Secondary navigation -->
                <header class="pb-4 pt-6 sm:pb-6 bg-white">
                    <div class="mx-auto flex  flex-wrap items-center gap-6 px-4 sm:flex-nowrap sm:px-6 lg:px-8">
                        <h1 class="text-base font-semibold leading-7 text-zinc-700">Sales</h1>

                        <DateRangeFilterAdmin :preset-range="filters.preset_range" :start-date="filters.start_date"
                            :end-date="filters.end_date" @update:filters="onFiltersChange" />

                        <a href="#"
                            class="hidden whitespace-nowrap ml-auto items-center gap-x-1 rounded-md bg-orange-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-orange-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600">
                            <svg class="-ml-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path
                                    d="M10.75 6.75a.75.75 0 00-1.5 0v2.5h-2.5a.75.75 0 000 1.5h2.5v2.5a.75.75 0 001.5 0v-2.5h2.5a.75.75 0 000-1.5h-2.5v-2.5z" />
                            </svg>
                            New Order
                        </a>
                    </div>


                </header>

                <div class="border-b border-b-gray-900/10 lg:border-t rounded lg:border-t-gray-900/5">
                    <dl class="mx-auto grid  grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 lg:px-2 xl:px-0">
                        <div
                            class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 xl:px-8">
                            <dt class="text-sm font-medium leading-6 text-gray-500">Sales</dt>
                            <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                                ${{ orderStats.sales }}</dd>
                        </div>
                        <div
                            class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 xl:px-8 ">
                            <dt class="text-sm font-medium leading-6 text-gray-500">Publisher Payments</dt>
                            <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                                ${{ orderStats.publisher_payments }}</dd>
                        </div>
                        <div
                            class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 xl:px-8 ">
                            <dt class="text-sm font-medium leading-6 text-gray-500">Profit</dt>
                            <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                                ${{ orderStats.profit }}</dd>
                        </div>
                        <div
                            class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 xl:px-8 ">
                            <dt class="text-sm font-medium leading-6 text-gray-500">Outstanding Payments</dt>
                            <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                                ${{ orderStats.outstanding_payments }}</dd>
                        </div>
                        <div
                            class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 xl:px-8 ">
                            <dt class="text-sm font-medium leading-6 text-gray-500">Withdrawals</dt>
                            <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                                ${{ orderStats.withdrawals }}</dd>
                        </div>
                    </dl>
                </div>

                <div class="absolute left-0 top-full -z-10 mt-96 origin-top-left translate-y-40 -rotate-90 transform-gpu opacity-20 blur-3xl sm:left-1/2 sm:-ml-96 sm:-mt-10 sm:translate-y-0 sm:rotate-0 sm:transform-gpu sm:opacity-50"
                    aria-hidden="true">
                    <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]"
                        style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)">
                    </div>
                </div>
            </div>



            <div>

                <h3 class="text-base font-semibold text-gray-900 pb-6">Orders</h3>

                <div v-if="orderStats" class="overflow-hidden rounded-xl">
                    <div
                        class="border-b border-b-gray-900/10 lg:border-t lg:border-t-gray-900/5 bg-white rounded-xl border ">
                        <dl class="mx-auto grid  grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 lg:px-2 xl:px-0 ">
                            <div @click="goToLink('pending')"
                                class="cursor-pointer stats-admin-block badge-pending flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10 sm:px-6 lg:b xl:px-8 ">
                                <dt class="text-sm  leading-6  pb-2 font-semibold tracking-light">Pending</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.pending }}</dd>
                            </div>
                            <div @click="goToLink('inprogress')"
                                class="cursor-pointer stats-admin-block badge-inprogress flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4  px-4 py-10 sm:px-6  xl:px-8 ">
                                <dt class=" text-sm  leading-6 pb-2 font-semibold tracking-light">In Progress</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.inprogress }}</dd>
                            </div>
                            <div @click="goToLink('late')"
                                class="cursor-pointer stats-admin-block badge-late flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10  xl:px-8 ">
                                <dt class="text-sm  leading-6 pb-2 font-semibold tracking-light">Late</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.late }}</dd>
                            </div>
                            <div @click="goToLink('delivered')"
                                class="cursor-pointer stats-admin-block badge-delivered flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10  xl:px-8 ">
                                <dt class="text-sm  leading-6 pb-2 font-semibold tracking-light">Delivered</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.delivered }}</dd>
                            </div>
                            <div @click="goToLink('completed')"
                                class="cursor-pointer stats-admin-block badge-completed flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10 sm:px-6 xl:px-8">
                                <dt class="text-sm  leading-6 pb-2 font-semibold tracking-light">Completed</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.completed }}</dd>
                            </div>
                            <div @click="goToLink('cancelled')"
                                class="cursor-pointer stats-admin-block badge-cancelled flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10 sm:px-6 xl:px-8">
                                <dt class="text-sm  leading-6 pb-2 font-semibold tracking-light">Cancelled</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.cancelled }}</dd>
                            </div>
                            <div @click="goToLink('refunded')"
                                class="cursor-pointer stats-admin-block badge-refunded flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10 sm:px-6 xl:px-8">
                                <dt class="text-sm  leading-6 pb-2 font-semibold tracking-light">Refunded</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.refunded }}</dd>
                            </div>
                            <div @click="goToLink('')"
                                class="cursor-pointer stats-admin-block  flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 px-4 py-10 sm:px-6  xl:px-8">
                                <dt class="text-sm  leading-6 pb-2 font-semibold tracking-light">Total Orders</dt>
                                <dd
                                    class="w-full flex-none text-3xl font-medium leading-10 tracking-tight stats-block-number">
                                    {{ orderStats.total }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
               
            </div>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { computed } from 'vue';
import { usePage, router } from "@inertiajs/vue3";
import DateRangeFilterAdmin from '@/Components/DateRangeFilterAdmin.vue';

// ==========================================================
// Props 
// ==========================================================
const props = defineProps({
    filters: Object,
    orderStats: {
        type: Object,
        default: () => ({}),
    },
});


// ==========================================================
// Refs & Injects
// ==========================================================
const page = usePage(); // Accessing global page props


// ==========================================================
// Format Status Label
// ==========================================================
function formatStatusLabel(status) {
    return status === 'total'
        ? 'Total Orders'
        : status.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
}

// ==========================================================
// Badge Class
// ==========================================================
// function getBadgeClass(status) {
//     return {
//         total: 'badge-blue',
//         pending: 'badge-yellow',
//         inprogress: 'badge-gray',
//         delivered: 'badge-green',
//         completed: 'badge-indigo',
//         late: 'badge-orange',
//         cancelled: 'badge-red',
//         refunded: 'badge-slate',
//     }[status] || 'badge-gray';
// }

// ==========================================================
// Order Status Links
// ==========================================================
function goToLink(orderStatus) {
    const baseParams = {
        orderStatus,
        perPage: props.filters.perPage || 10,
        searchTerm: props.filters.searchTerm || '',
        sortField: props.filters.sortField || 'id',
        sortOrder: props.filters.sortOrder || 'desc',
        preset_range: props.filters.preset_range || 'show_all',
        start_date: props.filters.start_date || '',
        end_date: props.filters.end_date || '',
    };

    router.get(route('admin.orders.index'), baseParams, {
        preserveState: true,
        preserveScroll: true,
    });
}

// ==========================================================
// Filter Change Handler
// ==========================================================
function onFiltersChange(updatedFilters) {
    const mergedFilters = {
        ...props.filters, // existing filters like searchTerm, perPage, etc.
        ...updatedFilters, // new date filters
    };

    router.get(route('admin.dashboard'), mergedFilters, {
        preserveState: true,
        preserveScroll: true,
    });
}

// ==========================================================
// Computed - Capitalize Role
// ==========================================================
const role = page.props.auth?.user?.role; // Current user role from auth

const capitalizedRole = computed(() => {
    if (!role) return 'x';
    return role.charAt(0).toUpperCase() + role.slice(1); // Capitalize first letter
});

</script>


<style lang="scss" scoped></style>