<template>
    <div>
        <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            
            Your Outreach Stats</h2>
        <p class="text-sm text-gray-500 mt-1">Based on selected date range</p>

        <div class="my-4">
            <DateRangeFilter :preset-range="filters.preset_range" :start-date="filters.start_date" :end-date="filters.end_date" @update:filters="onFiltersChange" />
        </div>

        <div class="mt-8">
            <h3 class="text-base text-gray-900">Displaying stats for the selected date range.</h3>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                <div class="stats-block badge-yellow">
                    <div class="stats-block-number">{{ stats.inprogress }}</div>
                    <div class="stats-block-label">In Progress</div>
                </div>
                <div class="stats-block badge-green">
                    <div class="stats-block-number">{{ stats.onboarded }}</div>
                    <div class="stats-block-label">Onboarded</div>
                </div>
               
                <div class="stats-block badge-gray">
                    <div class="stats-block-number">{{ stats.rejected }}</div>
                    <div class="stats-block-label">Rejected</div>
                </div>
                <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow-md border sm:p-6">
                    <div class="text-3xl font-bold text-gray-800">{{ stats.total }}</div>
                    <div class="text-sm text-gray-500">Total Websites</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { router } from '@inertiajs/vue3';
import DateRangeFilter from '@/Components/DateRangeFilter.vue';

const props = defineProps({
    filters: Object,
    stats: Object,
});

const onFiltersChange = (newFilters) => {
    router.get(route('admin.dashboard'), newFilters, {
        preserveScroll: true,
        preserveState: true,
    });
};
</script>
