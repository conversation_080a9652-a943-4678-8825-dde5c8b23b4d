<template>
    <div class="min-h-screen">
        <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900">
                    Withdrawal Requests
                </h2>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    ID
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    User
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Amount
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Date
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-if="withdrawals.length === 0">
                                <td colspan="6" class="px-6 py-8 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <OctagonAlert class="text-red-500 w-10 h-10 my-3" />
                                        <p class="text-lg font-medium text-red-500">
                                            No withdrawal requests found
                                        </p>
                                        <p class="text-sm text-gray-500 mt-1">
                                            There are currently no withdrawal requests to display
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <tr v-else v-for="withdrawal in withdrawals.data" :key="withdrawal.id"
                                class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                    #{{ withdrawal.id }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ withdrawal.user?.name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                    ${{ withdrawal.amount }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                                        {
                                            'bg-yellow-50 text-yellow-700 ring-1 ring-yellow-600/20': withdrawal.status === 'pending',
                                            'bg-green-50 text-green-700 ring-1 ring-green-600/20': withdrawal.status === 'approved',
                                            'bg-red-50 text-red-700 ring-1 ring-red-600/20': withdrawal.status === 'rejected'
                                        }
                                    ]">
                                        {{ withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ withdrawal.created_at }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                    <Link :href="route('admin.withdrawals.show', withdrawal.id)"
                                        class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    View Details
                                    </Link>

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import { OctagonAlert } from 'lucide-vue-next';

const props = defineProps({
    withdrawals: {
        type: Array,
        default: () => []
    }
});



</script>