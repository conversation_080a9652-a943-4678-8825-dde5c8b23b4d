<template>
    <div>
        <div class="sm:flex sm:items-start">
            <div class="sm:flex-auto">
                <h2 class="flex justify-start text-2xl text-gray-900 font-bold gap-1 items-center mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                    <Link :href="route('admin.payments.index')" class="link-indigo-icon">
                    <CircleChevronLeft class="h-6 w-6" />
                    </Link>
                    <span>
                        Payment Details
                    </span>
                </h2>

                <div>
                    <div class="ms-8 mt-4 ">
                        <div class="text-sm">
                            <div class="flex flex-row gap-2 ">
                                <span class="font-bold">Order By:</span>
                                <Link :href="route('admin.users.edit', payment.user.id)" class="text-blue-600 hover:underline">
                                <span>{{ payment.user?.name }}</span>
                                </Link>
                            </div>
                            <div class="flex flex-row gap-2 ">
                                <span class="font-bold">Advertiser Email:</span>
                                <span>{{ payment.user?.email }}</span>
                            </div>
                            <div class="flex flex-row gap-2 ">
                                <span class="font-bold">Time Elapsed:</span>
                                <span>{{ payment.order.time_elapsed }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <div class="flex flex-col gap-y-12 mt-8 border p-4 rounded-lg">
            <!-- create gird with four columns and in each column create a card with the order details -->
            <div class="grid grid-cols-3 gap-0 rounded-lg">
                <div class="overflow-hidden bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Payment ID:</span>
                        <span>{{ payment.id }}</span>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold">Stripe PI:</span>
                        <span>{{ payment.external_transaction_id }}</span>
                    </div>
                </div>
                <div class="overflow-hidden bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Amount:</span>
                        <span>${{ payment.payment_amount }}</span>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold">Status:</span>
                        <span :class="statusClass(payment.status)">
                            {{ formatStatus(payment.status) }}
                        </span>
                    </div>
                </div>

                <div class="overflow-hidden bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Date:</span>
                        <span>{{ payment.created_at_formatted }}</span>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold"></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">

                    <div class="sm:col-span-2">
                        <dt class="text-base font-semibold text-gray-900">Comments</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ payment.internal_comment }}</dd>
                    </div>

                    <div v-if="payment.order" class="sm:col-span-2">
                        <dt class="text-base font-semibold text-gray-900">Order Details</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <div class="flex flex-row gap-2">
                                <div class="flex items-center gap-1">
                                    <span class="font-bold">Order ID:</span>
                                    <Link :href="route('admin.orders.details', payment.order.id)" class="text-blue-600 hover:underline">
                                    {{ payment.order.id }}
                                    </Link>
                                </div>
                                <div class="flex items-center gap-1" v-if="payment.order.order_items_count !== undefined">
                                    <span class="font-bold">Items:</span>
                                    <span>{{ payment.order.order_items_count }}</span>
                                </div>
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import { Link } from '@inertiajs/vue3';
import { CircleChevronLeft } from 'lucide-vue-next';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    payment: Object,
});

// ==========================================================
// Methods - Status Formatting
// Return CSS class and formatted label based on payment status
// ==========================================================
const statusClass = (status) => {
    switch (status) {
        case 'paid': return 'text-green-600';
        case 'pending': return 'text-yellow-600';
        case 'refunded': return 'text-red-500';
        case 'partially_refunded': return 'text-orange-500';
        case 'credit_applied': return 'text-blue-500';
        default: return 'text-gray-500';
    }
};

function formatStatus(status) {
    return status
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}
</script>
