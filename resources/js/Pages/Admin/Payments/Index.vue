<template>
    <div>
        <div class="sm:flex sm:items-start ">
            <div class="sm:flex-auto ">
                <h2 class="title-1">Payments</h2>
                <p class="subtitle-1">
                    A list of all payments made, including user info, amount, and status.
                </p>
                <div class="text-sm text-gray-500">
                    Displaying {{ showingCount }} of {{ payments.total }} payments
                </div>
            </div>
            <div class="pe-5 text-xs border px-3 py-2 rounded-md bg-gray-50 flex flex-col gap-1 shadow-lg">
                <div class="flex flex-row items-center gap-2 border-b border-gray-200 pb-1">
                    <span class="font-bold">Paid:</span>
                    <span>${{ totals.paid ?? 0 }}</span>
                </div>
                <div class="flex flex-row items-center gap-2 border-b border-gray-200 pb-1">
                    <span class="font-bold">Pending:</span>
                    <span>${{ totals.pending ?? 0 }}</span>
                </div>
                <div class="flex flex-row items-center gap-2 border-b border-gray-200 pb-1">
                    <span class="font-bold">Refunded:</span>
                    <span>${{ totals.refunded ?? 0 }}</span>
                </div>
                <div class="flex flex-row items-center gap-2 border-b border-gray-200 pb-1">
                    <span class="font-bold">Partially Refunded:</span>
                    <span>${{ totals.partially_refunded ?? 0 }}</span>
                </div>
                <div class="flex flex-row items-center gap-2 border-b border-gray-200 pb-1">
                    <span class="font-bold">Credit Applied:</span>
                    <span>${{ totals.credit_applied ?? 0 }}</span>
                </div>
                
                <div class="flex flex-row items-center gap-2">
                    <span class="font-bold">Total:</span>
                    <span>${{ totals.total ?? 0 }}</span>
                </div>
            </div>
        </div>

        <div class="mt-5">
            <Filters :search="filters?.search" :status="filters?.status" :amount="filters?.amount" :preset-range="filters?.preset_range" :start-date="filters?.start_date" :end-date="filters?.end_date"
                :sort-by="filters?.sort_by" :sort-dir="filters?.sort_dir" />
        </div>

        <div class="border-b border-gray-200 flow-root mt-8">
            <div class="-mx-4 -my-2 lg:-mx-8 overflow-x-auto sm:-mx-6">
                <div class="align-middle inline-block lg:px-8 min-w-full py-2 sm:px-6">
                    <table v-if="payments.data.length" class="divide-gray-300 divide-y min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Details
                                </th>
                               
                                <th @click="sort('id')" class="whitespace-nowrap  cursor-pointer px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    ID
                                    <ChevronsUp v-if="filters.sort_by === 'id'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sort_dir === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th @click="sort('external_transaction_id')" class="whitespace-nowrap  cursor-pointer px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Stripe PI
                                    <ChevronsUp v-if="filters.sort_by === 'external_transaction_id'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sort_dir === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>


                                <th @click="sort('user_id')" class="whitespace-nowrap  cursor-pointer px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    User
                                    <ChevronsUp v-if="filters.sort_by === 'user_id'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sort_dir === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th @click="sort('payment_amount')" class="whitespace-nowrap  cursor-pointer px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Amount
                                    <ChevronsUp v-if="filters.sort_by === 'payment_amount'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sort_dir === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th @click="sort('status')" class="whitespace-nowrap  cursor-pointer px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Status
                                    <ChevronsUp v-if="filters.sort_by === 'status'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sort_dir === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th @click="sort('created_at')" class="whitespace-nowrap  cursor-pointer px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Date
                                    <ChevronsUp v-if="filters.sort_by === 'created_at'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sort_dir === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Comments
                                </th>
                                
                            </tr>
                        </thead>

                        <tbody class="bg-white divide-gray-200 divide-y">
                            <tr v-for="payment in payments.data" :key="payment.id">
                                <td class="px-3 py-4 text-sm text-gray-900">
                                    <Link :href="route('admin.payments.show', payment.id)" class="text-indigo-600 hover:underline flex flex-row gap-1 justify-center items-center">
                                    <Eye class="w-4 h-4 " />
                                    <span>Details</span>
                                    </Link>
                                </td>
                                <td class="px-3 py-4 text-sm text-gray-900">{{ payment.id }}</td>
                                <td class="px-3 py-4 text-sm text-gray-900">
                                    {{ payment.external_transaction_id }}
                                </td>

                                <td class="px-3 py-4 text-sm text-gray-900">
                                    <div>
                                        <div>{{ payment.user?.name }}</div>
                                        <div class="text-gray-500 text-xs">{{ payment.user?.email }}</div>
                                    </div>

                                    <div class="flex flex-row gap-2 text-xs text-gray-500 mt-1" v-if="payment.order">
                                        <div class="flex items-center gap-1">
                                            <span class="font-bold">Order ID:</span>
                                            <Link :href="route('admin.orders.details', payment.order.id)" class="text-blue-600 hover:underline">
                                            {{ payment.order.id }}
                                            </Link>
                                        </div>
                                        <div class="flex items-center gap-1" v-if="payment.order.order_items_count !== undefined">
                                            <span class="font-bold">Items:</span>
                                            <span>{{ payment.order.order_items_count }}</span>
                                        </div>
                                    </div>
                                </td>

                                <td class="px-3 py-4 text-sm text-gray-900">${{ payment.payment_amount }}</td>
                                <td class="px-3 py-4 text-sm">
                                    <span :class="statusClass(payment.status)">
                                        {{ formatStatus(payment.status) }}
                                    </span>
                                </td>
                                <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap">{{ payment.created_at_formatted }}</td>
                                <td class="px-3 py-4 text-sm text-gray-900">
                                    <span>
                                        {{ truncatedComments[payment.id] ?? truncate(payment.internal_comment) }}
                                    </span>
                                    <span v-if="!truncatedComments[payment.id] && payment?.internal_comment?.length > 42">
                                        <a href="#" @click.prevent="showFullComment(payment.id, payment.internal_comment)" class="text-blue-600 hover:underline">more...</a>
                                    </span>
                                </td>
                                
                            </tr>
                        </tbody>
                    </table>

                    <div v-if="!payments.data.length">
                        <NoDataMessage message1="No payment records found." message2="Try adjusting your filters." />
                    </div>
                </div>
            </div>
        </div>

        <PaginationLinks v-if="payments.data.length" :links="payments.links" />
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { router } from '@inertiajs/vue3';
import { reactive, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import Filters from './Filters.vue';
import PaginationLinks from '@/Components/PaginationLinks.vue';
import { OctagonAlert, ChevronsUp, ChevronsUpDown, Eye } from 'lucide-vue-next';
import { usePage } from '@inertiajs/vue3';
import NoDataMessage from '@/Components/NoDataMessage.vue';
// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    payments: Object,
    filters: Object,
    totals: Object,
});

// get query params page
const page = computed(() => {
    const queryPage = usePage().url.split('?')[1]?.split('&').find(param => param.startsWith('page='));
    return queryPage ? Number(queryPage.split('=')[1]) : 1;
});

const showingCount = computed(() => {
    const perPage = 15; // hardcoded because you did paginate(15) in controller
    const currentPage = page.value || 1;
    const itemsOnPage = props.payments.data.length;

    const totalShown = (currentPage - 1) * perPage + itemsOnPage;
    return totalShown > props.payments.total ? props.payments.total : totalShown;
});

// ==========================================================
// Refs & Reactive State
// Stores expanded comment text per payment ID
// ==========================================================
const truncatedComments = reactive({});

// ==========================================================
// Methods - Sorting
// Handles column sorting direction and triggers reload
// ==========================================================
const sort = (field) => {
    const direction =
        props.filters.sort_by === field && props.filters.sort_dir === 'asc'
            ? 'desc'
            : 'asc';

    router.get(route('admin.payments.index'), {
        ...props.filters,
        sort_by: field,
        sort_dir: direction,
    }, {
        preserveState: true,
        preserveScroll: true,
    });
};

// ==========================================================
// Methods - Status Helpers
// Returns tailwind classes and formatted label for statuses
// ==========================================================
const statusClass = (status) => {
    switch (status) {
        case 'paid': return 'text-green-600';
        case 'pending': return 'text-yellow-600';
        case 'refunded': return 'text-red-500';
        case 'partially_refunded': return 'text-orange-500';
        case 'credit_applied': return 'text-blue-500';
        default: return 'text-gray-500';
    }
};

function formatStatus(status) {
    return status
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

// ==========================================================
// Methods - Comment Display
// Truncates long comment and allows toggle to full
// ==========================================================
function truncate(text, length = 42) {
    if (!text) return '';
    return text.length > length ? text.slice(0, length) + '...' : text;
}

function showFullComment(id, fullText) {
    truncatedComments[id] = fullText;
}
</script>
