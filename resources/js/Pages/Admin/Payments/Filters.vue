<template>
    <div class="grid grid-cols-12 gap-x-4 items-center">
        <div class="col-span-4">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Search by Keyword</label>
                <div>
                    <input v-model="form.search" type="text" placeholder="Search by user, email, order ID, or amount..."
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm pr-10" />
                </div>
            </div>
        </div>

        <div class="col-span-2">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Payment Status</label>
                <div>
                    <select v-model="form.status" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                        <option value="">All Status</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                        <option value="refunded">Refunded</option>
                        <option value="partially_refunded">Partially Refunded</option>
                        <option value="credit_applied">Credit Applied</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-span-2">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Filter by Date</label>
                <div>
                    <select v-model="form.preset_range" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                        <option value="show_all">Show All</option>
                        <option value="custom">Custom Range</option>
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="last_7_days">Last 7 Days</option>
                        <option value="last_30_days">Last 30 Days</option>
                        <option value="last_90_days">Last 90 Days</option>
                        <option value="last_12_months">Last 12 Months</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-span-2" v-if="form.preset_range === 'custom'">
            <input type="date" v-model="form.start_date" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
        </div>
        <div class="col-span-2" v-if="form.preset_range === 'custom'">
            <input type="date" v-model="form.end_date" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
        </div>
    </div>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import { useForm, router } from '@inertiajs/vue3';
import { watch } from 'vue';
import debounce from 'lodash/debounce';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    search: String,
    status: String,
    presetRange: String,
    startDate: String,
    endDate: String,
    sortBy: String,
    sortDir: String,
});

// ==========================================================
// Form State
// Reactive form using useForm for payment filters
// ==========================================================
const form = useForm({
    search: props.search || '',
    status: props.status || '',
    preset_range: props.presetRange || 'show_all',
    start_date: props.startDate || '',
    end_date: props.endDate || '',
});

// ==========================================================
// Methods - Apply Filters
// Sends GET request to apply filters and reload the table
// ==========================================================
const applyFilters = debounce(() => {
    // Clear date range if "Show All" is selected
    if (form.preset_range === 'show_all') {
        form.start_date = '';
        form.end_date = '';
    }

    router.get(route('admin.payments.index'), {
        ...form,
        sort_by: props.sortBy,
        sort_dir: props.sortDir,
    }, {
        preserveState: true,
        preserveScroll: true,
    });
}, 300);

// ==========================================================
// Watchers
// Triggers applyFilters on form field changes
// ==========================================================
watch(() => form.search, applyFilters);
watch(() => form.status, applyFilters);
watch(() => form.preset_range, applyFilters);
watch(() => form.start_date, applyFilters);
watch(() => form.end_date, applyFilters);
</script>
