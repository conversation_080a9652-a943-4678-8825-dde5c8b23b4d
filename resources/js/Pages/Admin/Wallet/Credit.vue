<template>
    <div class="min-h-screen">
        <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900">
                    Credit To Wallet
                </h2>
            </div>

            <!-- Credit Form -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <form @submit.prevent="submitCredit" class="space-y-6">
                    <!-- User Selection -->
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Select User
                        </label>
                        <select id="user_id" v-model="form.user_id" :class="[
                            'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm',
                            formErrors.user_id ? 'border-red-300' : ''
                        ]" required>
                            <option value="">Select a user to credit</option>
                            <option v-for="user in users.data" :key="user.id" :value="user.id">
                                {{ user.name }} ({{ user.email }}) - Balance: ${{ user.wallet_balance }}
                            </option>
                        </select>
                        <p v-if="formErrors.user_id" class="mt-1 text-sm text-red-600">
                            {{ formErrors.user_id }}
                        </p>
                    </div>

                    <!-- Amount Input -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Credit Amount ($)
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input id="amount" v-model="form.amount" type="number" step="0.01" min="0.01" max="10000"
                                :class="[
                                    'block w-full pl-7 pr-12 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm',
                                    formErrors.amount ? 'border-red-300' : ''
                                ]" placeholder="0.00" required />
                        </div>
                        <p v-if="formErrors.amount" class="mt-1 text-sm text-red-600">
                            {{ formErrors.amount }}
                        </p>
                        <p class="mt-1 text-sm text-gray-500">
                            Minimum: $0.01 | Maximum: $10,000
                        </p>
                    </div>

                    <!-- Reason Input -->
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Reason for Credit
                        </label>
                        <textarea id="reason" v-model="form.reason" rows="4" :class="[
                            'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm',
                            formErrors.reason ? 'border-red-300' : ''
                        ]" placeholder="Enter the reason for this credit (e.g., Refund, Bonus, Compensation, etc.)"
                            required></textarea>
                        <p v-if="formErrors.reason" class="mt-1 text-sm text-red-600">
                            {{ formErrors.reason }}
                        </p>
                        <p class="mt-1 text-sm text-gray-500">
                            Please provide a clear reason for this credit (3-500 characters)
                        </p>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" :disabled="isSubmitting"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            {{ isSubmitting ? 'Processing...' : 'Credit Wallet' }}
                        </button>
                    </div>
                </form>
            </div>

            <!-- Recent Credits Table -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        Recent Credits
                    </h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    User
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Amount
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Reason
                                </th>
                                <th scope="col"
                                    class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    Date
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-if="recentCredits.length === 0">
                                <td colspan="4" class="px-6 py-8 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <svg class="text-gray-400 w-12 h-12 mb-3" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                            </path>
                                        </svg>
                                        <p class="text-lg font-medium text-gray-500">
                                            No recent credits
                                        </p>
                                        <p class="text-sm text-gray-400 mt-1">
                                            Credits will appear here once processed
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <tr v-else v-for="credit in recentCredits" :key="credit.id"
                                class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ credit.user_name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                    ${{ credit.amount }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {{ credit.reason }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ credit.created_at }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useForm } from '@inertiajs/vue3';

const props = defineProps({
    users: {
        type: Object,
        default: () => ({ data: [] })
    },
    recentCredits: {
        type: Array,
        default: () => []
    }
});

const form = useForm({
    user_id: '',
    amount: '',
    reason: ''
});

const formErrors = reactive({
    user_id: '',
    amount: '',
    reason: ''
});

const isSubmitting = ref(false);

const submitCredit = () => {
    // Clear previous errors
    Object.keys(formErrors).forEach(key => {
        formErrors[key] = '';
    });

    // Basic validation
    let hasErrors = false;

    if (!form.user_id) {
        formErrors.user_id = 'Please select a user to credit.';
        hasErrors = true;
    }

    if (!form.amount || parseFloat(form.amount) <= 0) {
        formErrors.amount = 'Please enter a valid amount greater than $0.';
        hasErrors = true;
    } else if (parseFloat(form.amount) > 10000) {
        formErrors.amount = 'The maximum credit amount is $10,000.';
        hasErrors = true;
    }

    if (!form.reason || form.reason.trim().length < 3) {
        formErrors.reason = 'Please provide a reason (at least 3 characters).';
        hasErrors = true;
    } else if (form.reason.trim().length > 500) {
        formErrors.reason = 'The reason cannot exceed 500 characters.';
        hasErrors = true;
    }

    if (hasErrors) {
        return;
    }

    isSubmitting.value = true;

    form.post(route('admin.wallet.credit.store'), {
        onSuccess: () => {
            // Reset form on success
            form.reset();
            isSubmitting.value = false;
        },
        onError: (errors) => {
            // Handle validation errors
            Object.keys(errors).forEach(key => {
                if (formErrors.hasOwnProperty(key)) {
                    formErrors[key] = errors[key];
                }
            });
            isSubmitting.value = false;
        },
        onFinish: () => {
            isSubmitting.value = false;
        }
    });
};
</script>

<style lang="scss" scoped></style>