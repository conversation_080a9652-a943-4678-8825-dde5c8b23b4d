<template>
    <div>
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Refund Request Details
            </h2>
            <div class="flex space-x-4">
                <button v-if="refund.status === 'pending'" @click="approveRefund"
                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                    Approve Refund
                </button>
                <!-- <button v-if="refund.status === 'pending'" @click="rejectRefund"
                    class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700">
                    Reject Refund
                </button> -->
            </div>
        </div>
    </div>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Refund Details -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Refund Information</h3>
                            <dl class="space-y-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Refund ID</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ refund.id }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1">
                                        <span :class="[
                                            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                            {
                                                'bg-yellow-100 text-yellow-800': refund.status === 'pending',
                                                'bg-green-100 text-green-800': refund.status === 'approved',
                                                'bg-red-100 text-red-800': refund.status === 'rejected'
                                            }
                                        ]">
                                            {{ refund.status }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Amount</dt>
                                    <dd class="mt-1 text-sm text-gray-900">${{ orderItem.price_paid }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Requested At</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ refund.created_at }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- User Information -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                            <dl class="space-y-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ user.name || 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ user.email || 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Reason -->
                    <div class="mt-6 bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Refund Reason</h3>
                        <p class="text-sm text-gray-900">{{ refund.reason }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { router } from '@inertiajs/vue3';

const props = defineProps({
    refund: {
        type: Object,
        required: true
    },
    user: {
        type: Object,
        required: true
    },
    orderItem: {
        type: Object,
        required: true
    }
});

const approveRefund = () => {
    if (confirm('Are you sure you want to approve this refund request?')) {
        router.post(route('admin.refunds.approve', props.refund.id));
    }
};

const rejectRefund = () => {
    if (confirm('Are you sure you want to reject this refund request?')) {
        router.post(route('admin.refunds.reject', props.refund.id));
    }
};
</script>