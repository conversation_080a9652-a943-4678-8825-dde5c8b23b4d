<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h2 class="title-1 flex items-center gap-1">
                    <Link :href="route('admin.websites.index')" class="link-indigo-icon">
                    <CircleChevronLeft class="h-6 w-6" />
                    </Link>
                    <span>Bulk Import Websites</span>
                </h2>
                <p class="subtitle-1">
                    Bulk import websites. Copy and paste the list of websites into the text area below.
                </p>
            </div>
        </div>

        <div class="mt-4 space-y-4">
            <div class="mb-4">
                <nav class="flex border-b">
                    <button class="border px-3 py-2 bg-gray-50" :class="tab === 'textarea' ? 'border-b-2 font-bold text-indigo-600' : ''" @click="switchTab('textarea')">
                        Copy/Paste URLs
                    </button>
                    <button class="border px-3 py-2 bg-gray-50 ml-4" :class="tab === 'csv' ? 'border-b-2 font-bold text-indigo-600' : ''" @click="switchTab('csv')">
                        Upload CSV File
                    </button>
                </nav>


            </div>

            <div v-if="tab === 'textarea'">


                <label class="block text-sm font-medium text-gray-700">Each website on a new line</label>
                <!-- Existing textarea for bulk domains -->
                <textarea v-model="websites" class="w-full h-96 p-4 border border-gray-300 rounded-md" placeholder="https://example.com"></textarea>

                <div v-if="successMessage" class="text-green-600 font-medium">
                    {{ successMessage }}
                </div>

                <div v-if="errorMessage" class="text-red-600 font-medium">
                    {{ errorMessage }}
                </div>

                <button @click="importWebsites" class="btn-indigo flex items-center gap-2 mt-2">
                    <Import class="h-4 w-4" />
                    <span>
                        Import Websites
                    </span>
                </button>
            </div>

            <div v-else>
                <input type="file" @change="updateCSVSelection" accept=".csv" />

                <div v-if="successMessage" class="text-green-600 font-medium mt-2">
                    {{ successMessage }}
                </div>
                <div v-if="errorMessage" class="text-red-600 font-medium mt-2">
                    {{ errorMessage }}
                </div>
                <button @click="handleCsvUpload" class="btn-indigo flex items-center gap-2 mt-2">Upload CSV</button>
                <div class="mt-10 border-t pt-4 flex items-center gap-2">
                    <span>Download CSV Template:</span>

                    <a target="_blank" href="/files/domain/domain-bulk-import.csv" class="link-indigo flex items-center gap-2">
                        <span>domain-bulk-import.csv</span>
                        <Download class="h-4 w-4" />
                    </a>
                </div>
            </div>


        </div>

    </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import { usePage, router } from '@inertiajs/vue3'
import axios from 'axios'
import { CircleChevronLeft, Import, Download } from 'lucide-vue-next'

const websites = ref('')
const successMessage = ref('')
const errorMessage = ref('')

const tab = ref('textarea')

const notify = inject("$notify");




// Pull flash success message if redirected after import
onMounted(() => {
    successMessage.value = usePage().props.flash?.success || ''



})

const importWebsites = () => {
    axios.post(route('admin.websites.store-import'), {
        websites: websites.value,
    })
        .then(response => {
            successMessage.value = response.data.message
            errorMessage.value = ''
            websites.value = ''
            notify(response.data.message, { type: "success" });

        })
        .catch(error => {
            errorMessage.value = 'Something went wrong.'
            successMessage.value = ''

            notify('Something went wrong.', { type: "success" });

        })
}


const formData = new FormData()

const updateCSVSelection = (event) => {
    const file = event.target.files[0]
    if (!file) {
        notify("No file selected.", { type: "error" });

        return
    }

    // Reset and append file
    formData.delete('csv')
    formData.append('csv', file)
}


const switchTab = (selectedTab) => {
    tab.value = selectedTab
    successMessage.value = ''
    errorMessage.value = ''
}


const handleCsvUpload = () => {
    if (!formData.has('csv')) {
        notify("Please select a CSV file before uploading.", { type: "error" });
        return
    }

    axios.post(route('admin.websites.import-csv'), formData)
        .then(response => {
            successMessage.value = response.data.message
            notify(response.data.message, { type: "success" });

            errorMessage.value = ''
            formData.delete('csv') // Clear after upload
        })
        .catch(error => {
            errorMessage.value = error.response?.data?.message || 'CSV import failed.'
            notify(error.response?.data?.message || 'CSV import failed.', { type: "success" });

            successMessage.value = ''
        })
}

</script>