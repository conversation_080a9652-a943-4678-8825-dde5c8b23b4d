<template>
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h2
                class="flex justify-between items-center text-2xl text-gray-900 font-bold gap-1 mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                <div class="flex items-center gap-1">
                    <Link :href="route(is_publisher ? 'publisher.websites.index' : 'admin.websites.index')"
                        class="link-indigo-icon">
                    <CircleChevronLeft class="h-6 w-6" />
                    </Link>
                    <span>
                        {{ props.mode === 'edit' ? "Update Website" : "Add New Website" }}
                    </span>
                </div>
                <TooltipProvider v-if="props.mode === 'edit' && website?.active && !is_publisher">
                    <Tooltip>
                        <TooltipTrigger as-child>
                            <button type="button" @click="generateSignedUrl"
                                class="inline-flex items-center px-3 py-1.5 border border-indigo-600 shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <Link class="h-4 w-4 mr-2" />
                                Generate Signed URL
                            </button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Generate and copy signed URL</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </h2>
            <p class="text-gray-700 text-sm mt-2">
                {{ props.mode === 'edit' ?
                    "Please complete`all required fields before submitting."
                    : "Enter website details to create a new listing." }}
            </p>
        </div>
    </div>
    <div>
        <div v-if="props.mode === 'edit'" class="flex flex-col gap-y-4 mt-8 border p-4 rounded-lg">

            <div class="bg-gray-100 p-4 rounded-lg text-2xl text-center font-bold">
                {{ form.website_domain }}
            </div>
            <!-- create gird with four columns and in each column create a card with the order details -->
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-0 rounded-lg">
                <div class="overflow-hidden bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Organic Traffic:</span>
                        <span>
                            {{ abbreviateNumber(website?.seo_stats?.ahref_organic_traffic, 1) }}

                        </span>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold">Semrush DR:</span>
                        <span>{{ website?.seo_stats?.ahref_domain_rank }}


                        </span>
                    </div>
                </div>
                <div class="overflow-hidden  bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold whitespace-nowrap">Top Country:</span>
                        <div class="flex flex-row gap-2" v-if="top_traffic_country">
                            <span> {{ top_traffic_country?.name + " - " +
                                abbreviateNumber(website?.seo_stats?.top_country_traffic, 1) ?? 0 }} </span>
                        </div>
                        <div v-else>-</div>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold">Total Backlinks:</span>
                        <span>{{ abbreviateNumber(website?.seo_stats?.total_backlinks_count, 2) ?? 0 }}</span>
                    </div>

                </div>

                <div class="overflow-hidden  bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Language:</span>
                        <span>{{ website?.language?.name }}</span>
                    </div>
                    <div class="px-4 py-2 flex flex-row gap-2 text-sm">
                        <span class="font-bold">Category:</span>
                        <span>{{ website?.category?.category }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <form @submit.prevent="submit">
        <div class="divide-gray-900/10 divide-y">


            <div class="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-3 py-10">
                <div class="px-4 sm:px-0">

                    <h2 class="text-base text-gray-900 font-semibold">Website Information</h2>
                    <!-- <p class="text-sm text-gray-500">Please complete all required fields before submitting.</p> -->
                </div>


                <div class="md:col-span-2 flex flex-col gap-y-6">

                    <div class="bg-gray-50 shadow-md   md:col-span-2 ring-1 ring-gray-900/5 sm:rounded-xl">
                        <div class="px-4 py-6 sm:p-8">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 max-w-2xl sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Website Domain <span
                                            class="text-red-500">*</span></label>

                                    <template v-if="is_publisher && props.mode === 'edit'">
                                        <div class="mt-2 text-gray-900 text-xl">{{ form.website_domain }}</div>
                                        <div class="text-xs text-gray-400 mt-3 flex items-center gap-1">
                                            <span class="flex items-center gap-1">
                                                <Lock class="w-3 h-3" />
                                            </span>
                                            <span>Domain name is not editable.</span>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <input v-model="form.website_domain" type="text" placeholder="example.com"
                                            class="field-website_domain bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                        <p v-if="form.errors.website_domain" class="text-red-500 text-sm">{{
                                            form.errors.website_domain }}</p>
                                    </template>

                                </div>

                                <div v-if="props.mode === 'edit'" class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Status</label>

                                    <div class="my-2 p-1 px-3 border rounded-md w-full text-sm">
                                        <span
                                            :class="`${props.website?.active ? 'text-green-500 font-bold' : 'text-red-500 font-bold'}`">
                                            {{
                                                props.website?.active ? 'Onboarded' : "Inactive" }}</span>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div v-if="props.mode === 'edit' && !is_publisher"
                class="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-3 py-10">
                <div class="px-4 sm:px-0">
                    <h2 v-if="props.mode === 'edit' && !is_publisher" class="text-base text-gray-900 font-semibold">
                        Assigned Publisher</h2>
                    <h2 v-else class="text-base text-gray-900 font-semibold">Status</h2>
                    <!-- <p class="text-sm text-gray-500">Please complete all required fields before submitting.</p> -->
                </div>


                <div class="md:col-span-2 flex flex-col gap-y-6">

                    <div class="bg-gray-50 shadow-md   md:col-span-2 ring-1 ring-gray-900/5 sm:rounded-xl">
                        <div class="px-4 py-6 sm:p-8">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 max-w-2xl sm:grid-cols-6">
                                <div v-if="props.mode === 'edit' && !is_publisher" class="sm:col-span-3 ">
                                    <label class="text-gray-900 text-sm block font-medium mb-2">
                                        Assigned Publisher
                                    </label>

                                    <div class="bg-white  p-1 px-2 rounded-md border border-gray-600">

                                        <template v-if="form.publisher_user_id === 0 || !publisherInfo">
                                            <button @click="openAssignPublisherDialog" type="button"
                                                class=" text-indigo-600 hover:underline w-full text-start">
                                                Assign Publisher
                                            </button>
                                        </template>

                                        <template v-else>
                                            <button @click="openAssignPublisherDialog" type="button"
                                                class="text-start text-indigo-600 hover:underline">
                                                <div class="font-medium text-gray-600 flex items-center gap-1">
                                                    <Check class="w-3 h-3" /> {{ publisherInfo?.name }}
                                                </div>
                                                <div class="text-xs text-gray-500">{{ publisherInfo?.email }} - ID: {{
                                                    publisherInfo?.id }}</div>
                                            </button>
                                        </template>
                                    </div>
                                </div>



                                <!-- 
                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Mark as Onboarded
                                    </label>
                                    <label class="inline-flex items-center cursor-pointer pt-6">
                                        <input type="checkbox" class="sr-only peer" :checked="form.active === 1" @change="handleToggleComplete" />
                                        <div
                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                        </div>
                                        <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                                            {{ form.active === 1 ? 'Onboarded' : 'Inactive' }}
                                        </span>
                                    </label>
                                </div> -->

                            </div>
                        </div>
                    </div>

                </div>
            </div>


            <div class="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-3 py-10">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base text-gray-900 font-semibold">Niche Pricing</h2>
                </div>


                <div class="md:col-span-2 flex flex-col gap-y-6">

                    <div class="bg-gray-50 shadow-md   md:col-span-2 ring-1 ring-gray-900/5 sm:rounded-xl">
                        <div class="px-4 py-6 sm:p-8">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 max-w-2xl sm:grid-cols-6">

                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Guest Post Price ($) <span class="text-red-500">*</span>
                                    </label>
                                    <div class="flex flex-row gap-2 items-center">
                                        <input v-model="form.guest_post_price" type="number" min="0"
                                            placeholder="Enter Price in USD"
                                            class="field-guest_post_price w-full block mt-2 px-3 py-1.5 sm:text-sm rounded-md text-base focus:outline-indigo-600"
                                            :class="[
                                                'bg-white text-gray-900 outline-gray-300',
                                                validationErrors.guest_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.guest_post_price" class="text-red-500 text-sm">{{
                                        form.errors.guest_post_price }}</p>
                                </div>

                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Link Insert Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">

                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>
                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.link_insert_price"
                                                            @change="disablePrice('link_insert_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>

                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Link Insert Price</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>


                                        <input v-model="form.link_insert_price" type="number" min="0"
                                            :disabled="!priceToggles.link_insert_price" placeholder="Enter Price in USD"
                                            class="field-link_insert_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.link_insert_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.link_insert_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.link_insert_price" class="text-red-500 text-sm">{{
                                        form.errors.link_insert_price }}</p>
                                </div>

                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Crypto Post Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">

                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>
                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.crypto_post_price"
                                                            @change="disablePrice('crypto_post_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Crypto Post Price</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                        <input v-model="form.crypto_post_price" type="number" min="0"
                                            :disabled="!priceToggles.crypto_post_price" placeholder="Enter Price in USD"
                                            class="field-crypto_post_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.crypto_post_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.crypto_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.crypto_post_price" class="text-red-500 text-sm">{{
                                        form.errors.crypto_post_price }}</p>
                                </div>


                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Casino Post Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">

                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>
                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.casino_post_price"
                                                            @change="disablePrice('casino_post_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Casino Post Price</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>

                                        <input v-model="form.casino_post_price" type="number" min="0"
                                            :disabled="!priceToggles.casino_post_price" placeholder="Enter Price in USD"
                                            class="field-casino_post_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.casino_post_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.casino_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.casino_post_price" class="text-red-500 text-sm">{{
                                        form.errors.casino_post_price }}</p>
                                </div>



                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Adult Post Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>

                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.adult_post_price"
                                                            @change="disablePrice('adult_post_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>

                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Adult Post Price</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                        <input v-model="form.adult_post_price" type="number" min="0"
                                            :disabled="!priceToggles.adult_post_price" placeholder="Enter Price in USD"
                                            class="field-adult_post_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.adult_post_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.adult_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.adult_post_price" class="text-red-500 text-sm">{{
                                        form.errors.adult_post_price }}</p>
                                </div>


                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Finance Post Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>

                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.finance_post_price"
                                                            @change="disablePrice('finance_post_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Finance Post Price</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>

                                        <input v-model="form.finance_post_price" type="number" min="0"
                                            :disabled="!priceToggles.finance_post_price"
                                            placeholder="Enter Price in USD"
                                            class="field-finance_post_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.finance_post_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.finance_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.finance_post_price" class="text-red-500 text-sm">{{
                                        form.errors.finance_post_price }}</p>
                                </div>



                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Dating Post Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">


                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>

                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.dating_post_price"
                                                            @change="disablePrice('dating_post_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Dating Post Price</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>

                                        <input v-model="form.dating_post_price" type="number" min="0"
                                            :disabled="!priceToggles.dating_post_price" placeholder="Enter Price in USD"
                                            class="field-dating_post_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.dating_post_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.dating_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.dating_post_price" class="text-red-500 text-sm">{{
                                        form.errors.dating_post_price }}</p>
                                </div>



                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">CBD Post Price ($) <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex flex-row gap-2 items-center">

                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>
                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            :checked="priceToggles.cbd_post_price"
                                                            @change="disablePrice('cbd_post_price')" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                    </label>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable CBD Post Price </p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>

                                        <input v-model="form.cbd_post_price" type="number" min="0"
                                            :disabled="!priceToggles.cbd_post_price" placeholder="Enter Price in USD"
                                            class="field-cbd_post_price bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"
                                            :class="[
                                                'bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm',
                                                !priceToggles.cbd_post_price && 'opacity-40 cursor-not-allowed',
                                                validationErrors.cbd_post_price ? 'border border-red-500' : 'border border-gray-300'
                                            ]" />
                                    </div>
                                    <p v-if="form.errors.cbd_post_price" class="text-red-500 text-sm">{{
                                        form.errors.cbd_post_price
                                        }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-3 py-10">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base text-gray-900 font-semibold">Publishing</h2>
                </div>


                <div class="md:col-span-2 flex flex-col gap-y-6">

                    <div class="bg-gray-50 shadow-md   md:col-span-2 ring-1 ring-gray-900/5 sm:rounded-xl">
                        <div class="px-4 py-6 sm:p-8">
                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 max-w-2xl sm:grid-cols-6">
                                <div class="sm:col-span-6">
                                    <label class="text-gray-900 text-sm block font-medium">Example Post URL </label>
                                    <div class="flex flex-row items-center justify-center gap-2">

                                        <input v-model="form.example_post_url" type="text"
                                            placeholder="https://www.example.com"
                                            class="field-example_post_url bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                        <a :href="form.example_post_url" target="_blank" class="text-indigo-500">
                                            <ExternalLink class="w-6 h-6" />
                                        </a>
                                    </div>
                                    <p v-if="form.errors.example_post_url" class="text-red-500 text-sm">{{
                                        form.errors.example_post_url }}</p>
                                </div>
                                <div class="sm:col-span-6">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Site Requirements
                                    </label>
                                    <textarea v-model="form.site_requirements" rows="4"
                                        placeholder="Enter site requirements"
                                        class="field-site_requirements bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"></textarea>
                                    <p v-if="form.errors.site_requirements" class="text-red-500 text-sm">{{
                                        form.errors.site_requirements }}</p>
                                </div>

                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Turnaround Publishing Time
                                        (Days) <span class="text-red-500">*</span></label>
                                    <input v-model="form.turn_around_time_in_days" type="number" min="0"
                                        placeholder="Number of days required for publishing"
                                        class="field-turn_around_time_in_days bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm" />
                                    <p v-if="form.errors.turn_around_time_in_days" class="text-red-500 text-sm">{{
                                        form.errors.turn_around_time_in_days }}</p>
                                </div>

                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">Article Validity
                                        (Months)</label>
                                    <div class="flex flex-row items-center gap-2">
                                        <input v-model="form.article_validity_in_months" type="number" min="0" :max="36"
                                            :disabled="articleValidityToggle" @input="limitArticleValidity"
                                            placeholder="Number of Months"
                                            class="field-article_validity_in_months bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm transition-opacity duration-300"
                                            :class="{ 'opacity-40 pointer-events-none': articleValidityToggle }" />



                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger as-child>
                                                    <label class="inline-flex items-center cursor-pointer mt-2">
                                                        <input type="checkbox" class="sr-only peer"
                                                            v-model="articleValidityToggle" />
                                                        <div
                                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                                        </div>
                                                        <span
                                                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Unlimited</span>
                                                    </label>

                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Enable or Disable Article Validity in Months </p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                    <p v-if="form.errors.article_validity_in_months" class="text-red-500 text-sm">{{
                                        form.errors.article_validity_in_months }}</p>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>


            <div class="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-3 py-10">
                <div class="px-4 sm:px-0">
                    <h2 class="text-base text-gray-900 font-semibold">Data</h2>
                </div>


                <div class="md:col-span-2 flex flex-col gap-y-6">

                    <div class="bg-gray-50 shadow-md   md:col-span-2 ring-1 ring-gray-900/5 sm:rounded-xl">
                        <div class="px-4 py-6 sm:p-8">

                            <div class="grid grid-cols-1 gap-x-6 gap-y-8 max-w-2xl sm:grid-cols-6">
                                <!-- Category Dropdown -->
                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Site Category
                                    </label>
                                    <!-- <select v-model="form.main_category_id"
                                        class="field-main_category_id bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm">
                                        <option disabled value="">Select a category</option>
                                        <option v-for="category in categories" :key="category.id" :value="category.id">
                                            {{ category.category }}
                                        </option>
                                    </select> -->

                                    <!-- {{ categories }} -->

                                    <div class="relative w-full">
                                        <BaseCombobox v-model="form.main_category_id" :items="props.categories"
                                            labelKey="category" valueKey="id" placeholder="Select a Category"
                                            class="border rounded-md border-gray-500 mt-2 " />
                                    </div>

                                    <p v-if="form.errors.main_category_id" class="text-red-500 text-sm">{{
                                        form.errors.main_category_id }}</p>
                                </div>


                                <!-- Language Dropdown -->
                                <div class="sm:col-span-3 ">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Site Language
                                    </label>
                                    <!-- <select v-model="form.site_language_id"
                                        class="field-site_language_id bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm">
                                        <option disabled value="">Select a category</option>
                                        <option v-for="language in languages" :key="language.id" :value="language.id">
                                            {{ language.name }}
                                        </option>
                                    </select> -->
                                    <div class="relative w-full">
                                        <BaseCombobox v-model="form.site_language_id" :items="props.languages"
                                            labelKey="name" valueKey="id" placeholder="Select a Language"
                                            class="border rounded-md border-gray-500 mt-2 " />
                                    </div>

                                    <p v-if="form.errors.site_language_id" class="text-red-500 text-sm">{{
                                        form.errors.site_language_id }}</p>
                                </div>




                                <!-- Link Relation Dropdown -->
                                <div class="sm:col-span-3">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Link Relation
                                    </label>
                                    <select v-model.number="form.link_relation"
                                        class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm">
                                        <option value="dofollow">DoFollow</option>
                                        <option value="nofollow">NoFollow</option>
                                        <option value="sponsored">Sponsored</option>

                                    </select>
                                    <p v-if="form.errors.link_relation" class="text-red-500 text-sm">{{
                                        form.errors.link_relation }}
                                    </p>
                                </div>

                                <div class="sm:col-span-3">
                                </div>
                                <!-- indexed_article Dropdown -->
                                <div class="sm:col-span-2">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Indexed Article
                                    </label>
                                    <label class="inline-flex items-center cursor-pointer py-2 mt-2">
                                        <input type="checkbox" class="sr-only peer"
                                            :checked="form.indexed_article === 1"
                                            @change="form.indexed_article = $event.target.checked ? 1 : 0" />
                                        <div
                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                        </div>
                                        <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                                            {{ form.indexed_article === 1 ? 'Yes' : 'No' }}
                                        </span>
                                    </label>
                                    <p v-if="form.errors.indexed_article" class="text-red-500 text-sm mt-1">{{
                                        form.errors.indexed_article }}</p>
                                </div>



                                <!-- sponsorship_label Dropdown -->
                                <div class="sm:col-span-2">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Sponsorship Label
                                    </label>
                                    <label class="inline-flex items-center cursor-pointer py-2 mt-2">
                                        <input type="checkbox" class="sr-only peer"
                                            :checked="form.sponsorship_label === 1"
                                            @change="form.sponsorship_label = $event.target.checked ? 1 : 0" />
                                        <div
                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                        </div>
                                        <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                                            {{ form.sponsorship_label === 1 ? 'Yes' : 'No' }}
                                        </span>
                                    </label>
                                    <p v-if="form.errors.sponsorship_label" class="text-red-500 text-sm mt-1">{{
                                        form.errors.sponsorship_label }}</p>
                                </div>



                                <!-- homepage_visible Dropdown -->
                                <div class="sm:col-span-2">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Homepage Visible
                                    </label>
                                    <label class="inline-flex items-center cursor-pointer py-2 mt-2">
                                        <input type="checkbox" class="sr-only peer"
                                            :checked="form.homepage_visible === 1"
                                            @change="form.homepage_visible = $event.target.checked ? 1 : 0" />
                                        <div
                                            class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600 dark:peer-checked:bg-green-600">
                                        </div>
                                        <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                                            {{ form.homepage_visible === 1 ? 'Yes' : 'No' }}
                                        </span>
                                    </label>
                                    <p v-if="form.errors.homepage_visible" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.homepage_visible }}
                                    </p>
                                </div>

                                <!-- Topics -->
                                <div class="sm:col-span-6">
                                    <label
                                        class="text-gray-900 text-sm block font-medium mb-2 flex flex-row gap-2 items-center">
                                        <span>Topics</span><span class="text-gray-400">|</span><span
                                            class="text-gray-400 text-xs">Type comma or hit enter to create new
                                            topic</span>
                                    </label>

                                    <Combobox :multiple="true" v-model="selectedTopics" class="">
                                        <ComboboxAnchor class="w-full">
                                            <div class="relative w-full items-center">
                                                <ComboboxInput v-model="topicInput" @keydown="handleTopicKeydown"
                                                    @input="handleTopicTyping" :display-value="() => topicInput"
                                                    class="pl-9 w-full bg-white rounded-md border border-gray-500" />
                                                <span
                                                    class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
                                                    <Search class="size-4 text-muted-foreground" />
                                                </span>
                                            </div>
                                        </ComboboxAnchor>

                                        <ComboboxList v-if="availableTopics.length">
                                            <ComboboxEmpty>No topic found.</ComboboxEmpty>
                                            <ComboboxGroup>
                                                <ComboboxItem v-for="topic in availableTopics" :key="topic.name"
                                                    :value="topic.name">
                                                    {{ topic.name }}
                                                    <ComboboxItemIndicator>
                                                        <Check class="ml-auto h-4 w-4" />
                                                    </ComboboxItemIndicator>
                                                </ComboboxItem>

                                            </ComboboxGroup>
                                        </ComboboxList>
                                    </Combobox>


                                    <!-- Display selected tags -->
                                    <div class="flex flex-wrap mt-2 gap-2">
                                        <span v-for="(topic, index) in selectedTopics" :key="index"
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                                            {{ topic }}
                                            <button type="button" @click="removeTopic(index)"
                                                class="ml-1 text-gray-500 hover:text-red-600">
                                                ×
                                            </button>
                                        </span>
                                    </div>


                                </div>




                                <div class="sm:col-span-6">
                                    <label class="text-gray-900 text-sm block font-medium">
                                        Internal Note
                                    </label>
                                    <textarea v-model="form.internal_note" rows="4"
                                        placeholder="This is a note for internal use only"
                                        class="bg-white rounded-md text-base text-gray-900 w-full block focus:outline-indigo-600 mt-2 outline-gray-300 px-3 py-1.5 sm:text-sm"></textarea>
                                    <p v-if="form.errors.internal_note" class="text-red-500 text-sm">{{
                                        form.errors.internal_note }}
                                    </p>
                                </div>

                            </div>


                        </div>
                    </div>
                </div>
            </div>


            <!-- Buttons -->
            <div class="flex flex-col sm:flex-row border-gray-900/10 border-t px-4 py-4"
                :class="`${is_outreach ? 'justify-between' : 'justify-end'}`">
                <div v-if="is_outreach && props.mode === 'edit'">
                    <button @click="handleRejectOutreach()" :disabled="props.website?.active" type="button"
                        :class="props.website?.active ? 'hidden' : 'btn-red'">
                        <Trash2 class="w-4 h-4 mr-0" />
                        Reject Website
                    </button>
                </div>

                <div class="flex flex-col sm:flex-row justify-start items-center gap-x-6 mt-4 sm:mt-0">
                    <button type="button" @click="handleCancelWebsite()"
                        class="text-gray-900 text-sm font-semibold order-3 md:order-1  mt-4 md:mt-0">
                        Cancel
                    </button>
                    <div v-if="props.mode === 'edit'"
                        class="flex flex-col sm:flex-row items-center justify-end gap-x-6 order-2 md:order-3">
                        <button @click="handleActivate()" type="button" class="flex items-center gap-1"
                            :class="props.website?.active ? 'btn-red' : 'btn-green'">

                            <Power v-if="!props.website?.active" class="w-4 h-4" />
                            <PowerOff v-if="props.website?.active" class="w-4 h-4" />

                            {{ props.website?.active ? 'Deactivate' : 'Onboard' }} Website
                        </button>
                    </div>
                    <button type="submit" class="btn-indigo order-1 md:order-3 mb-4 sm:mb-0"
                        :disabled="form.processing">
                        <span v-if="form.processing">
                            <LoaderCircle class="h-5 w-5 animate-spin" />
                        </span>
                        <span v-if="!form.processing">
                            <Save class="h-5 w-5" />
                        </span>
                        <span>
                            {{ form.processing ? (props.mode === 'edit' ? "Saving..." : "Creating...") : (props.mode
                                ===
                                'edit' ?
                                "Save Changes" : "Create Website") }}
                        </span>
                    </button>
                </div>

            </div>
        </div>
    </form>
    <div class="my-10" v-if="props.is_outreach == false && props.is_publisher == false">
        <WebsiteLogs ref="websiteLogsRef" v-if="props.mode === 'edit'" :websiteId="props.website?.id"
            @refresh="fetchLogs" />
    </div>

    <ConfirmDialog v-model:open="show" :title="confirmTitle" :description="confirmDescription" cancelText="No, keep it"
        :confirmText="confirmText" @confirm="handleConfirm" @cancel="handleCancel" />
    <AssignPublisherDialog v-model:open="showAssignDialog" :website="selectedWebsite"
        :currentPublisherId="form.publisher_user_id" @assigned="updatePublisher" :publisher="publisherInfo" />

</template>




<script setup>
// ==========================================================
// Imports
// ==========================================================
import { router, useForm } from "@inertiajs/vue3";
import { computed, inject, ref, watch, watchEffect } from "vue";
import { Lock, CircleChevronLeft, Save, LoaderCircle, Trash2, ExternalLink, Power, PowerOff, Search, Check } from 'lucide-vue-next';
import ConfirmDialog from '@/Components/ConfirmDialog.vue';
import AssignPublisherDialog from '@/Components/AssignPublisherDialog.vue';
import { abbreviateNumber } from '@/helpers/utils';
import BaseCombobox from '@/Components/BaseCombobox.vue'
import debounce from 'lodash/debounce';
import WebsiteLogs from './WebsiteLogs.vue'

import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@/Components/ui/tooltip'

import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList
} from '@/Components/ui/combobox'

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    website: Object,
    categories: Array,
    languages: Array,
    is_outreach: {
        type: Boolean,
        default: false
    },
    mode: {
        type: String,
        default: 'create' // or 'edit'
    },
    top_traffic_country: Object,
    is_publisher: {
        type: Boolean,
        default: false
    }
});

// ==========================================================
// Refs & Injects 
// ==========================================================
const notify = inject("$notify");
const show = ref(false);
const showAssignDialog = ref(false);
const confirmTitle = ref('');
const confirmDescription = ref('');
const confirmText = ref('');
const publisherInfo = ref(props.website?.publisher ?? null);
const selectedWebsite = computed(() => props.website);


const websiteLogsRef = ref(null)

// topics

// const selectedTopics = ref(props.website?.topics || [])
const selectedTopics = ref((props.website?.topics || []).map(t => typeof t === 'string' ? t : t.name))

const topicInput = ref('')
const availableTopics = ref([])
const isSearching = ref(false)



async function searchTopics(query) {
    isSearching.value = true
    try {
        const res = await axios.get(route('admin.topics.search', { q: query }))
        availableTopics.value = res.data
    } catch (e) {
        availableTopics.value = []
    } finally {
        isSearching.value = false
    }
}
function addTopic(topic) {
    const topicName = typeof topic === 'string' ? topic.trim() : topic?.name?.trim();
    if (topicName && !selectedTopics.value.includes(topicName)) {
        selectedTopics.value.push(topicName);
    }
    topicInput.value = '';
}

function removeTopic(index) {
    selectedTopics.value.splice(index, 1)
}

// function handleTopicKeydown(event) {
//     const value = topicInput.value.trim();
//     if ((event.key === 'Enter' || event.key === ',') && value) {
//         event.preventDefault();
//         addTopic(value);
//     }
// }

function handleTopicKeydown(event) {
    const value = topicInput.value.trim();
    if ((event.key === 'Enter' || event.key === ',') && value) {
        event.preventDefault();
        if (!selectedTopics.value.includes(value)) {
            selectedTopics.value.push(value);
        }
        topicInput.value = '';
    }
}

watch(selectedTopics, () => {
    availableTopics.value = [];
    topicInput.value = '';
});


const handleTopicTyping = debounce((event) => {
    topicInput.value = event.target.value;
    const trimmed = topicInput.value.trim();

    if (trimmed.length >= 3) {
        searchTopics(trimmed);
    } else {
        availableTopics.value = [];
    }
}, 300);
// ==========================================================
// Form - useForm Initialization
// ==========================================================
const form = useForm({
    website_domain: props.website?.website_domain || "",
    site_language_id: props.website?.site_language_id || "",
    main_category_id: props.website?.main_category_id || "",
    category_global_rank: props.website?.category_global_rank || 0,
    site_title: props.website?.site_title || "",
    site_description: props.website?.site_description || "",
    domain_registration_date: props.website?.domain_registration_date || "",
    site_icon_image_id: props.website?.site_icon_image_id || 0,
    guest_post_price: props.website?.guest_post_price || 0,
    link_insert_price: props.website?.link_insert_price || 0,
    casino_post_price: props.website?.casino_post_price || 0,
    adult_post_price: props.website?.adult_post_price || 0,
    finance_post_price: props.website?.finance_post_price || 0,
    dating_post_price: props.website?.dating_post_price || 0,
    cbd_post_price: props.website?.cbd_post_price || 0,
    crypto_post_price: props.website?.crypto_post_price || 0,
    site_requirements: props.website?.site_requirements || "",
    example_post_url: props.website?.example_post_url || "",
    article_validity_in_months: props.website?.article_validity_in_months || 36,
    turn_around_time_in_days: props.website?.turn_around_time_in_days || 0,
    indexed_article: props.website?.indexed_article ?? 0,
    link_relation: props.website?.link_relation || "dofollow",
    sponsorship_label: props.website?.sponsorship_label ?? 0,
    homepage_visible: props.website?.homepage_visible ?? 0,
    publisher_user_id: props.website?.publisher_user_id || 0,
    // contact_email: props.website?.contact_email || "",
    active: props.website?.active ?? 0,
    site_source: props.website?.site_source || "self",
    internal_note: props.website?.internal_note || "",
    topics: props.website?.topics || [],
});

const articleValidityToggle = ref(form.article_validity_in_months === 36);
const originalArticleValidity = ref(form.article_validity_in_months);



const validationErrors = ref({
    guest_post_price: false,
    link_insert_price: false,
    casino_post_price: false,
    adult_post_price: false,
    finance_post_price: false,
    dating_post_price: false,
    cbd_post_price: false,
    crypto_post_price: false,
});

watch(articleValidityToggle, (val) => {
    if (val) {
        form.article_validity_in_months = 36;
    } else {
        form.article_validity_in_months = originalArticleValidity.value;
    }
});

// watch(topicInput, debounce((val) => {
//     searchTopics(val);
// }, 300));

function validatePriceField(fieldName) {
    const toggleOn = fieldName === 'guest_post_price' ? true : priceToggles.value[fieldName]; // guest_post_price has no toggle
    const value = form[fieldName];

    if (toggleOn && (!value || Number(value) === 0)) {
        validationErrors.value[fieldName] = true;
        scrollToField(fieldName);
        notify(`${fieldName.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())} must be greater than 0`, { type: "error" });
        return false;
    }

    validationErrors.value[fieldName] = false;
    return true;
}



const originalPrices = ref({
    guest_post_price: props.website?.guest_post_price || 0,
    link_insert_price: props.website?.link_insert_price || 0,
    casino_post_price: props.website?.casino_post_price || 0,
    adult_post_price: props.website?.adult_post_price || 0,
    finance_post_price: props.website?.finance_post_price || 0,
    dating_post_price: props.website?.dating_post_price || 0,
    cbd_post_price: props.website?.cbd_post_price || 0,
    crypto_post_price: props.website?.crypto_post_price || 0,
});
const priceToggles = ref({
    guest_post_price: props.website?.guest_post_price > 0,
    link_insert_price: props.website?.link_insert_price > 0,
    casino_post_price: props.website?.casino_post_price > 0,
    adult_post_price: props.website?.adult_post_price > 0,
    finance_post_price: props.website?.finance_post_price > 0,
    dating_post_price: props.website?.dating_post_price > 0,
    cbd_post_price: props.website?.cbd_post_price > 0,
    crypto_post_price: props.website?.crypto_post_price > 0,
});

function disablePrice(field) {
    const isEnabled = priceToggles.value[field];

    if (isEnabled) {
        originalPrices.value[field] = form[field]; // Save current value
        form[field] = 0;
        priceToggles.value[field] = false;
    } else {
        form[field] = originalPrices.value[field] || 0;
        priceToggles.value[field] = true;
    }
}

const isUnlimitedValidity = computed(() => form.article_validity_in_months === 36);



// ==========================================================
// Methods - Dialog Management
// ==========================================================
function openAssignPublisherDialog() {
    showAssignDialog.value = true;
}

function updatePublisher(user) {
    if (!user) {
        form.publisher_user_id = 0;
        publisherInfo.value = null;
        return;
    }

    form.publisher_user_id = user.id;
    publisherInfo.value = user;

    if (props.website) {
        props.website.publisher_user_id = user.id;
        props.website.publisher = user;
    }

    showAssignDialog.value = false;
}

// ==========================================================
// Methods - Confirmation Modal (Activate/Deactivate)
// ==========================================================
function handleActivate() {
    const isActive = props.website?.active;


    // Check: prevent activation if no publisher is assigned
    if (!isActive && form.publisher_user_id === 0) {
        notify("Please assign a publisher before onboarding the website.", { type: "error" });
        return;
    }


    confirmTitle.value = isActive ? "Deactivate Website?" : "Activate Website?";
    confirmDescription.value = isActive
        ? "Are you sure you want to deactivate this website?"
        : "Are you sure you want to activate this website?";
    confirmText.value = isActive ? "Yes, Deactivate" : "Yes, Onboard Website";

    show.value = true;
}

function handleConfirm() {
    toggleStatus(props.website);
    show.value = false;
}

function handleCancel() {
    show.value = false;
}

const emit = defineEmits(['logs-updated'])


// ==========================================================
// Methods - Form Submission
// ==========================================================
const submit = () => {
    const isEdit = props.mode === 'edit';
    const routeParams = isEdit ? props.website?.id : undefined;

    // Assign selected topics
    form.topics = selectedTopics.value;

    if (!articleValidityToggle.value && (!form.article_validity_in_months || Number(form.article_validity_in_months) === 0)) {
        scrollToField('article_validity_in_months');
        notify("Article Validity (Months) is required and must be greater than 0", { type: "error" });
        return;
    }
    // Reset all validations
    Object.keys(validationErrors.value).forEach(key => validationErrors.value[key] = false);

    // Validate pricing fields
    const fieldsToValidate = [
        'guest_post_price',
        'link_insert_price',
        'casino_post_price',
        'adult_post_price',
        'finance_post_price',
        'dating_post_price',
        'cbd_post_price',
        'crypto_post_price',
    ];

    for (const field of fieldsToValidate) {
        if (!validatePriceField(field)) return;
    }


    const routeName = isEdit ? "admin.websites.update" : "admin.websites.store";

    if (isEdit) {
        form.put(route(routeName, routeParams), {
            preserveScroll: true,
            onSuccess: () => {
                notify(isEdit ? "Website updated successfully!" : "Website created successfully!", { type: "success" });

                websiteLogsRef.value?.fetchLogs?.()

                // Emit event to refresh logs
                if (isEdit) {
                    emit('logs-updated')
                }
            },
            onError: () => {
                const firstErrorKey = Object.keys(form.errors)[0];
                notify(form.errors[firstErrorKey], { type: "error" });
                scrollToField(firstErrorKey);
            }
        });
    } else {
        form.post(route(routeName, routeParams), {
            preserveScroll: true,
            onSuccess: () => {
                notify(isEdit ? "Website updated successfully!" : "Website created successfully!", { type: "success" });

                websiteLogsRef.value?.fetchLogs?.()

                // Emit event to refresh logs
                if (isEdit) {
                    emit('logs-updated')
                }
            },
            onError: () => {
                const firstErrorKey = Object.keys(form.errors)[0];
                notify(form.errors[firstErrorKey], { type: "error" });
                scrollToField(firstErrorKey);
            }
        });
    }
};


// ==========================================================
// Methods - Status Toggle
// ==========================================================
const toggleStatus = (website) => {
    const newStatus = !website.active;
    const statusText = newStatus ? 'Active' : 'Inactive';

    router.put(route('admin.websites.toggle-status', website.id), {
        active: newStatus
    }, {
        preserveScroll: true,
        preserveState: true,
        replace: false,
        onSuccess: () => {
            notify(`Website status updated to ${statusText}`, { type: 'success' });
            form.active = newStatus ? 1 : 0;

            if (props.website) {
                props.website.active = newStatus ? 1 : 0;
            }
        },
        onError: () => {
            notify('Failed to update status', { type: 'error' });
        }
    });
};

// ==========================================================
// Methods - Reject Outreach Website
// ==========================================================
function handleRejectOutreach() {
    if (confirm("Are you sure you want to reject this website?")) {
        router.put(route('admin.outreach.reject', props.website?.id), {}, {
            preserveScroll: true,
            preserveState: true,
            replace: false,
            onSuccess: () => {
                notify("Outreach rejected successfully!", { type: "success" });
            },
            onError: () => {
                notify("Failed to reject outreach", { type: "error" });
            }
        });
    }
}

// ==========================================================
// Methods - Toggle Switch Validation
// ==========================================================

function limitArticleValidity(event) {
    const value = parseInt(event.target.value, 10);

    if (value > 36) {
        form.article_validity_in_months = 36;
    } else {
        form.article_validity_in_months = value;
    }
}

// ==========================================================
// Helpers
// ==========================================================
const scrollToField = (field) => {
    const el = document.querySelector(`.field-${field}`);
    if (el) {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
        setTimeout(() => el.focus(), 300);
    }
};

const confirmDelete = () => {
    router.delete(route("admin.websites.destroy", props.website?.id), {}, {
        onSuccess: () => {
            notify("Website deleted successfully!", { type: "success" });
            router.visit(route('admin.websites.index'), { replace: true });
        },
        onError: (errors) => alert(errors),
    });
};

// Listen to update event and call fetchLogs()
watchEffect(() => {
    if (websiteLogsRef.value) {
        watch(() => props.website, () => {
            websiteLogsRef.value.fetchLogs?.()
        })
    }
})

// topics

const generateSignedUrl = async () => {
    if (!props.website?.id) {
        notify("Website ID not found", { type: "error" });
        return;
    }

    try {
        const response = await axios.post(route('admin.websites.generate-signed-url', props.website.id));
        await navigator.clipboard.writeText(response.data.url);
        notify("Signed URL copied to clipboard!", { type: "success" });
    } catch (error) {
        console.log(error);
        notify("Failed to generate signed URL", { type: "error" });
    }
};

// Handle Cancel
function handleCancelWebsite() {
    if (props.is_publisher) {
        router.visit(route('publisher.websites.index'), { replace: true });

    } else {
        router.visit(route('admin.websites.index'), { replace: true });

    }
}



</script>
