<template>
    <Transition name="fade">
        <div v-if="message" class="mb-4 rounded-md bg-green-100 p-4 text-green-700 flex justify-between items-start shadow-sm">
            <div class="text-sm font-medium leading-5">
                {{ message }}
            </div>
            <button @click="$emit('dismiss')" class="ml-4 text-green-700 hover:text-green-900 font-bold text-lg" aria-label="Dismiss">
                &times;
            </button>
        </div>
    </Transition>
</template>

<script setup>
defineProps({
    message: {
        type: String,
        required: true,
    },
})

defineEmits(['dismiss'])
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>