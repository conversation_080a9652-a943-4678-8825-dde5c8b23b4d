<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <div class="flex flex-row gap-2 items-center">
                    <h2 v-if="!is_outreach && !outreachUserIdExists" class="title-1">
                        Marketplace Websites
                    </h2>
                    <h2 v-if="!is_outreach && outreachUserIdExists" class="title-1">
                        Websites assigned to Outreach User
                    </h2>

                    <h2 v-if="is_outreach && !outreachUserIdExists" class="title-1">
                        Outreach Websites
                    </h2>

                    <h2 v-if="is_outreach" class="badge-gray text-sm text-gray-500">
                        {{ active_outreach_websites_count }} websites in queue
                    </h2>
                </div>

                <!-- <div v-if="!is_outreach && outreachUserIdExists" class="flex flex-row gap-2 items-center">
                    x
                </div> -->

                <p class="subtitle-1">
                    Manage all listed websites.
                </p>
                <div class="text-sm text-gray-500">
                    Displaying {{ showingCount }} of {{ websites.total }} websites
                </div>
            </div>

            <div class="flex flex-col gap-2 justify-end items-end">


                <button v-if="user.role === pageProps.props.roleEnums.Outreach" @click="handleAssignWebsite" class="btn-green">
                    <ListFilterPlus class="h-5 w-5" />
                    <span>Assign New Website</span>
                </button>

                <Link :href="route('admin.websites.create')" class="btn-indigo">
                <PlusCircle class="h-5 w-5" />
                <span>Add Website</span>
                </Link>





                <Link v-if="user.role === pageProps.props.roleEnums.SuperAdmin" :href="route('admin.websites.import')" class="btn-indigo">
                <Import class="h-5 w-5" />
                <span>Bulk Import Websites</span>
                </Link>



                <!-- Button to open the modal -->
                <button @click="showEligibilityModal = true" class="btn-gray">
                    <ArrowRightToLine class="h-5 w-5" />
                    <span>Check Website Eligibility</span>
                </button>

            </div>
        </div>

        <!-- Filters Component -->
        <div class="mt-5">
            <Filters :key="JSON.stringify(filters)" :searchTerm="filters.searchTerm" :status="filters.status" :verified="filters.verified" :outreach_status="filters.outreach_status" :is_outreach="is_outreach" />
        </div>

        <!-- Table -->
        <div class="border-b border-gray-200 flow-root mt-8">
            <div class="-mx-4 -my-2 lg:-mx-8 overflow-x-auto sm:-mx-6">
                <div class="align-middle inline-block lg:px-8 min-w-full py-2 sm:px-6">
                    <table v-if="websites.data.length" class="divide-gray-300 divide-y min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="text-gray-900  text-sm relative w-[120px]">Edit</th>

                                <th @click="sort('id')" class="w-[80px] text-gray-900 text-left text-sm cursor-pointer font-semibold px-0 py-3.5 ps-4">
                                    ID
                                    <ChevronsUp v-if="filters.sortField === 'id'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortOrder === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th @click="sort('website_domain')" class="text-gray-900 text-left text-sm cursor-pointer font-semibold px-0 py-3.5 ps-4">
                                    Website
                                    <ChevronsUp v-if="filters.sortField === 'website_domain'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortOrder === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                                </th>
                                <th @click="sort('orders_count')" class="whitespace-nowrap text-gray-900 text-left text-sm cursor-pointer font-semibold px-4 py-3.5">
                                    Orders
                                    <ChevronsUp v-if="filters.sortField === 'orders_count'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortOrder === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th @click="sort('guest_post_price')" class="w-[150px]  text-gray-900 text-left text-sm cursor-pointer font-semibold px-0 py-3.5 ps-4 whitespace-nowrap">
                                    Price
                                    <ChevronsUp v-if="filters.sortField === 'guest_post_price'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortOrder === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                </th>
                                <th class="text-gray-900  text-left text-sm  font-semibold px-0 py-3.5 ps-4">
                                    Publisher
                                </th>


                                <th class="text-gray-900 text-sm  font-semibold px-0 py-3.5 text-center w-[80px] ">
                                    Verified
                                </th>

                                <th v-if="!is_outreach" @click="sort('active')" class="text-center text-gray-900  text-sm cursor-pointer font-semibold px-0 py-3.5 ps-4">
                                    <span>
                                        Status
                                        <ChevronsUp v-if="filters.sortField === 'active'" :class="[
                                            'inline-block transition-transform duration-300',
                                            filters.sortOrder === 'asc' ? 'rotate-180' : ''
                                        ]" class="w-4 h-4 ml-1" />
                                        <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />
                                    </span>
                                </th>

                                <th v-if="is_outreach" class="text-gray-900 text-left text-sm  font-semibold px-0 py-3.5 ps-4">
                                    <span>
                                        Outreach
                                    </span>
                                </th>

                                <th @click="sort('updated_at')" class="text-gray-900 text-left text-sm cursor-pointer font-semibold px-0 py-3.5 ps-4  w-[150px]">
                                    Updated

                                    <ChevronsUp v-if="filters.sortField === 'updated_at'" :class="[
                                        'inline-block transition-transform duration-300',
                                        filters.sortOrder === 'asc' ? 'rotate-180' : ''
                                    ]" class="w-4 h-4 ml-1" />
                                    <ChevronsUpDown v-else class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />


                                </th>

                                <!-- <th scope="col" class="text-gray-900  text-sm px-3 py-3.5 relative sm:pr-0">Delete</th> -->

                            </tr>
                        </thead>

                        <tbody class="divide-y divide-gray-200 bg-white">
                            <tr v-for="website in websites.data" :key="website.id" class="hover:bg-gray-50">
                                <td class="text-sm font-medium pl-3 pr-4 py-5">
                                    <div class="flex justify-center relative">
                                        <Link :href="route('admin.websites.edit', website.id)" class="flex text-indigo-600 gap-1 hover:text-indigo-900 items-center whitespace-nowrap">
                                        <FilePenLine class="size-4" />
                                        <span>Edit</span>
                                        </Link>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">{{ website.id }}</td>
                                <td class="px-4 py-3 text-sm text-gray-900 ">
                                    <div class="flex gap-1 items-center whitespace-nowrap">
                                        <span>
                                            {{ website.website_domain }}
                                        </span>
                                        <a :href="returnWebsiteUrl(website)" target="_blank" class="text-indigo-500">
                                            <ExternalLink class="w-4 h-4" />
                                        </a>

                                    </div>

                                    <div v-if="!is_outreach" class="flex gap-1 items-center">
                                        <span class="text-xs text-gray-500 w-fit" :class="`badge-${website.outreach?.status}`">
                                            {{ website.outreach?.status }}
                                        </span>
                                        <span v-if="website.outreach?.status" class="text-xs text-gray-500 w-fit">:</span>
                                        <Link v-if="!is_outreach" :href="route('admin.users.edit', website.outreach?.user?.id ? website.outreach?.user?.id : '0')" class="text-xs text-gray-400">
                                        <span class="text-xs text-gray-500 font-bold hover:text-indigo-500 hover:underline">
                                            {{ website.outreach?.user?.name }}
                                        </span>
                                        </Link>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center">
                                    {{ website.orders_count }}
                                </td>

                                <td class="px-4 py-3 text-sm text-gray-600">${{ website.guest_post_price }}</td>

                                <td class="px-4 py-3 text-sm text-gray-600">
                                    <template v-if="website.publisher_user_id === 0">
                                        <span class="text-gray-300 italic text-xs whitespace-nowrap">- No Account -</span>
                                    </template>

                                    <template v-else>

                                        <div v-if="website.publisher_user_id !== 0">
                                            <div class="font-medium text-gray-700">{{ website.publisher?.name }}</div>
                                            <div class="text-xs text-gray-500">{{ website.publisher?.email }}</div>
                                        </div>
                                        <!-- <div v-else>
                                            <div class="font-sm text-gray-700">{{ website.contact_email }}</div>
                                        </div> -->
                                    </template>
                                </td>



                                <td class=" ">
                                    <CircleCheck v-if="website.verified_status" class="w-5 h-5 text-green-500 m-auto" />
                                    <CircleOff class="w-5 h-5 text-red-500 m-auto" v-else />
                                </td>


                                <td v-if="!is_outreach" class="px-4 py-3 text-sm font-semibold text-center w-[120px]">
                                    <span :class="[
                                        'px-3 py-2 rounded-full w-[120px] block text-xs',
                                        website.active ? 'text-green-500' : 'text-red-500'
                                    ]">
                                        {{ website.active ? 'Onboarded' : 'Inactive' }}
                                    </span>
                                </td>



                                <td v-if="is_outreach" class="px-4 py-3 text-sm font-semibold text-center w-[150px]">
                                    <span class="whitespace-nowrap px-3 py-2 rounded-full" :class="website.outreach?.status === 'inprogress' ? 'badge-yellow' :
                                        website.outreach?.status === 'onboarded' ? 'badge-green' :
                                            'badge-gray'">
                                        {{ website.outreach?.status == "inprogress" ? "In Progress" :
                                            website.outreach?.status == "onboarded" ? "Onboarded" : "Rejected"
                                        }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-600 whitespace-nowrap">{{
                                    website.updated_at_formatted }}</td>



                            </tr>
                        </tbody>
                    </table>

                    <div v-if="!websites.data.length" class="p-4 text-center text-red-500">
                        <NoDataMessage message1="No websites found." message2="Try adjusting your filters." />

                    </div>
                </div>
            </div>
        </div>
        <!-- Pagination -->
        <PaginationLinks v-if="websites.data.length" :links="websites.links" :filters="props.filters" class="mt-4" />

        <ConfirmDialog v-model:open="show" title="Are you sure?" description="Are you sure you want to change status to Inactive?" cancelText="No, keep it" confirmText="Yes, Change Status" @confirm="handleConfirm"
            @cancel="handleCancel" />

        <ConfirmDialog v-model:open="assignConfirm" title="Are you sure?" description="Do you want to assign a new website for outreach?" cancelText="No, cancel" confirmText="Yes, assign"
            @confirm="proceedAssignWebsite" />



        <!-- ShadCN Modal (Dialog) -->
        <Dialog v-model:open="showEligibilityModal">
            <DialogContent class="w-full max-w-md">
                <DialogHeader>
                    <DialogTitle>Check Website Eligibility </DialogTitle>
                    <DialogDescription>Enter the website address below to check its eligibility.
                    </DialogDescription>
                </DialogHeader>

                <div class="space-y-4 mt-4 ">
                    <Input @keyup.enter="handleCheckWebsiteEligibility" :value="websiteUrlInput" @input="websiteUrlInput = $event.target.value" placeholder="Enter website (e.g. example.com)" class="w-full rounded-lg" />


                    <div class="flex flex-row gap-4 items-center justify-end">
                        <Button @click="handleCheckWebsiteEligibility" :disabled="isChecking" class="btn-indigo w-full">
                            <span v-if="!isChecking">Check Eligibility</span>
                            <span v-else>Checking...</span>
                        </Button>
                    </div>
                    <div v-if="eligibilityResult !== null" class="text-center mt-4">
                        <p :class="eligibilityResult ? 'text-green-500 font-semibold' : 'text-red-500 font-semibold'">
                            {{ eligibilityResult ? 'Website is eligible ✅' : 'Website is NOT eligible ❌' }}
                        </p>
                    </div>
                    <div v-else class="text-center mt-4">
                        <p class="text-gray-500 font-semibold">
                            {{ isChecking ? 'Searching....' : 'Type to check domain' }}
                        </p>
                    </div>
                </div>
            </DialogContent>
        </Dialog>

    </div>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import { computed, inject, onMounted, ref } from "vue";
import { router } from "@inertiajs/vue3";

import Filters from "./Filters.vue";
import PaginationLinks from '@/Components/PaginationLinks.vue';
import ConfirmDialog from '@/Components/ConfirmDialog.vue';
import { returnWebsiteUrl } from '@/lib/utils';
import NoDataMessage from '@/Components/NoDataMessage.vue';
import axios from 'axios';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogFooter,
    DialogTitle,
    DialogDescription,
    DialogClose
} from '@/Components/ui/dialog'

import {
    Import,
    ArrowRightToLine,
    Check,
    ChevronsUp,
    ChevronsUpDown,
    CircleCheck,
    CircleOff,
    ExternalLink,
    FilePenLine,
    ListFilterPlus,
    PlusCircle,
    Trash,
    UserPen,
    UserPlus,
    Trash2,
    CircleCheckBig,
    CircleCheckBigIcon
} from "lucide-vue-next";

import { usePage } from '@inertiajs/vue3';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    websites: {
        type: Object,
        default: () => ({ data: [], links: [] })
    },
    filters: {
        type: Object,
        default: () => ({ searchTerm: "", status: "", verified: "", sortField: "updated_at", sortOrder: "desc" })
    },
    user: {
        type: Object,
        default: () => ({})
    },
    active_outreach_websites_count: {
        type: Number,
        default: 0
    },
    is_outreach: {
        type: Boolean,
        default: false
    }
});

// ==========================================================
// Refs & Injects
// ==========================================================
const hoveredRowId = ref(null);
const selectedWebsite = ref(null);
const show = ref(false);
const assignConfirm = ref(false);
const notify = inject("$notify");

const showEligibilityModal = ref(false);
const websiteUrlInput = ref('');
const eligibilityResult = ref(null);  // true (success), false (failed), null (not checked)
const isChecking = ref(false);


// ==========================================================
// Lifecycle Hooks - Notify Outreach Websites
// ==========================================================
onMounted(() => {
    if (props.is_outreach && props.active_outreach_websites_count === 0) {
        notify("No websites in queue. Please add new websites or contact support.", { type: "error" });
    }
});

// ==========================================================
// Computed - Filters
// Get values from props and return as computed object
// ==========================================================
const filters = computed(() => ({
    searchTerm: props.filters.searchTerm || "",
    status: props.filters.status || "",
    verified: props.filters.verified || "",
    sortField: props.filters.sortField || "updated_at",
    sortOrder: props.filters.sortOrder || "desc",
}));

// get query params page
const page = computed(() => {
    const queryPage = usePage().url.split('?')[1]?.split('&').find(param => param.startsWith('page='));
    return queryPage ? Number(queryPage.split('=')[1]) : 1;
});

const pageProps = inject("page");

// Check if outreach_user_id exists in query string
const outreachUserIdExists = computed(() => {
    const url = usePage().url;
    if (!url || !url.includes('?')) {
        return false;
    }
    const queryParams = new URLSearchParams(url.split('?')[1]);
    return queryParams.has('outreach_user_id');
});

// Ensure default sorting fallback for props (reactivity-safe)
props.filters.sortField = props.filters.sortField || 'id';
props.filters.sortOrder = props.filters.sortOrder || 'asc';

// ==========================================================
// Methods - Sorting
// Sort the websites by the selected field
// ==========================================================
const sort = (field) => {
    const direction = props.filters.sortField === field && props.filters.sortOrder === 'asc' ? 'desc' : 'asc';

    router.get(route('admin.websites.index'), {
        ...props.filters,
        sortField: field,
        sortOrder: direction
    }, {
        preserveState: true,
        preserveScroll: true
    });
};

// ==========================================================
// Methods - Status Toggle & Confirmations
// Toggle the status of the selected website
// ==========================================================

const showingCount = computed(() => {
    const perPage = props.filters.perPage || 10;
    const currentPage = page.value || 1;
    const itemsOnPage = props.websites.data.length;

    const totalShown = (currentPage - 1) * perPage + itemsOnPage;
    return totalShown > props.websites.total ? props.websites.total : totalShown;
});

function handleDelete(website) {
    selectedWebsite.value = website;
    show.value = true;
}

function handleConfirm() {
    toggleStatus(selectedWebsite.value);
    show.value = false;
}

function handleCancel() {
    show.value = false;
}

const toggleStatus = (website) => {
    const newStatus = !website.active;
    const statusText = newStatus ? 'Active' : 'Inactive';

    router.put(route('admin.websites.toggle-status', website.id), {
        active: newStatus
    }, {
        preserveScroll: true,
        preserveState: true,
        replace: false,
        onSuccess: () => notify(`Website status updated to ${statusText}`, { type: 'success' }),
        onError: () => notify('Failed to update status', { type: 'error' }),
    });
};

// ==========================================================
// Methods - Delete Website
// Confirm the deletion of the selected website
// ==========================================================
const confirmDelete = (websiteId) => {
    if (confirm("Are you sure you want to delete this website?")) {
        router.post(route("admin.websites.delete", websiteId), {}, {
            preserveScroll: true,
            onSuccess: () => notify("website deleted successfully!", { type: "success" }),
            onError: (errors) => alert(errors),
        });
    }
};

// ==========================================================
// Methods - Assign Website
// Confirm the assignment of a new website for outreach
// ==========================================================
function handleAssignWebsite() {
    assignConfirm.value = true;
}

function proceedAssignWebsite() {
    router.visit(route('admin.outreach.assign'), {
        preserveState: false,
        preserveScroll: true,
    });
}

function handleCheckWebsiteEligibility() {
    if (!websiteUrlInput.value.trim()) {
        notify("Please enter a website URL", { type: "error" });
        return;
    }

    isChecking.value = true;
    eligibilityResult.value = null;

    axios.post(route('admin.website.checkeligibility'), {
        website_url: websiteUrlInput.value
    })
        .then(response => {
            eligibilityResult.value = response.data.success ?? true;
            isChecking.value = false;
        })
        .catch(error => {
            console.error(error);
            eligibilityResult.value = false;
            isChecking.value = false;

            if (error.response && error.response.data && error.response.data.message) {
                // notify(error.response.data.message, { type: "error" });
            } else {
                notify("An error occurred while checking eligibility.", { type: "error" });
            }
        });
}



</script>
