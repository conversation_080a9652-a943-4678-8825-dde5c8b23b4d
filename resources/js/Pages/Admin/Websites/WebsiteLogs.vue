<template>
    <div>
        <div class="flex flex-row items-center justify-between bg-gray-100 p-4 rounded-md">
            <h2 class="text-xl font-semibold text-gray-800 ">Activity Logs
                <span class="badge-green ms-1">{{ logs.total }}</span>
            </h2>

            <div class="flex flex-row items-center gap-2">
                <button class="btn-gray-selected" @click="showLogs = !showLogs">
                    <ChevronUp v-if="showLogs" class="w-4 h-4" />
                    <ChevronDown v-else class="w-4 h-4" />
                </button>
            </div>
        </div>
        <div v-if="loading" class="text-gray-500 text-sm">Loading logs...</div>


        <div v-if="showLogs">
            <table v-if="logs.data?.length" class="mt-4 min-w-full divide-y divide-gray-200 border rounded shadow-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-left px-4 py-2">ID</th>
                        <th class="text-left px-4 py-2">Description</th>
                        <th class="text-left px-4 py-2">User</th>
                        <th class="text-left px-4 py-2">Changes</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    <tr v-for="log in logs.data" :key="log.id">
                        <td class="px-4 py-2 text-sm">{{ log.id }} </td>
                        <td class="px-4 py-2 text-sm">{{ log.description }}</td>
                        <td class="px-4 py-2 text-sm">
                            <div>{{ log.causer?.name || 'System' }}</div>
                            <div class="text-xs text-gray-400">ID: {{ log.causer?.id || 'N/A' }}</div>
                        </td>
                        <td class="px-4 py-2 text-xs text-gray-700 whitespace-pre-wrap">
                            <!-- Custom view for topic changes -->
                            <div v-if="log.description === 'Updated Topics'">
                                <div v-if="log.properties?.added_topics?.length">
                                    <span class="font-medium">Added Topics:</span>
                                    <ul class="text-green-600 list-disc list-inside">
                                        <li v-for="(topic, i) in log.properties.added_topics" :key="'added-' + i">{{ topic }}</li>
                                    </ul>
                                </div>
                                <div v-if="log.properties?.removed_topics?.length" class="mt-2">
                                    <span class="font-medium">Removed Topics:</span>
                                    <ul class="text-red-600 list-disc list-inside">
                                        <li v-for="(topic, i) in log.properties.removed_topics" :key="'removed-' + i">{{ topic }}</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Default attribute change view -->
                            <div v-else-if="log.properties?.attributes">
                                <div v-for="(val, key) in log.properties.attributes" :key="key" class="flex gap-1">
                                    <span class="font-medium">{{ key }}:</span>
                                    <span class="text-green-600">{{ val }}</span>
                                    <span v-if="log.properties.old?.[key] !== undefined" class="text-gray-400">
                                        (was <span class="text-red-500">{{ log.properties.old[key] }}</span>)
                                    </span>
                                </div>
                            </div>

                            <!-- Fallback -->
                            <div v-else>-</div>
                        </td>

                    </tr>
                </tbody>
            </table>
            <div v-if="logs.data?.length">
                <PaginationLinks v-if="logs.links?.length" :links="logs.links" class="mt-4" />
            </div>
            <div v-else>
                <div class="text-gray-500 text-sm">No logs found</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, defineExpose } from 'vue'
import PaginationLinks from '@/Components/PaginationLinks.vue'

import { ChevronDown, ChevronUp } from 'lucide-vue-next'

const props = defineProps({
    websiteId: {
        type: Number,
        required: true
    }
})

const showLogs = ref(false)

const logs = ref({ data: [], links: [] })
const loading = ref(true)

async function fetchLogs(page = 1) {
    loading.value = true
    try {
        const res = await axios.get(route('admin.websites.activity-logs', { website: props.websiteId, page }))
        logs.value = res.data.props.logs
    } catch (e) {
        console.error('Failed to fetch logs', e)
    } finally {
        loading.value = false
    }
}

defineExpose({
    fetchLogs
})

onMounted(() => {
    fetchLogs()
})

// Optional: re-fetch logs if websiteId changes
watch(() => props.websiteId, () => {
    fetchLogs()
})
</script>