<template>
    <div class="grid grid-cols-7 gap-x-4 items-center w-full">
        <!-- Search Input -->
        <div class="col-span-3 relative flex items-center">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Search by Keyword</label>
                <div>
                    <input ref="searchInputRef" v-model="form.searchTerm" type="text" placeholder="Search website, title, email..."
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm pr-10" />
                    <button v-if="form.searchTerm" @click="clearSearch" class="absolute right-1 top-[35px] text-gray-400 hover:text-gray-600 flex items-center justify-center">
                        <XCircle class="w-5 h-5" />
                    </button>
                </div>
            </div>
            <!-- Clear button: Visible only when input has text -->

        </div>

        <!-- Status Filter -->
        <div class="col-span-1">
            <label class="text-xs text-gray-400">Website Status</label>

            <select v-model="form.status" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                <option value="">All Status</option>
                <option value="1">Active</option>
                <option value="0">Inactive</option>
            </select>
        </div>

        <!-- Verified Filter -->
        <div class="col-span-1">
            <label class="text-xs text-gray-400">Verified Contact</label>
            <select v-model="form.verified" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                <option value="">Show All</option>
                <option value="1">Verified</option>
                <option value="0">Unverified</option>
            </select>
        </div>

        <!-- Outreach Status Filter -->
        <div class="col-span-1">
            <label class="text-xs text-gray-400">Outreach Status</label>
            <select v-model="form.outreach_status" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                <option value="all">Show All</option> <!-- Default -->
                <option v-if="!is_outreach" value="unassigned">Unassigned</option>

                <option value="inprogress">In Progress</option>
                <option value="onboarded">Onboarded</option>
                <option value="rejected">Rejected</option>
            </select>
        </div>

        <!-- Paginate Total -->
        <div class="col-span-1">
            <label class="text-xs text-gray-400">Display Websites</label>
            <select v-model="form.perPage" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" @change="applyFilters">
                <option value="5">5 per page</option>
                <option value="10">10 per page</option>
                <option value="20">20 per page</option>
                <option value="50">50 per page</option>
                <option value="100">100 per page</option>
            </select>
        </div>
    </div>
</template>


<script setup>
// ==========================================================
// Imports
// ==========================================================
import { ref, watch, onMounted, nextTick } from "vue";
import { useForm, router } from "@inertiajs/vue3";
import debounce from "lodash/debounce";
import { XCircle } from "lucide-vue-next";

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    searchTerm: String,
    status: String,
    verified: String,
    perPage: String,
    outreach_status: String,
    is_outreach: Boolean
});

// ==========================================================
// Refs
// ==========================================================
const searchInputRef = ref(null);

// ==========================================================
// Lifecycle Hooks
// ==========================================================
onMounted(() => {
    focusSearchInput();
});

// ==========================================================
// Form State (with Defaults)
// ==========================================================
const form = useForm({
    searchTerm: props.searchTerm || "",
    status: props.status || "",
    verified: props.verified || "",
    perPage: props.perPage || "10",
    outreach_status: props.outreach_status !== undefined
        ? props.outreach_status
        : (props.is_outreach ? "inprogress" : "all")
});

// ==========================================================
// Watchers - Sync Form with Incoming Props
// ==========================================================

// Sync form with incoming props
watch(
    () => props,
    (newProps) => {
        form.searchTerm = newProps.searchTerm ?? "";
        form.status = newProps.status ?? "";
        form.verified = newProps.verified ?? "";
        form.perPage = newProps.perPage ?? "10";
        form.outreach_status = newProps.outreach_status !== undefined
            ? newProps.outreach_status
            : (props.is_outreach ? "inprogress" : "all");
    },
    { immediate: true, deep: true }
);

// Debounced search trigger
watch(() => form.searchTerm, () => {
    applyFilters();
});

// ==========================================================
// Methods - Focus Search Input
// ==========================================================

// Focus input (used only on mount now)
const focusSearchInput = () => {
    nextTick(() => {
        searchInputRef.value?.focus();
    });
};

// Clear search input
const clearSearch = () => {
    form.searchTerm = "";
    applyFilters();
};

// Apply filters (debounced)
const applyFilters = debounce(() => {
    const query = {};

    if (form.searchTerm.trim() !== "") query.searchTerm = form.searchTerm;
    if (form.status.trim() !== "") query.status = form.status;
    if (form.verified.trim() !== "") query.verified = form.verified;
    if (form.outreach_status.trim() !== "") query.outreach_status = form.outreach_status;

    query.perPage = form.perPage;

    router.get(route("admin.websites.index"), query, {
        preserveState: true,
        preserveScroll: true,
    });
}, 500);
</script>

