<template>
    <div>
        <div class="sm:flex sm:items-center justify-between">
            <div class="sm:flex-auto">
                <h2 class="title-1">Marketplace Orders</h2>
                <p class="subtitle-1">Manage and review all customer orders.</p>

                <div class="text-sm text-gray-500 flex flex-row items-center gap-x-1">
                    <span>
                        Displaying {{ showingCount }} of {{ orders.total }}
                    </span>


                    <span v-if="props.filters.orderStatus !== ''">
                        {{ props.filters.orderStatus }}
                    </span>
                    <span>orders</span>


                    <span v-if="filters.preset_range !== 'show_all'" class="mr-1">
                        updated {{ filters.preset_range === 'today' ? 'Today' :
                            filters.preset_range === 'yesterday' ? 'Yesterday' :
                                filters.preset_range === 'last_7_days' ? 'Last 7 Days' :
                                    filters.preset_range === 'last_30_days' ? 'Last 30 Days' :
                                        filters.preset_range === 'last_90_days' ? 'Last 90 Days' :
                                            filters.preset_range === 'last_12_months' ? 'Last 12 Months' :
                                                filters.preset_range === 'custom' ? 'Custom Range' :
                                                    filters.preset_range }}

                    </span>


                    <span v-if="filters.preset_range !== 'show_all' || props.filters.orderStatus !== ''">
                        <span class="pe-2 text-gray-300">|</span>
                        <Link :href="route('admin.orders.index', { preset_range: 'show_all' })"
                            class="text-indigo-600 hover:underline">
                        <span>Show All</span>
                        </Link>
                    </span>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="mt-5 flex flex-row gap-x-2">
            <div class="">
                <Filters :filters="localFilters" :statuses="statuses" @update="applyFilters" />
            </div>
            <div class="">

                <DateRangeFilter :preset-range="filters.preset_range" :start-date="filters.start_date"
                    :end-date="filters.end_date" :show-quick-buttons="false" @update:filters="onFiltersChange" />
            </div>
        </div>

        <!-- Orders Table -->
        <div class="mt-6 overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-center px-4 py-2 text-sm font-semibold text-gray-700">Details</th>

                        <th @click="sort('id')"
                            class="cursor-pointer text-left px-4 py-2 text-sm font-semibold text-gray-700">
                            Order ID
                            <ChevronsUp v-if="filters.sortField === 'id'" :class="[
                                'inline-block transition-transform duration-300 w-4 h-4 ml-1',
                                filters.sortOrder === 'asc' ? 'rotate-180' : ''
                            ]" />
                            <ChevronsUpDown v-else
                                class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                        </th>
                        <th @click="sort('created_at')"
                            class="cursor-pointer text-left px-4 py-2 text-sm font-semibold text-gray-700">
                            Order Date

                            <ChevronsUp v-if="filters.sortField === 'created_at'" :class="[
                                'inline-block transition-transform duration-300 w-4 h-4 ml-1',
                                filters.sortOrder === 'asc' ? 'rotate-180' : ''
                            ]" />
                            <ChevronsUpDown v-else
                                class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                        </th>
                        <th class="text-left px-4 py-2 text-sm font-semibold text-gray-700">Customer</th>
                        <th class="text-left px-4 py-2 text-sm font-semibold text-gray-700">Order Status</th>
                        <th @click="sort('price_paid')"
                            class="cursor-pointer text-left px-4 py-2 text-sm font-semibold text-gray-700">
                            Amount
                            <ChevronsUp v-if="filters.sortField === 'price_paid'" :class="[
                                'inline-block transition-transform duration-300 w-4 h-4 ml-1',
                                filters.sortOrder === 'asc' ? 'rotate-180' : ''
                            ]" />
                            <ChevronsUpDown v-else
                                class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                        </th>
                        <th @click="sort('items_in_orders')"
                            class="cursor-pointer text-left px-4 py-2 text-sm font-semibold text-gray-700">
                            Items

                            <ChevronsUp v-if="filters.sortField === 'items_in_orders'" :class="[
                                'inline-block transition-transform duration-300 w-4 h-4 ml-1',
                                filters.sortOrder === 'asc' ? 'rotate-180' : ''
                            ]" />
                            <ChevronsUpDown v-else
                                class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                        </th>
                        <th @click="sort('updated_at')"
                            class="cursor-pointer text-left px-4 py-2 text-sm font-semibold text-gray-700">
                            Last Updated

                            <ChevronsUp v-if="filters.sortField === 'updated_at'" :class="[
                                'inline-block transition-transform duration-300 w-4 h-4 ml-1',
                                filters.sortOrder === 'asc' ? 'rotate-180' : ''
                            ]" />
                            <ChevronsUpDown v-else
                                class="inline-block transition-transform duration-300 w-4 h-4 ml-1" />

                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    <tr v-for="order in orders.data" :key="order.id">
                        <td class="px-4 py-2 text-sm text-center">
                            <Link :href="route('admin.orders.details', order.id)"
                                class="text-indigo-600 hover:underline flex flex-row gap-1 justify-center items-center">
                            <Eye class="w-4 h-4 " />

                            <span>Details</span>
                            </Link>
                        </td>
                        <td class="px-4 py-2 text-sm text-gray-700">#{{ order.id }}</td>
                        <td class="px-4 py-2 text-sm text-gray-700">{{ order.created_at_formatted }}</td>
                        <td class="px-4 py-2 text-sm text-gray-700">
                            {{ order.user?.name }}<br />
                            <span class="text-xs text-gray-500">{{ order.user?.email }}</span>
                        </td>
                        <td class="px-4 py-2 text-sm">
                            <span :class="[
                                'px-2 py-1 rounded-full text-xs font-semibold',
                                order.status === 'delivered'
                                    ? 'status-delivered'
                                    : order.status === 'inprogress'
                                        ? 'status-inprogress'
                                        : order.status === 'pending'
                                            ? 'status-pending'
                                            : order.status === 'late'
                                                ? 'status-late'
                                                : order.status === 'refunded'
                                                    ? 'status-refunded'
                                                    : order.status === 'cancelled'
                                                        ? 'status-cancelled'
                                                        : 'status-completed'
                            ]">
                                {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
                            </span>

                        </td>
                        <td class="px-4 py-2 text-sm text-gray-700">${{ order.price_paid }}</td>
                        <td class="px-4 py-2 text-sm text-gray-700"> {{ order.completed_order_items_count }}/{{
                            order.order_items_count }}</td>
                        <td class="px-4 py-2 text-sm text-gray-700">{{ order.updated_at_formatted }}</td>

                    </tr>
                </tbody>
            </table>

            <PaginationLinks v-if="orders.data.length" :links="orders.links" :filters="{
                ...props.filters,
                ...localFilters.value,
                sortField: props.filters.sortField,
                sortOrder: props.filters.sortOrder
            }" class="mt-4" />
            <div v-else class="p-4 text-center text-red-500">
                <NoDataMessage message1="No orders found." message2="Try adjusting your filters." />
            </div>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { ref, computed } from "vue";
import { router } from "@inertiajs/vue3";
import { Link } from "@inertiajs/vue3";
import PaginationLinks from "@/Components/PaginationLinks.vue";
import { ChevronsUp, ChevronsUpDown, Eye } from "lucide-vue-next";
import { usePage } from '@inertiajs/vue3';
import DateRangeFilter from "@/Components/DateRangeFilter.vue";
import Filters from "./Filters.vue";
import NoDataMessage from '@/Components/NoDataMessage.vue';


// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    orders: {
        type: Object,
        default: () => ({
            data: [],
            links: [],
        }),
    },
    filters: {
        type: Object,
        default: () => ({
            searchTerm: '',
            orderStatus: '',
            sortField: 'id',
            sortOrder: 'desc',
            perPage: 10,
        }),
    },
    statuses: {
        type: Array,
        default: () => [],
    },
});

// ==========================================================
// Refs & Computed
// ==========================================================
const localFilters = ref({
    searchTerm: props.filters.searchTerm || "",
    orderStatus: props.filters.orderStatus || "",
    perPage: props.filters.perPage || 10,
});

// get query params page
const page = computed(() => {
    const queryPage = usePage().url.split('?')[1]?.split('&').find(param => param.startsWith('page='));
    return queryPage ? Number(queryPage.split('=')[1]) : 1;
});

const showingCount = computed(() => {
    const perPage = props.filters.perPage || 10;
    const currentPage = page.value || 1;
    const itemsOnPage = props.orders.data.length; // <<< add props. here

    const totalShown = (currentPage - 1) * perPage + itemsOnPage;
    return totalShown > props.orders.total ? props.orders.total : totalShown;
});

// ==========================================================
// Methods - Apply Filters
// Triggers a search with updated filter values
// ==========================================================
function applyFilters(updatedFilters) {
    localFilters.value = { ...updatedFilters };

    console.log(localFilters)

    router.get(route("admin.orders.index"), {
        ...props.filters,
        ...localFilters.value,
    }, {
        preserveState: true,
        preserveScroll: true,
    });
}

// ==========================================================
// Methods - On Filters Change
// Triggers a search with updated filter values
// ==========================================================
function onFiltersChange(updatedFilters) {
    router.get(route('admin.orders.index'), {
        ...props.filters,
        ...localFilters.value,
        ...updatedFilters,
    }, {
        preserveScroll: true,
        preserveState: true,
    });
}



// ==========================================================
// Methods - Sorting
// Sorts the orders table by a specific field
// ==========================================================
function sort(field) {
    const newDirection =
        props.filters.sortField === field && props.filters.sortOrder === "asc"
            ? "desc"
            : "asc";

    router.get(route("admin.orders.index"), {
        ...props.filters,
        ...localFilters.value,
        sortField: field,
        sortOrder: newDirection,
    }, {
        preserveState: true,
        preserveScroll: true,
    });
}

</script>
