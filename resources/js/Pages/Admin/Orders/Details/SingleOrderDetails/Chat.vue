<template>
    <div class="flex flex-col h-full">
        <!-- Chat Display -->
        <div ref="chatContainer"
            class="p-4 pt-0 border rounded-t-xl bg-white flex flex-col gap-3 max-h-[400px]  min-h-[400px] overflow-y-auto relative">
            <div class="sticky pt-4 top-0 bg-white z-10 -mt-4 -mx-4 px-4 pb-2 border-b">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">Chat</h3>
                </div>
            </div>
            <!-- Messages Container -->
            <div v-if="messages.length === 0" class="flex-1 flex items-center justify-center min-h-[200px]">
                <div class="text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <p class="text-sm">No messages yet</p>
                    <p class="text-xs mt-1">Start the conversation</p>
                </div>
            </div>
            <div v-else v-for="message in messages" :key="message.id" :data-message-id="message.id" :class="message.sender_id === senderId
                ? 'self-end bg-blue-500 text-white'
                : 'self-start bg-gray-100 text-gray-900'" class="p-3 rounded-xl w-fit max-w-[75%] mt-4">
                <div class="whitespace-pre-line">{{ message.text }}</div>
                <div v-if="message.attachment_path" class="mt-1">
                    <a :href="'/storage/' + message.attachment_path" target="_blank"
                        class="text-sm text-blue-200 underline">View
                        Attachment</a>
                </div>
                <div class="flex items-center justify-end gap-1 mt-1">
                    <span class="text-xs opacity-70">{{ formatTime(message.created_at) }}</span>
                </div>
            </div>
        </div>

    </div>
</template>


<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { formatDistanceToNow } from 'date-fns'

const props = defineProps({
    item: {
        type: Object,
        required: true
    },
    order: {
        type: Object,
        required: true
    }
});

const messages = ref([])
const chatContainer = ref(null)
const orderItemId = ref(props.item.id)
const hasNewMessages = ref(false)
const senderId = ref(props.order.user_id)

const fetchMessages = async () => {
    const { data } = await axios.get(route('messages.index', orderItemId.value))
    messages.value = data
    hasNewMessages.value = false
}


const formatTime = (date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
}

onMounted(() => {
    fetchMessages();

})




</script>

<style scoped></style>
