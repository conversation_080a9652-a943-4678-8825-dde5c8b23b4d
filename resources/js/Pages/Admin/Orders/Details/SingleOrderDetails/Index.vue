<template>
    <div class="flex justify-between items-center">
        <div class="min-w-0 flex-1 ">
            <h2
                class="flex justify-start text-2xl text-gray-900 font-bold gap-1 items-center mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                <Link v-if="order?.id" :href="route('admin.orders.details', order.id)" class="link-indigo-icon">
                <CircleChevronLeft class="h-6 w-6" />
                </Link>
                <span>
                    Orders Item Details
                </span>
            </h2>

            <!-- <div>
                {{ item }}
            </div> -->

            <div>
                <div class="ms-8 mt-4 ">
                    <div class="text-sm">
                        <div class="flex flex-row gap-2 ">
                            <span class="font-bold">Order By:</span>

                            <Link v-if="order.user?.id" :href="route('admin.users.show', order.user.id)"
                                class="text-blue-600 hover:underline">
                            <span>{{ order.user?.name }}</span>
                            </Link>
                            <span v-else>{{ order.user?.name || 'N/A' }}</span>
                        </div>
                        <div class="flex flex-row gap-2 ">
                            <span class="font-bold">Advertiser Email:</span>
                            <span>{{ order.user?.email }}</span>
                        </div>
                        <div class="flex flex-row gap-2 ">
                            <span class="font-bold">Publisher:</span>
                            <Link v-if="item.website?.publisher?.id"
                                  :href="route('admin.users.edit', item.website.publisher?.id ?? 1)"
                                class="text-blue-600 hover:underline">
                            <span>{{ item.website.publisher?.name ?? 'System' }}</span>
                            </Link>
                            <span v-else>{{ item.website?.publisher?.name || 'N/A' }}</span>
                        </div>
                        <div class="flex flex-row gap-2 ">
                            <span class="font-bold">Publisher Email:</span>
                            <span>{{ item.website.publisher?.email ?? '<EMAIL>' }}</span>
                        </div>
                        <div class="flex flex-row gap-2 ">
                            <span class="font-bold">Time Elapsed:</span>
                            <span>{{ item.time_elapsed }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-col items-end gap-2">
            <Link v-if="item.refund?.id" :href="route('admin.refunds.show', item.refund.id)"
                class="group inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-indigo-600 text-white font-semibold shadow hover:bg-indigo-700 transition-all focus:outline-none focus:ring-2 focus:ring-indigo-400"
                title="View the refund request for this order">
            <WalletCards class="h-5 w-5 transition-transform group-hover:translate-x-1" />
            <span>View Refund Request</span>
            </Link>
            <h3 class="text-xl p-4 bg-gray-100 font-bold mt-2">
                Due Date: {{ item.estimated_publication_date_formatted }}
            </h3>
        </div>
    </div>
    <div class="flex flex-col gap-3">
        <div class="mt-5">
            <Header :order="order" :item="item"></Header>
        </div>
        <div class="flex flex-row gap-3">
            <div class=" w-2/3  flex flex-col gap-3 ">
                <!-- <div class="border p-6 bg-white">
                    <Article></Article>
                </div> -->

                <div class="border p-6 bg-white  paper-shadow"
                    v-show="item.content != null || item.state_name == orderItemStates.ContentPending">
                    <ContentForm :item="item" :media="media" @content-updated="reloadLogs" />

                </div>

                <div class="border p-6 bg-white paper-shadow">
                    <RequirementForm :order="order" :item="item" @requirements-updated="reloadLogs" />
                </div>

                <div class="border p-6 bg-white paper-shadow" v-if="item.content != null && item.requirements != null">
                    <Publication :item="item" :revisions="revisions" />
                </div>

            </div>
            <div class=" w-1/3  flex flex-col gap-3">
                <div class="bg-white border p-6 paper-shadow">
                    <!-- <VerticalTimeline></VerticalTimeline> -->
                    <VerticalTimeline :currentState="item.state_name"
                        :isContentProvidedByCustomer="item.is_content_provided_by_customer" />
                </div>
                <div class="paper-shadow">
                    <Chat :item="item" :order="order"></Chat>
                </div>
            </div>
        </div>
        <div class="my-10 border-t pt-10">
            <OrderItemLogActivity ref="logComponent" :order-item-id="item.id" />
        </div>
    </div>
</template>
<script setup>
// ==========================================================
// Imports
// ==========================================================
import Header from './Header.vue';
import ContentForm from './ContentForm.vue';
import RequirementForm from './RequirementForm.vue';
import VerticalTimeline from './VerticalTimeline.vue';
import Chat from './Chat.vue';
import { CircleChevronLeft, WalletCards } from 'lucide-vue-next';
import { ref, inject } from 'vue';
import OrderItemLogActivity from './OrderItemLogActivity.vue';
import Publication from '@/Pages/Publisher/Orders/Details/Publication.vue';

// ==========================================================
// Props
// ==========================================================
defineProps({
    order: Object,
    item: Object,
    media: Object,

});

// Ref for log component
const logComponent = ref(null)

// Trigger refresh when needed
const reloadLogs = () => {
    logComponent.value?.refresh()
}

const page = inject("page");
const orderItemStates = page.props.orderItemStates;

</script>


<style lang="scss" scoped></style>