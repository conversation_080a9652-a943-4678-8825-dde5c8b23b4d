<template>
    <div class="overflow-x-auto">


        <div class="flex flex-row items-center justify-between bg-gray-100 p-4 rounded-md">
            <h2 class="text-xl font-semibold text-gray-800 ">Activity Logs
                <span class="badge-green ms-1">{{ logs.total }}</span>
            </h2>

            <div class="flex flex-row items-center gap-2">
                <button class="btn-gray-selected" @click="showLogs = !showLogs">
                    <ChevronUp v-if="showLogs" class="w-4 h-4" />
                    <ChevronDown v-else class="w-4 h-4" />
                </button>
            </div>
        </div>

        <div v-if="loading" class="text-gray-500 text-sm">Loading logs...</div>
        <div v-if="showLogs">
            <table v-if="logs.data?.length" class="mt-4 min-w-full divide-y divide-gray-200 border rounded shadow-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="text-left px-4 py-2">ID</th>
                        <th class="text-left px-4 py-2">Model</th>
                        <th class="text-left px-4 py-2">Description</th>
                        <th class="text-left px-4 py-2">User</th>
                        <th class="text-left px-4 py-2">Changes</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    <tr v-for="log in logs.data" :key="log.id">
                        <td class="px-4 py-2 text-sm">{{ log.id }}</td>
                        <td class="px-4 py-2 text-sm">{{ log.subject_type }}</td>
                        <td class="px-4 py-2 text-sm">{{ log.description }}</td>
                        <td class="px-4 py-2 text-sm">
                            <div>{{ log.user || 'System' }}</div>
                        </td>
                        <td class="px-4 py-2 text-xs text-gray-700 whitespace-pre-wrap">
                            <div v-if="log.changes?.attributes">
                                <div v-for="(val, key) in log.changes.attributes" :key="key" class="flex gap-1">
                                    <span class="font-medium">{{ key }}:</span>
                                    <span class="text-green-600">{{ val }}</span>
                                    <span v-if="log.changes.old?.[key] !== undefined" class="text-gray-400">
                                        (was <span class="text-red-500">{{ log.changes.old[key] }}</span>)
                                    </span>
                                </div>
                            </div>
                            <div v-else>-</div>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div v-if="logs.data?.length">
                <PaginationLinks :links="logs.links" class="mt-4" @navigate="fetchLogs" />
            </div>
            <div v-else-if="!loading && !logs.data?.length" class="text-gray-500 text-sm">
                No activity log found for this order item.
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, defineExpose } from 'vue'
import axios from 'axios'
import PaginationLinks from '@/Components/PaginationLinks.vue'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'

const props = defineProps({
    orderItemId: {
        type: Number,
        required: true,
    },
})

const logs = ref([])
const loading = ref(true)
const showLogs = ref(false)



const fetchLogs = async (url = null) => {
    loading.value = true
    try {
        const res = await axios.get(url || route('admin.orders.logs', { orderItemId: props.orderItemId }))
        logs.value = res.data.props.logs
    } catch (err) {
        console.error('Failed to fetch activity logs:', err)
    } finally {
        loading.value = false
    }
}

onMounted(fetchLogs)

watch(() => props.orderItemId, fetchLogs)

// Expose fetchLogs method to parent

defineExpose({
    refresh: fetchLogs,
})
</script>