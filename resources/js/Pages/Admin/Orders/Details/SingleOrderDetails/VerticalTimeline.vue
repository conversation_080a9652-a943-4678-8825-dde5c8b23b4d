<template>
    <div class="flex flex-col items-start  relative">
        <!-- Vertical Line -->
        <div class="pb-0 w-full -top-0">
            <h3 class="text-base/7 font-semibold text-gray-900  w-full">Status</h3>
        </div>
        <div class="flex flex-col items-start space-y-6 relative">
            <!-- Horizontal line with visible height -->
            <div class=" w-full h-[0px] -top-0 absolute border-dashed border-b-3 border-b-gray-400 "></div>

            <!-- Vertical line -->
            <div
                class="absolute w-[1px] left-[16px]  border-s border-s-3 border-s-gray-400 border-dashed h-full transform -translate-x-1/2">
            </div>

            <!-- Timeline Items -->
            <div v-for="(item, index) in timeline" :key="item.id" class="flex items-center space-x-4">
                <!-- Timeline Circle & Icon -->
                <div class="relative flex items-center justify-center">
                    <div class="w-8 h-8 flex items-center justify-center rounded-full border-2"
                        :class="item.completed ? 'border-green-500 bg-green-100' : 'border-gray-400 bg-white'">
                        <Check v-if="item.isCurrent" class="text-green-500 w-4 h-4" />
                        <Check v-else-if="item.completed" class="text-green-500 w-4 h-4" />
                        <Hourglass v-else class="text-gray-400 w-4 h-4" />
                    </div>
                </div>

                <!-- Timeline Label -->
                <span class="text-base/7  font-semibold text-gray-700">{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { computed } from 'vue';
import { Check, Hourglass } from 'lucide-vue-next';
import { stateMilestones } from '@/helpers/utils';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    currentState: String,
    isContentProvidedByCustomer: {
        type: Number,
        default: 0
    }
});

// ==========================================================
// Computed - Active State
// Tracks the currently active state from props
// ==========================================================
const currentState = computed(() => props.currentState);

// ==========================================================
// Computed - Timeline Steps
// Builds the timeline items and marks current/complete steps
// ==========================================================
const timeline = computed(() => {
    const currentStep = stateMilestones[currentState.value] || 1;

    // Base states that are common for both paths
    const baseStates = [
        { id: 1, label: 'Requirements Pending', completed: currentStep >= 1 },
        { id: 2, label: 'Requirements Awaiting Publisher Approval', completed: currentStep >= 2 },
        { id: 3, label: 'Requirements Revision Requested', completed: currentStep >= 3 },
    ];

    // States specific to when content is provided by customer
    const customerContentStates = [
        { id: 4, label: 'Content Pending', completed: currentStep >= 4 },
        { id: 5, label: 'Content Awaiting Publisher Approval', completed: currentStep >= 5 },
    ];

    // States specific to when content is written by us
    const writerContentStates = [
        { id: 4, label: 'Content Pending', completed: currentStep >= 4 },
        { id: 5, label: 'Content Assigned to Writer', completed: currentStep >= 5 },
        { id: 6, label: 'Content Advertiser Review', completed: currentStep >= 6 },
        { id: 7, label: 'Content Revision Requested', completed: currentStep >= 7 },
        { id: 8, label: 'Content Awaiting Publisher Approval', completed: currentStep >= 8 },
    ];

    // Common states after content approval
    const commonStates = [
        { id: 9, label: 'Publication In Process', completed: currentStep >= 9 },
        { id: 10, label: 'Publication Delivered', completed: currentStep >= 10 },
        { id: 11, label: 'Publication Revision Requested', completed: currentStep >= 11 },
        { id: 12, label: 'Order Completed', completed: currentStep >= 12 },
        // { id: 13, label: 'Order Cancelled', completed: currentStep >= 13 },
        // { id: 14, label: 'Refund Requested', completed: currentStep >= 14 },
        // { id: 15, label: 'Refunded to Wallet', completed: currentStep >= 15 }
    ];

    const orderCancelledStates = [
        { id: 13, label: 'Order Cancelled', completed: currentStep >= 13 },
        { id: 14, label: 'Refund Requested', completed: currentStep >= 14 },
        { id: 15, label: 'Refunded to Wallet', completed: currentStep >= 15 }
    ];

    // const isCancelled = props.currentState === 'order-item-cancelled' || props.currentState === 'order-item-refund-requested' || props.currentState === 'refunded-to-wallet';

    let isCancelled = false;
    if (props.currentState === 'order-item-cancelled' || props.currentState === 'order-item-refund-requested' || props.currentState === 'refunded-to-wallet') {
        isCancelled = true;
    }

    // Combine states based on content provider
    const allSteps = [
        ...baseStates,
        ...(props.isContentProvidedByCustomer === 1 ? customerContentStates : writerContentStates),
        ...(isCancelled == true ? [] : commonStates),
        ...(isCancelled ? orderCancelledStates : [])
    ];

    return allSteps.map((step, index, arr) => {
        const isLastCompleted =
            step.completed &&
            (index === arr.length - 1 || !arr[index + 1].completed);

        return {
            ...step,
            isCurrent: isLastCompleted,
        };
    });
});
</script>


<style scoped>
/* Aligns the timeline items vertically */
.flex-col-reverse {
    display: flex;
    flex-direction: column-reverse;
}
</style>
