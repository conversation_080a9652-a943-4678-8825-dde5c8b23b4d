<template>
    <div>
        <div class="flex flex-row items-center justify-start gap-2">
            <span v-if="title === 'In Progress'" class="bg-gray-100 border border-gray-200 p-2 rounded-full">
                <Hourglass class="w-4 h-4 text-gray-500" />
            </span>
            <span v-if="title === 'Completed'" class="bg-gray-100 border p-2 rounded-full">
                <Check class="w-4 h-4 text-gray-500" />
            </span>
            <span v-if="title === 'Cancelled'" class="bg-gray-100 border p-2 rounded-full">
                <X class="w-4 h-4 text-gray-500" />
            </span>
            <h3 class="text-base font-semibold text-gray-900">{{ title }}</h3>
        </div>
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="mt-5 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full align-middle border">
                        <table v-if="items.length > 0" class="min-w-full divide-y divide-gray-300">
                            <thead>
                                <tr class="bg-gray-100 text-sm">
                                    <th class="w-[130px] px-6 py-3.5 text-left  font-semibold text-gray-900">Details</th>
                                    <!-- <th class="w-[20px] py-3.5 px-6 text-left  font-semibold text-gray-900">ID</th> -->
                                    <th class="px-6 py-3.5 text-left  font-semibold text-gray-900">Website</th>
                                    <th class="w-[180px] px-6 py-3.5 text-left  font-semibold text-gray-900">Amount</th>
                                    <th class="w-[180px] px-6 py-3.5 text-left  font-semibold text-gray-900">Due Date</th>
                                    <th class="w-[130px] px-6 py-3.5 text-left  font-semibold text-gray-900">Content</th>
                                    <th class="w-[250px] px-6 py-3.5 text-left  font-semibold text-gray-900">Status</th>
                                    <th class="w-[180px] px-6 py-3.5 text-left  font-semibold text-gray-900 whitespace-nowrap">Date Ordered</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr v-for="item in items" :key="item.id" class="text-sm">

                                    <td class="whitespace-nowrap px-6 py-4 ">
                                        <Link :href="route('admin.orders.item', { order: item.order_id, item: item.id })" class="text-indigo-600 hover:underline flex flex-row gap-1 justify-start items-center">
                                        <Eye class="w-4 h-4 " />

                                        <span>Details</span>
                                        </Link>
                                    </td>
                                    <!-- <td class="whitespace-nowrap px-6 py-4  text-gray-900 font-medium">
                                        #{{ item.id }}
                                    </td> -->
                                    <td class="whitespace-nowrap px-6 py-4  text-gray-700">
                                        <div class="flex flex-row items-center gap-2 text-gray-900">{{ item.website?.website_domain }}

                                            <a :href="returnWebsiteUrl(item.website)" target="_blank" class="text-indigo-500">
                                                <ExternalLink class="w-4 h-4" />
                                            </a>
                                        </div>
                                        <div class="flex flex-row items-center gap-2 text-gray-400 text-xs">
                                            <div class="flex flex-row items-center gap-1">
                                                <span class="font-bold">ID:</span>
                                                <span>{{ item.id }},</span>
                                            </div>
                                            <div class="flex flex-row items-center gap-1">
                                                <span class="font-bold">Niche:</span>
                                                <span>{{ item.niche }}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4  text-gray-700">
                                        ${{ item.price_paid }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4  text-gray-700">
                                        {{ item.estimated_publication_date_formatted }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4  text-gray-700">
                                        <div>
                                            <ContentStatus v-if="item.is_content_provided_by_customer == 0" :status="item.content_status" />
                                            <div v-if="item.content" class="flex flex-row items-center gap-1 text-xs text-gray-400 mt-1">
                                                <span class="font-bold">Writer:</span>
                                                <span>
                                                    {{ item.content?.writer?.name }}
                                                </span>
                                            </div>
                                        </div>
                                        <span v-if="item.is_content_provided_by_customer == 1" class="text-gray-400">
                                            Provided by advertiser
                                        </span>

                                        <!-- <div>{{ item.content }}</div> -->
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 ">
                                        <span :class="getStatusClass(item.state_name)">
                                            {{ item.state_name }}
                                        </span>
                                    </td>


                                    <td class="whitespace-nowrap px-6 py-4  text-gray-700">
                                        {{ item.created_at_formatted }}
                                    </td>

                                </tr>
                            </tbody>
                        </table>

                        <div v-if="items.length === 0" class="p-4 text-gray-500 text-sm text-center">
                            No Items {{ title }}.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
// ==========================================================
// Imports
// ==========================================================
import { ExternalLink, Hourglass, Check, X, Eye } from 'lucide-vue-next';
import { getStatusClass } from '@/helpers/utils.js';

import ContentStatus from '@/Components/ContentStatus.vue'

import { returnWebsiteUrl } from '@/lib/utils';

// ==========================================================
// Props
// ==========================================================
defineProps({
    items: Object,
    title: String,
});


</script>


<style lang="scss" scoped></style>
