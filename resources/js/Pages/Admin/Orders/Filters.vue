<!-- Admin/Orders/Filters.vue -->
<template>
    <div class="grid grid-cols-6 gap-2 items-center">
        <div class="col-span-3">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Search by Keyword</label>
                <div>
                    <input v-model="localFilters.searchTerm" @input="onSearchInput" placeholder="Search by customer name, email, or amount..."
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm pr-10" />
                </div>
            </div>
        </div>

        <div class="col-span-2">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400">Display</label>
                <div>
                    <select v-model="localFilters.perPage" @change="updateFilters" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                        <option value="10">10 per page</option>
                        <option value="20">20 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-span-1">
            <div class="flex flex-col  w-full">
                <label class="text-xs text-gray-400 whitespace-nowrap">Order Status</label>
                <div>
                    <select v-model="localFilters.orderStatus" @change="emit('update', localFilters)"
                        class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                        <option value="">All</option>
                        <option v-for="status in props.statuses" :key="status.value" :value="status.value">
                            {{ capitalize(status.label) }}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
// ==========================================================
// Imports
// ==========================================================
import { reactive, watch } from 'vue';
import debounce from 'lodash/debounce';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    filters: Object,
    statuses: Array,
});

// ==========================================================
// Emits
// ==========================================================
const emit = defineEmits(['update']);

// ==========================================================
// Refs
// ==========================================================
// Local reactive copy of incoming filters for internal control
const localFilters = reactive({
    searchTerm: props.filters.searchTerm || '',
    perPage: props.filters.perPage || 10,
    orderStatus: props.filters.orderStatus || '',
});

// ==========================================================
// Methods - Debounced Search
// Emit updated filters after debounce when typing in search input
// ==========================================================
const onSearchInput = debounce(() => {
    emit('update', localFilters);
}, 300);

// ==========================================================
// Watchers - Dropdown Filters
// Watch changes to perPage or orderStatus and emit updated filters
// ==========================================================
watch(() => [localFilters.perPage, localFilters.orderStatus], () => {
    emit('update', localFilters);
});

// ==========================================================
// Helpers
// Capitalize first letter of status label
// ==========================================================
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}
</script>
