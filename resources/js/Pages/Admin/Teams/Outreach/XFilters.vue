<template>
  <div class="flex flex-row gap-x-4 items-center">

    <div class="">
      <div class="flex flex-row gap-x-2">

        <div class="">
          <label class="text-sm text-gray-400">&nbsp;</label>
          <button @click="form.preset_range = 'show_all'" :class="form.preset_range === 'show_all' ? 'btn-gray-selected' : 'btn-gray'" class="whitespace-nowrap">
            <span class="text-xs">All</span>
          </button>
        </div>
        <div class="">
          <label class="text-sm text-gray-400">&nbsp;</label>
          <button @click="form.preset_range = 'today'" :class="form.preset_range === 'today' ? 'btn-gray-selected' : 'btn-gray'" class="whitespace-nowrap">
            <span class="text-xs">Today</span>
          </button>
        </div>

        <div>
          <label class="text-sm text-gray-400">&nbsp;</label>
          <button @click="form.preset_range = 'yesterday'" :class="form.preset_range === 'yesterday' ? 'btn-gray-selected' : 'btn-gray'" class="whitespace-nowrap">
            <span class="text-xs">Yesterday</span>
          </button>
        </div>

        <div>
          <label class="text-sm text-gray-400">&nbsp;</label>
          <button @click="form.preset_range = 'last_7_days'" :class="form.preset_range === 'last_7_days' ? 'btn-gray-selected' : 'btn-gray'" class="whitespace-nowrap">
            <span class="text-xs">Last 7 Days</span>
          </button>
        </div>


        <div>
          <label class="text-sm text-gray-400">&nbsp;</label>
          <button @click="form.preset_range = 'last_30_days'" :class="form.preset_range === 'last_30_days' ? 'btn-gray-selected' : 'btn-gray'" class="whitespace-nowrap">
            <span class="text-xs">Last 30 Days</span>
          </button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-12 gap-x-4 items-center w-full ">
      <div class="col-span-6">
        <div class="flex flex-col  w-full">
          <label class="text-sm text-gray-400">Filter by Date</label>
          <div>
            <select v-model="form.preset_range" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
              <option value="show_all">Show All</option>
              <option value="custom">Custom Range</option>
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="last_7_days">Last 7 Days</option>
              <option value="last_30_days">Last 30 Days</option>
              <option value="last_90_days">Last 90 Days</option>
              <option value="last_12_months">Last 12 Months</option>
            </select>
          </div>
        </div>
      </div>

      <div class="col-span-3" v-if="form.preset_range === 'custom'">
        <div class="flex flex-col  w-full">
          <label class="text-sm text-gray-400">Start Date</label>
          <input type="date" v-model="form.start_date" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
        </div>
      </div>
      <div class="col-span-3" v-if="form.preset_range === 'custom'">
        <div class="flex flex-col  w-full">
          <label class="text-sm text-gray-400">End Date</label> <input type="date" v-model="form.end_date"
            class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
        </div>
      </div>
    </div>



  </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { useForm, router } from '@inertiajs/vue3';
import { watch } from 'vue';
import debounce from 'lodash/debounce';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
  presetRange: String,
  startDate: String,
  endDate: String,
});

// ==========================================================
// Form State
// Initializes the filter form with date values
// ==========================================================
const form = useForm({
  preset_range: props.presetRange || 'show_all',
  start_date: props.startDate || '',
  end_date: props.endDate || '',
});

// ==========================================================
// Methods - Apply Filters
// Triggers router GET request to fetch filtered stats
// ==========================================================
const applyFilters = debounce(() => {
  if (form.preset_range === 'show_all') {
    form.start_date = '';
    form.end_date = '';
  }

  router.get(route('admin.outreach.stats'), {
    ...form,
  }, {
    preserveScroll: true,
    preserveState: true,
  });
}, 300);

// ==========================================================
// Watchers
// Reactively apply filters when range or dates change
// ==========================================================
watch(() => form.preset_range, applyFilters);
watch(() => form.start_date, applyFilters);
watch(() => form.end_date, applyFilters);
</script>
