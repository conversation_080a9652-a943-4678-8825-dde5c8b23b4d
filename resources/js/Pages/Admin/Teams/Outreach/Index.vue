<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <div class="flex flex-row gap-2 items-center">
                    <h2 class="title-1">Outreach Stats</h2>
                </div>
                <p class="subtitle-1">Users with outreach role and their website assignment stats.</p>
                <div class="text-sm text-gray-500">
                    Displaying {{ showingCount }} of {{ users.total }} users with outreach role
                </div>
            </div>
        </div>

        <div class="my-4">
            <!-- <Filters :preset-range="filters?.preset_range" :start-date="filters?.start_date" :end-date="filters?.end_date" /> -->

            <DateRangeFilter :preset-range="filters.preset_range" :start-date="filters.start_date" :end-date="filters.end_date" @update:filters="onFiltersChange" />
        </div>

        <div class="border-b border-gray-200 flow-root mt-8">
            <div class="-mx-4 -my-2 lg:-mx-8 overflow-x-auto sm:-mx-6">
                <div class="align-middle inline-block lg:px-8 min-w-full py-2 sm:px-6">
                    <table v-if="users.data.length" class="divide-gray-300 divide-y min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="text-gray-900 text-left text-sm font-semibold px-4 py-3.5">ID</th>
                                <th class="text-gray-900 text-left text-sm font-semibold px-4 py-3.5">Name</th>
                                <th class="text-gray-900 text-left text-sm font-semibold px-4 py-3.5">Progress</th>
                                <th class="text-gray-900 text-left text-sm font-semibold px-4 py-3.5">Onboarded</th>
                                <th class="text-gray-900 text-left text-sm font-semibold px-4 py-3.5">Rejected</th>
                                <th class="text-gray-900 text-left text-sm font-semibold px-4 py-3.5">
                                    Total Websites
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            <tr v-for="user in users.data" :key="user.id" class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-sm text-gray-900">{{ user.id }}</td>
                                <td class="px-4 py-3 text-sm text-gray-900">
                                    <div>
                                        {{ user.name }}

                                    </div>

                                    <div class="text-xs text-gray-500">
                                        {{ user.email }}
                                    </div>
                                </td>


                                <td class="px-4 py-3 text-sm text-gray-900">
                                    <Link :href="route('admin.websites.index', {
                                        outreach_user_id: user.id,
                                        outreach_status: 'inprogress',
                                        preset_range: filters.preset_range,
                                        start_date: filters.start_date,
                                        end_date: filters.end_date
                                    })" class="badge-inprogress hover:underline">
                                    <span class="text-lg px-3 py-2">
                                        {{ user.inprogress_count }}
                                    </span>
                                    </Link>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">
                                    <Link :href="route('admin.websites.index', {
                                        outreach_user_id: user.id,
                                        outreach_status: 'onboarded',
                                        preset_range: filters.preset_range,
                                        start_date: filters.start_date,
                                        end_date: filters.end_date
                                    })" class="badge-onboarded hover:underline">
                                    <span class="text-lg px-3 py-2">
                                        {{ user.onboarded_count }}
                                    </span>
                                    </Link>
                                </td>
                                <td class="px-4 py-3 text-gray-900">
                                    <Link :href="route('admin.websites.index', {
                                        outreach_user_id: user.id,
                                        outreach_status: 'rejected',
                                        preset_range: filters.preset_range,
                                        start_date: filters.start_date,
                                        end_date: filters.end_date
                                    })" class="badge-rejected hover:underline">

                                    <span class="text-lg px-3 py-2">
                                        {{ user.rejected_count }}
                                    </span>
                                    </Link>
                                </td>
                                <td class="px-4 py-3 text-gray-900 text-sm">
                                    <span class="text-lg px-3 py-2">
                                        {{
                                            (user.inprogress_count || 0) +
                                            (user.onboarded_count || 0) +
                                            (user.rejected_count || 0)
                                        }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div v-else class="p-4 text-center text-red-500">
                        No users found.
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
// import Filters from './Filters.vue'
import { usePage, router } from '@inertiajs/vue3';
import { computed } from 'vue';
import DateRangeFilter from '@/Components/DateRangeFilter.vue';
// ==========================================================
// Props
// Receive paginated outreach user stats and current filter state
// ==========================================================
const props = defineProps({
    users: Object,
    filters: Object,
});

const page = computed(() => {
    const queryPage = usePage().url.split('?')[1]?.split('&').find(param => param.startsWith('page='));
    return queryPage ? Number(queryPage.split('=')[1]) : 1;
});

const showingCount = computed(() => {
    const perPage = props.users.per_page || 10;
    const currentPage = page.value || 1;
    const itemsOnPage = props.users.data.length;

    const totalShown = (currentPage - 1) * perPage + itemsOnPage;
    return totalShown > props.users.total ? props.users.total : totalShown;
});

const onFiltersChange = (newFilters) => {
    router.get(route('admin.outreach.stats'), newFilters, {
        preserveScroll: true,
        preserveState: true,
    });
};
</script>

<style scoped></style>
