<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <div class="flex flex-row gap-2 items-center">
                    <h2 class="title-1">Writing Assignments</h2>
                </div>
                <p class="subtitle-1">All writing tasks that require internal content creation.</p>
                <div class="text-sm text-gray-500">
                    Displaying {{ items.data.length }} of {{ items.total }} assignments
                </div>
            </div>
        </div>
        <div>
            <div class="grid grid-cols-4 gap-4 mt-4">
                <div class="bg-white p-4 rounded-lg shadow">
                    <h3 class="font-medium text-gray-900">In-progress</h3>
                    <h2 class="text-2xl font-bold">{{ stats.in_progress }}</h2>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <h3 class="font-medium text-gray-900">Pending Assignment</h3>
                    <h2 class="text-2xl font-bold">{{ stats.pending_assignment }}</h2>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <h3 class="font-medium text-gray-900">In Revision</h3>
                    <h2 class="text-2xl font-bold">{{ stats.in_revision }}</h2>
                </div>
                <div class="bg-white p-4 rounded-lg shadow">
                    <h3 class="font-medium text-gray-900">Upcoming</h3>
                    <h2 class="text-2xl font-bold">{{ stats.upcoming }}</h2>
                </div>
            </div>
        </div>
        <div class="mt-4">
            <Filters :filters="filters" :statuses="statuses" @update="updateFilters" />
        </div>

        <div class="border-b border-gray-200 flow-root mt-8">
            <div class="-mx-4 -my-2 lg:-mx-8 overflow-x-auto sm:-mx-6">
                <div class="align-middle inline-block lg:px-8 min-w-full py-2 sm:px-6">
                    <table v-if="items.data.length" class="divide-gray-300 divide-y min-w-full">
                        <thead class="bg-gray-50">
                            <tr>

                                <th @click="sort('id')" class="sortable-col ps-4">Order ID
                                    <SortIcon :field="filters.sortField" :order="filters.sortOrder" name="id" />
                                </th>
                                <th class="px-4 py-3.5 text-sm font-semibold text-left text-gray-900">Website</th>
                                <th class="px-4 py-3.5 text-sm font-semibold text-left text-gray-900">Writer</th>
                                <th class="px-4 py-3.5 text-sm font-semibold text-left text-gray-900">Content Status
                                </th>
                                <th class="px-4 py-3.5 text-sm font-semibold text-left text-gray-900">Date Ordered</th>
                                <th class="px-4 py-3.5 text-sm font-semibold text-left text-gray-900">Due Date</th>
                                <!-- <th class="px-4 py-3.5 text-sm font-semibold text-left text-gray-900">Edit</th> -->
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                            <tr v-for="item in items.data" :key="item.id" class="hover:bg-gray-50">

                                <td class="ps-4 py-3 text-sm text-gray-900">
                                    <Link :href="route('admin.orders.assignments.edit', item.id)"
                                        class="hover:underline text-indigo-600 flex flex-row gap-1 items-center whitespace-nowrap">
                                    <FilePenLine class="size-4" />
                                    Edit #{{ item.id }}
                                    </Link>
                                </td>
                                <td class="px-4 py-3 text-sm ">


                                    <div class="flex gap-1 items-center whitespace-nowrap">
                                        <span>
                                            {{ item.website?.website_domain }}
                                        </span>
                                        <a :href="returnWebsiteUrl(item.website)" target="_blank"
                                            class="text-indigo-500">
                                            <ExternalLink class="w-4 h-4" />
                                        </a>

                                    </div>

                                    <div class="text-xs text-gray-500 ">
                                        <div class="flex flex-row gap-1">
                                            <span class="font-bold">Niche: </span><span>{{ item.niche }},</span>
                                            <span class="font-bold">Order State: </span>
                                            <span
                                                :class="isStateBeforeContent(item.state) ? 'text-red-500' : 'text-green-500'">{{
                                                    item.state_label }}</span>
                                        </div>

                                        <Link :href="route('admin.orders.item', [item.order_id, item.id])"
                                            class="text-indigo-600 hover:underline">Order Details</Link>

                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-700">
                                    <template v-if="item.content?.writer">
                                        <div>
                                            <Link :href="route('admin.users.edit', item.content.writer.id)"
                                                class="hover:underline text-indigo-600">
                                            {{ item.content.writer.name }}
                                            </Link>

                                        </div>
                                    </template>
                                    <template v-else>
                                        <span class="text-gray-400 italic">Unassigned</span>
                                    </template>
                                </td>
                                <td class="px-4 py-3 text-sm capitalize text-gray-700">
                                    <!-- {{ item.content_status }} -->
                                    <ContentStatus :status="item.content_status" />
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-700">{{ item.created_at_formatted }}</td>
                                <td class="px-4 py-3 text-sm text-gray-700">
                                    <span v-if="item.estimated_publication_date">{{
                                        item.estimated_publication_date_formatted }}</span>
                                    <span v-else class="text-gray-400 italic">N/A</span>
                                </td>

                                <!-- <td class="px-4 py-3 text-sm">
                                    <Link :href="route('admin.orders.assignments.edit', item.id)" class="text-indigo-600 hover:underline">Edit</Link>
                                </td> -->
                            </tr>
                        </tbody>
                    </table>

                    <div v-else class="p-4 text-center text-red-500">
                        <NoDataMessage message1="No assignments found." message2="Try adjusting your filters." />
                    </div>
                </div>
            </div>
        </div>

        <PaginationLinks :links="items.links" :filters="filters" class="mt-4" />
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { router, Link, usePage } from '@inertiajs/vue3';
import PaginationLinks from '@/Components/PaginationLinks.vue';
import SortIcon from '@/Components/SortIcon.vue';
import Filters from './Filters.vue';
import { ExternalLink, FilePenLine } from 'lucide-vue-next';
import ContentStatus from '@/Components/ContentStatus.vue';
import { returnWebsiteUrl } from '@/lib/utils';
import NoDataMessage from '@/Components/NoDataMessage.vue';

const props = defineProps({
    items: Object,
    perPage: [Number, String],
    statuses: Array,
    stats: Object,
    filters: Object,
});



const page = usePage();

const filters = ref({
    searchTerm: props.filters?.searchTerm || '',
    assignmentStatus: props.filters?.assignmentStatus || 'assigned_unassigned',
    orderStatus: props.filters?.orderStatus || '',
    perPage: props.filters?.perPage || 10,
    sortField: props.filters?.sortField || 'id',
    sortOrder: props.filters?.sortOrder || 'desc',
});


const statuses = computed(() => page.props.statuses || []);



function sort(field) {
    const order = filters.value.sortField === field && filters.value.sortOrder === 'asc' ? 'desc' : 'asc';
    router.get(route('admin.writers.assignments'), {
        ...filters.value,
        sortField: field,
        sortOrder: order
    }, {
        preserveScroll: true,
        preserveState: true
    });
}



function updateFilters(newFilters) {

    filters.value = { ...filters.value, ...newFilters };

    router.get(route('admin.writers.assignments'), {
        ...filters.value,
        page: 1,
    }, {
        preserveScroll: true,
        preserveState: true,
    });
}





const isStateBeforeContent = (item_state) => {
    const states = page.props.orderItemStates;

    const statesBeforeContent = [
        states.RequirementsPending,
        states.RequirementAwaitingPublisherApproval,
        states.RequirementRevisionRequested,
        states.OrderItemCancelled,
        states.OrderItemRefundRequested,
        states.RefundedToWallet
    ];

    return statesBeforeContent.includes(item_state);
};
</script>

<style scoped>
.sortable-col {
    @apply text-left text-sm font-semibold text-gray-900 cursor-pointer px-4 py-3.5;
}
</style>
