<template>
    <div class="sm:flex sm:items-center">

        <div class="sm:flex-auto">
            <h2
                class="flex justify-start text-2xl text-gray-900 font-bold gap-1 items-center mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                <Link :href="route('admin.writers.assignments')" class="link-indigo-icon">
                <CircleChevronLeft class="h-6 w-6" />
                </Link>
                <span>Assignment Details</span>
            </h2>
            <p class="text-gray-700 text-sm mt-2">View and update assignment writer.</p>
        </div>
    </div>

    <div class="mt-8 bg-gray-50 shadow-md ring-1 ring-gray-900/5 sm:rounded-xl px-6 py-8">


        <div class="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-2">

            <div>
                <label class="text-gray-900 text-sm block font-medium">Assignment ID</label>
                <p class="mt-1 text-sm text-gray-700">{{ item.id }}</p>
            </div>

            <div>
                <label class="text-gray-900 text-sm block font-medium">Website</label>
                <div class="mt-1 flex gap-2 items-center">
                    <span class="text-sm text-gray-700">{{ item.website?.website_domain }}</span>
                    <a :href="returnWebsiteUrl(item.website)" target="_blank" class="text-indigo-600 hover:underline">
                        <ExternalLink class="w-4 h-4" />
                    </a>
                </div>
            </div>

            <div>
                <label class="text-gray-900 text-sm block font-medium">Assign Writer</label>
                <div v-if="item.state_name !== 'requirements-pending' && item.state_name !== 'requirement-awaiting-publisher-approval'"
                    class="mt-1 flex gap-2 items-center">
                    <template v-if="writer">
                        <span class="text-sm text-gray-700">
                            {{ writer.name }} (ID: {{ writer.id }})
                        </span>
                    </template>
                    <template v-else>
                        <span class="text-sm italic text-gray-400">Unassigned</span>
                    </template>
                    <button @click="openAssignDialog"
                        class="text-indigo-600 hover:underline text-sm flex gap-1 items-center btn-indigo">
                        <UserPlus class="w-4 h-4" />
                        <span>{{ writer ? 'Reassign' : 'Assign' }} Writer</span>
                    </button>
                </div>
                <div v-else class="mt-1 flex gap-2 items-center text-sm text-red-500">
                    <span class="font-bold">Order State:</span> <span class="">{{ item.state_label }}</span>
                </div>
            </div>


            <div>
                <label class="text-gray-900 text-sm block font-medium">Content Status</label>
                <p class="mt-1 text-sm text-gray-700 capitalize">
                    <!-- {{ item.content_status }} -->
                    <ContentStatus :status="content_status" />
                </p>
            </div>

            <div>
                <label class="text-gray-900 text-sm block font-medium">Niche</label>
                <p class="mt-1 text-sm text-gray-700">{{ item.niche }}</p>
            </div>

            <div>
                <label class="text-gray-900 text-sm block font-medium">Date Ordered</label>
                <p class="mt-1 text-sm text-gray-700">{{ item.created_at_formatted }}</p>
            </div>

            <div>
                <label class="text-gray-900 text-sm block font-medium">Due Date</label>
                <p class="mt-1 text-sm text-gray-700">
                    <span v-if="item.estimated_publication_date">
                        {{ item.estimated_publication_date_formatted }}
                    </span>
                    <span v-else class="italic text-gray-400">N/A</span>
                </p>
            </div>

            <div>
                <label class="text-gray-900 text-sm block font-medium">Order Details</label>
                <Link :href="route('admin.orders.item', [item.order_id, item.id])"
                    class="text-indigo-600 hover:underline text-sm">
                View Order
                </Link>
            </div>

        </div>
    </div>

    <AssignWriterDialog v-model:open="showAssignDialog" :item="item" :currentWriterId="item.content?.writer_id"
        :writer="item.content?.writer" @assigned="updateWriter" />
</template>

<script setup>
import { ref, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import { CircleChevronLeft, ExternalLink } from 'lucide-vue-next';
import AssignWriterDialog from '@/Components/AssignWriterDialog.vue';
import ContentStatus from '@/Components/ContentStatus.vue';
import { UserPlus } from "lucide-vue-next";
import { returnWebsiteUrl } from '@/lib/utils';
const props = defineProps({
    item: Object,
    statuses: Array,
    content_status: String,
});

const showAssignDialog = ref(false);

const writer = computed(() => props.item.content?.writer);


function openAssignDialog() {
    showAssignDialog.value = true;
}

function updateWriter(user) {
    if (!props.item.content) {
        props.item.content = {}; // fallback if content was null
    }

    if (user) {
        props.item.content.writer = user;
        props.item.content.writer_id = user.id;
    } else {
        props.item.content.writer = null;
        props.item.content.writer_id = null;
    }

    showAssignDialog.value = false;
}


</script>