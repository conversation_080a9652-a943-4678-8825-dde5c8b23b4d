<template>
    <div class="grid grid-cols-6 gap-2 items-center">
        <div class="col-span-2">
            <div class="flex flex-col w-full">
                <label class="text-xs text-gray-400">Search by Writer Name or Website</label>
                <input v-model="localFilters.searchTerm" @input="onSearchInput" placeholder="Search by writer or website..."
                    class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm pr-10" />
            </div>
        </div>

        <div class="col-span-2">
            <div class="flex flex-col w-full">
                <label class="text-xs text-gray-400">Assignment Status</label>
                <select v-model="localFilters.assignmentStatus" @change="emit('update', localFilters)" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                    <!-- <option value="">All</option> -->
                    <option value="assigned_unassigned">Assigned/Unassigned</option>
                    <option value="completed">Completed</option>
                    <option value="upcoming">Upcoming</option>
                </select>
            </div>
        </div>

        <div class="col-span-1">
            <div class="flex flex-col w-full">
                <label class="text-xs text-gray-400">Order State</label>
                <select v-model="localFilters.orderStatus" @change="emit('update', localFilters)" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                    <option value="">All</option>
                    <option v-for="status in statuses" :key="status.value" :value="status.value">
                        {{ capitalize(status.label) }}
                    </option>
                </select>
            </div>
        </div>

        <div class="col-span-1 ">
            <div class="flex flex-col w-full">
                <label class="text-xs text-gray-400">Per Page</label>
                <select v-model="localFilters.perPage" @change="emit('update', { ...localFilters, perPage: Number(localFilters.perPage) })"
                    class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive } from 'vue';
import debounce from 'lodash/debounce';

const props = defineProps({
    filters: Object,
    statuses: Array,
});

const emit = defineEmits(['update']);

const localFilters = reactive({
    searchTerm: props.filters.searchTerm || '',
    perPage: Number(props.filters.perPage) || 10,
    orderStatus: props.filters.orderStatus || '',
    assignmentStatus: props.filters.assignmentStatus || 'assigned_unassigned',
});

const onSearchInput = debounce(() => {
    emit('update', {
        ...localFilters,
        perPage: Number(localFilters.perPage),
        
    });
}, 300);

function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}
</script>
