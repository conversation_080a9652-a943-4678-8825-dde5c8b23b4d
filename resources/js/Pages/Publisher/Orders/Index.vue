<template>
    <div>
        <div class="lg:flex lg:items-center lg:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Orders
                </h2>
            </div>
        </div>
        <div class="flex flex-col gap-y-12 mt-8">
            <PendingOrderItems :items="pendingOrderItems" />
            <Completed :orders="completedOrders" />
        </div>
    </div>
</template>

<script setup>
import Completed from "./Completed.vue";
import PendingOrderItems from "./OrderItems/Pending.vue";

defineProps({
    pendingOrderItems: {
        type: Object,
        required: true,
        default: () => []
    },
    completedOrders: {
        type: Object,
        required: true,
        default: () => []
    }
});
</script>

<style lang="scss" scoped></style>