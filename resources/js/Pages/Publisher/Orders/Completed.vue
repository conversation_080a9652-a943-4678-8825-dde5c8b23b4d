<template>
    <div>
        <h3 class="text-base font-semibold text-gray-900">Completed</h3>
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="mt-5 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div v-if="orders.length === 0">
                        <div class="flex flex-col gap-y-3 text-sm border p-4">
                            No completed orders found
                        </div>
                    </div>

                    <div v-else class="inline-block min-w-full align-middle border">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="">
                                <tr class="bg-gray-100">
                                    <th scope="col"
                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0 sm:px-6 lg:px-6">
                                        Order ID
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Customer
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Topic
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Status
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Amount
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Niche
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Date
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr v-if="orders.length === 0">
                                    <td colspan="7" class="px-3 py-4 text-sm text-gray-500 text-center">
                                        No completed orders found
                                    </td>
                                </tr>
                                <tr v-for="order in orders" :key="order.id">
                                    <td class="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
                                        <div class="flex items-center">
                                            <div class="ml-6">
                                                <div class="font-medium text-gray-900">#{{ order.id }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="text-gray-900">{{ order.customer_name }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="mt-1 text-gray-500">{{ order.topic }}</div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <span :class="[
                                            'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium',
                                            order.status === 'delivered' || order.status === 'completed'
                                                ? 'bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20'
                                                : order.status === 'inprogress'
                                                    ? 'bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20'
                                                    : order.status === 'pending'
                                                        ? 'bg-yellow-50 text-gray-700 ring-1 ring-inset ring-yellow-600/20'
                                                        : order.status === 'cancelled'
                                                            ? 'bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20'
                                                            : 'bg-gray-50 text-gray-700 ring-1 ring-inset ring-gray-600/20'
                                        ]">
                                            {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">${{
                                        order.price_paid
                                    }}</td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">{{ order.niche }}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">{{
                                        order.created_at }}
                                    </td>

                                    <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                        <div class="flex items-center">
                                            <Link :href="route('publisher.orders.details', { id: order.id })"
                                                class="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                            Order Details
                                            <ChevronRight color="" :size="32" class="-mr-0.5 size-5" />
                                            </Link>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Link } from '@inertiajs/vue3';
import { ChevronRight } from 'lucide-vue-next';

defineProps({
    orders: {
        type: Object,
        required: true,
        default: () => []
    },
});

const loading = ref(true);

onMounted(() => {
    setTimeout(() => {
        loading.value = false;
    }, 1000);
});
</script>

<style lang="scss" scoped></style>