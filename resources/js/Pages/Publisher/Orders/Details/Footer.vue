<template>
    <div class="">
        <h3 class="text-base/7 font-semibold text-gray-900 mb-5">Details</h3>
        <div class="flex flex-col gap-2">

            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                <dt class="text-sm/6 font-medium text-gray-900">Client Link:</dt>
                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <a :href="item.requirements?.advertiser_url" target="_blank"
                        class="flex flex-row gap-1 items-center text-blue-500 hover:text-blue-700">

                        <span>{{ item.requirements?.advertiser_url ?? 'N/A' }}</span>
                        <ExternalLink class="w-4 h-4" v-if="item.requirements?.advertiser_url" />
                    </a>
                </dd>
            </div>
            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                <dt class="text-sm/6 font-medium text-gray-900">Anchor Keyword:</dt>
                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">

                    {{ item.requirements?.anchor_text ?? 'N/A' }}

                </dd>
            </div>
            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                <dt class="text-sm/6 font-medium text-gray-900">Topic:</dt>
                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                    {{ item.requirements?.article_topic ?? 'N/A' }}
                </dd>
            </div>
            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                <dt class="text-sm/6 font-medium text-gray-900">Comments:</dt>
                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                    {{ item.requirements?.requirement_comments ?? 'N/A' }}
                </dd>
            </div>
        </div>
    </div>
</template>

<script setup>

import { Check, CheckCheck, ChevronRight, ExternalLink } from 'lucide-vue-next';


defineProps({
    order: Object,
    item: Object,
})
</script>


<style lang="scss" scoped></style>