<template>
    <div>
        <div class="flex flex-col gap-3 mt-5 border border-gray-200 p-1 sm:p-5 bg-gray-100">
            <div class="border border-gray-200 bg-white rounded-lg shadow-sm">
                <div class="p-6">
                    <!-- Header with Actions -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                        <div class="flex flex-col sm:flex-row items-center gap-3">
                            <h3 class="text-lg font-semibold text-gray-900">Order Status:</h3>
                            <div class="flex items-center gap-2">
                                <span class="px-2.5 py-0.5 text-xs font-medium rounded-full" :class="getStatusClass(item.state_name)">
                                    {{ item.state_label || 'Not Started' }}
                                </span>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row items-center gap-3"
                            v-if="item.state_name === orderItemStates.ContentAwaitingPublisherApproval || item.state_name === orderItemStates.RequirementAwaitingPublisherApproval">
                            <button @click="showApproveModal = true"
                                class="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200">
                                {{ item.state_name ===
                                    orderItemStates.ContentAwaitingPublisherApproval ? 'Approve Content' :
                                    'Approve Requirements' }}
                            </button>
                            <button @click="showDisapproveModal = true"
                                class="px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200">
                                Request Revision
                            </button>
                            <button v-if="item.state_name === orderItemStates.RequirementAwaitingPublisherApproval" @click="showCancelModal = true"
                                class="px-3 py-1.5 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200">
                                Cancel Order
                            </button>
                        </div>
                    </div>

                    <!-- Divider -->
                    <div class="border-t border-gray-200 mb-6"></div>

                    <!-- Content Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Order ID</dt>
                                <dd class="text-sm text-gray-900">{{ order.id }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Time Elapsed</dt>
                                <dd class="text-sm text-gray-900">{{ item.time_elapsed }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Date</dt>
                                <dd class="text-sm text-gray-900">{{ order.created_at_formatted }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Due Date</dt>
                                <dd class="text-sm text-gray-900">{{ item.estimated_publication_date_formatted }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Advertiser Name</dt>
                                <dd class="text-sm text-gray-900">{{ order.user?.name || 'N/A' }}</dd>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Niche</dt>
                                <dd class="text-sm text-gray-900">{{ item.niche }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Earning</dt>
                                <dd class="text-sm text-gray-900">${{ item.price_paid }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Website</dt>
                                <dd class="text-sm text-gray-900">
                                    <div class="flex items-center gap-2">
                                        {{ item.website?.website_domain || 'N/A' }}
                                        <a v-if="item.website" :href="returnWebsiteUrl(item.website)" target="_blank" class="text-indigo-500">
                                            <ExternalLink class="w-4 h-4" />
                                        </a>
                                    </div>
                                </dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                <dd class="text-sm text-gray-900">{{ item.updated_at_formatted || 'N/A' }}</dd>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-0">
                                <dt class="text-sm font-medium text-gray-500">Published URL</dt>
                                <dd class="text-sm text-gray-900">
                                    <div class="flex items-center gap-2">
                                        {{ item.publication?.publication_url || 'Not published yet' }}
                                        <a v-if="item.publication?.publication_url" :href="item.publication?.publication_url" target="_blank" class="text-indigo-500">
                                            <ExternalLink class="w-4 h-4" />
                                        </a>
                                    </div>
                                </dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="order.user?.advertiser_guidelines?.guidelines_for_publisher" class="px-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Advertiser Guidelines</h3>
                    <p class="text-gray-600 text-sm">{{ order.user?.advertiser_guidelines?.guidelines_for_publisher }}</p>
                </div>
            </div>
        </div>

        <!-- Approve Confirmation Modal -->
        <div v-if="showApproveModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white p-6 rounded-lg max-w-md w-full mx-4 shadow-xl">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Approval</h3>
                <p class="text-gray-600 mb-6">Are you sure you want to approve this {{ item.state_name ===
                    orderItemStates.RequirementAwaitingPublisherApproval ? 'requirement' : 'content' }}?</p>
                <div class="flex justify-end gap-4">
                    <button @click="showApproveModal = false" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Cancel
                    </button>
                    <button @click="confirmApprove" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        Confirm Approval
                    </button>
                </div>
            </div>
        </div>

        <!-- Disapprove Modal -->
        <div v-if="showDisapproveModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white p-6 rounded-lg max-w-md w-full mx-4 shadow-xl">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Request Revision</h3>
                <textarea v-model="revisionReason" class="w-full border border-gray-300 rounded-md p-2 mb-4 focus:ring-2 focus:ring-red-500 focus:border-red-500" rows="4"
                    placeholder="Please provide details for the revision request..."></textarea>
                <div class="flex justify-end gap-4">
                    <button @click="showDisapproveModal = false" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Cancel
                    </button>
                    <button @click="requestRevision" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        Submit Revision Request
                    </button>
                </div>
            </div>
        </div>

        <!-- Cancel Order Modal -->
        <div v-if="showCancelModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white p-6 rounded-lg max-w-md w-full mx-4 shadow-xl">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Cancel Order</h3>
                <p class="text-gray-600 mb-6">Are you sure you want to cancel this order? This action cannot be undone.
                </p>
                <div class="flex justify-end gap-4">
                    <button @click="showCancelModal = false" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        No, Keep Order
                    </button>
                    <button @click="cancelOrder" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        Yes, Cancel Order
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue'
import { ExternalLink } from 'lucide-vue-next'
import { getStatusClass } from '@/helpers/utils.js'
import { router } from '@inertiajs/vue3'
import { returnWebsiteUrl } from '@/lib/utils';

const props = defineProps({
    order: Object,
    item: Object,
})

const page = inject("page");
const orderItemStates = page.props.orderItemStates;


const notify = inject('$notify')
const showDisapproveModal = ref(false)
const showApproveModal = ref(false)
const showCancelModal = ref(false)
const revisionReason = ref('')

const confirmApprove = () => {
    showApproveModal.value = false
    approveOrder()
}

const approveOrder = () => {
    var routeName = props.item.state_name === orderItemStates.RequirementAwaitingPublisherApproval ?
        "publisher.orders.item.approve-requirement" : "publisher.orders.item.approve-content";

    router.post(route(routeName, props.item.id), {}, {
        preserveScroll: true,
        onSuccess: () => notify("Requirements approved successfully!", { type: "success" }),
        onError: (errors) => console.log(errors),
    });

}


const requestRevision = () => {
    if (!revisionReason.value.trim() || revisionReason.value.trim().length < 10) {
        notify('Please provide a detailed reason for revision (minimum 10 characters)', { type: 'error' })
        return
    }
    var routeName = props.item.state_name === orderItemStates.ContentAwaitingPublisherApproval ?
        "publisher.orders.item.request-content-revision" : "publisher.orders.item.request-revision";

    router.post(route(routeName, props.item.id), {
        reason: revisionReason.value
    }, {
        preserveScroll: true,
        onSuccess: () => {
            notify('Revision requested successfully', { type: 'success' })
            showDisapproveModal.value = false
            revisionReason.value = ''
            router.reload({ only: ['order'] })
        },
        onError: (errors) => {
            notify('Failed to request revision', { type: 'error' })
            console.error(errors)
        }
    });

}


const cancelOrder = () => {
    router.post(route("publisher.orders.item.cancel", props.item.id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            notify('Order cancelled successfully', { type: 'success' })
            showCancelModal.value = false
            router.reload({ only: ['order'] })
        },
        onError: (errors) => {
            notify('Failed to cancel order', { type: 'error' })
            console.error(errors)
        }
    });
}
</script>
