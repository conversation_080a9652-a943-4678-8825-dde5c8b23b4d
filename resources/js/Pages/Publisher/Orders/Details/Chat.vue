<template>
    <div class="flex flex-col h-full">
        <!-- Chat Display -->
        <div ref="chatContainer"
            class="p-4 pt-0 border rounded-t-xl bg-white flex flex-col gap-3 max-h-[400px] min-h-[400px] overflow-y-auto relative">
            <div class="sticky pt-4 top-0 bg-white z-10 -mt-4 -mx-4 px-4 pb-2 border-b">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">Chat</h3>
                    <div class="flex items-center gap-2">
                        <button v-if="hasNewMessages" @click="scrollToBottom"
                            class="flex items-center gap-1 px-3 py-1 text-sm bg-blue-500 text-white rounded-full hover:bg-blue-600 transition">
                            <span>New Messages</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </button>
                        <div v-if="hasNewMessages" class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>
            <!-- Messages Container -->
            <div v-if="messages.length === 0" class="flex-1 flex items-center justify-center min-h-[200px]">
                <div class="text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <p class="text-sm">No messages yet</p>
                    <p class="text-xs mt-1">Start the conversation</p>
                </div>
            </div>
            <div v-else v-for="message in messages" :key="message.id" :data-message-id="message.id" :class="message.sender_id === senderId
                ? 'self-end bg-blue-500 text-white'
                : 'self-start bg-gray-100 text-gray-900'" class="p-3 rounded-xl w-fit max-w-[75%] mt-4">
                <div class="whitespace-pre-line">{{ message.text }}</div>
                <div v-if="message.attachment_path" class="mt-1">
                    <a :href="'/storage/' + message.attachment_path" target="_blank"
                        class="text-sm text-blue-200 underline">View
                        Attachment</a>
                </div>
                <div class="flex items-center justify-end gap-1 mt-1">
                    <span class="text-xs opacity-70">{{ formatTime(message.created_at) }}</span>
                </div>
            </div>
        </div>

        <!-- Input Form -->
        <form @submit.prevent="sendMessage" enctype="multipart/form-data" class="border-t bg-gray-50 rounded-b-xl">
            <input type="hidden" v-model="orderItemId" />
            <input type="hidden" v-model="senderId" />
            <input type="hidden" v-model="receiverId" />
            <div class="flex items-center gap-2 px-4 py-3">
                <label class="relative cursor-pointer group" :class="{ 'opacity-50 cursor-not-allowed': isSending }">
                    <input type="file" ref="fileInput" @change="handleFileUpload"
                        accept=".jpg,.jpeg,.png,.doc,.docx,.pdf,.txt" class="hidden" :disabled="isSending" />
                    <div class="p-2 text-gray-600 hover:text-blue-600 transition-colors"
                        :class="{ 'hover:text-gray-600': isSending }">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                    </div>
                    <div v-if="uploadProgress > 0 && uploadProgress < 100"
                        class="absolute -bottom-1 left-0 right-0 h-1 bg-gray-200 rounded-full">
                        <div class="h-full bg-blue-500 rounded-full transition-all duration-300"
                            :style="{ width: uploadProgress + '%' }"></div>
                    </div>
                </label>
                <div v-if="attachment" class="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-lg">
                    <span class="text-sm text-gray-600 truncate max-w-[150px]">{{ attachment.name }}</span>
                    <button @click="removeAttachment" class="text-gray-500 hover:text-red-500" :disabled="isSending">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <textarea ref="messageInput" v-model="newMessage" rows="1" placeholder="Type a message..."
                    class="flex-1 resize-none p-2.5 text-sm text-gray-900 bg-white rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    :disabled="isSending" @keydown.enter.exact.prevent="sendMessage"
                    @keydown.enter.shift="addNewline"></textarea>
                <button type="submit" class="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition"
                    :class="{ 'opacity-50 cursor-not-allowed': isSending }" :disabled="isSending">
                    <svg class="w-5 h-5 rotate-90" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                        viewBox="0 0 18 20">
                        <path
                            d="m17.914 18.594-8-18a1 1 0 0 0-1.828 0l-8 18a1 1 0 0 0 1.157 1.376L8 18.281V9a1 1 0 0 1 2 0v9.281l6.758 1.689a1 1 0 0 0 1.156-1.376Z" />
                    </svg>
                </button>
            </div>
        </form>
    </div>
</template>


<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'
import axios from 'axios'
import { usePage } from '@inertiajs/vue3'
import { formatDistanceToNow } from 'date-fns'

const props = defineProps({
    order: {
        type: Object,
        required: true
    }
});

const page = usePage()
const newMessage = ref('')
const attachment = ref(null)
const messages = ref([])
const chatContainer = ref(null)
const orderItemId = ref(props.order.id)
const senderId = ref(page.props.auth.user.id)
const receiverId = ref(props.order.order.user_id)
const uploadProgress = ref(0)
const fileInput = ref(null)
const hasNewMessages = ref(false)
const isSending = ref(false)
const observer = ref(null)

const messageInput = ref(null)



const fetchMessages = async () => {
    const { data } = await axios.get(route('messages.index', orderItemId.value))
    messages.value = data
    hasNewMessages.value = false
}

const addNewline = (event) => {
    const textarea = messageInput.value;
    if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        newMessage.value = newMessage.value.substring(0, start) + '\n' + newMessage.value.substring(end);
        nextTick(() => {
            textarea.selectionStart = textarea.selectionEnd = start + 1;
        });
    } else {
        newMessage.value += '\n';  // fallback
    }
};


const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file type
    const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/pdf',
        'text/plain'
    ];

    if (!allowedTypes.includes(file.type)) {
        alert('Please upload a valid file type (jpg, png, doc, docx, pdf, txt)');
        event.target.value = ''; // Clear the input
        return;
    }

    // Check file size (2MB limit)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
        alert('File size should not exceed 2MB');
        event.target.value = ''; // Clear the input
        return;
    }

    attachment.value = file;
    uploadProgress.value = 0;
}

const scrollToBottom = () => {
    if (chatContainer.value) {
        chatContainer.value.scrollTop = chatContainer.value.scrollHeight
        hasNewMessages.value = false
    }
}

const sendMessage = async () => {
    if (isSending.value || newMessage.value.trim() === '') return;

    isSending.value = true;
    const formData = new FormData()
    formData.append('text', newMessage.value)
    formData.append('sender_id', senderId.value)
    formData.append('order_item_id', orderItemId.value)
    formData.append('receiver_id', receiverId.value)
    if (attachment.value) {
        formData.append('attachment', attachment.value)

        // Show upload progress
        const config = {
            onUploadProgress: function (progressEvent) {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                uploadProgress.value = percentCompleted
            }
        }

        try {
            const { data } = await axios.post(route('messages.store'), formData, config)
            // Ensure the sender_id is set correctly in the response data
            data.sender_id = senderId.value
            data.receiver_id = receiverId.value
            messages.value.push(data)

            // Reset after successful upload
            newMessage.value = ''
            attachment.value = null
            uploadProgress.value = 0

            // Scroll to bottom after new message
            await nextTick()
            messageInput.value?.focus();
            scrollToBottom()
        } catch (error) {
            console.error('Error sending message:', error)
            alert('Failed to send message. Please try again.')
        } finally {
            isSending.value = false;
        }
    } else {
        try {
            const { data } = await axios.post(route('messages.store'), formData)
            // Ensure the sender_id is set correctly in the response data
            data.sender_id = senderId.value
            data.receiver_id = receiverId.value
            messages.value.push(data)
            newMessage.value = ''

            // Scroll to bottom after new message
            await nextTick()
            messageInput.value?.focus();
            scrollToBottom()
        } catch (error) {
            console.error('Error sending message:', error)
            alert('Failed to send message. Please try again.')
        } finally {
            isSending.value = false;
        }
    }
}

const removeAttachment = () => {
    attachment.value = null;
    uploadProgress.value = 0;
    // Reset the file input value to allow reselecting the same file
    if (fileInput.value) {
        fileInput.value.value = '';
    }
};

const formatTime = (date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
}

const setupIntersectionObserver = () => {
    observer.value = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const messageId = entry.target.getAttribute('data-message-id');
                markMessageAsSeen(messageId);
            }
        });
    }, { threshold: 0.5 });

    // Observe all message elements
    nextTick(() => {
        const messageElements = chatContainer.value.querySelectorAll('[data-message-id]');
        messageElements.forEach(el => {
            observer.value.observe(el);
        });
    });
};

const markMessageAsSeen = async (messageId) => {
    const message = messages.value.find(m => m.id === messageId);
    if (message && message.sender_id !== senderId.value && !message.is_read) {
        try {
            await axios.post(route('messages.mark-as-read', { orderItemId: orderItemId.value }));
        } catch (error) {
            console.error('Error marking message as read:', error);
        }
    }
};

const markMessagesAsSeen = async () => {
    const unreadMessages = messages.value.filter(message =>
        message.sender_id !== senderId.value && !message.is_read
    );

    if (unreadMessages.length > 0) {
        try {
            await axios.post(route('messages.mark-as-read', { orderItemId: orderItemId.value }));
        } catch (error) {
            console.error('Error marking messages as read:', error);
        }
    }
};

const handleScroll = () => {
    if (!chatContainer.value) return;

    const isNearBottom = chatContainer.value.scrollHeight - chatContainer.value.scrollTop <= chatContainer.value.clientHeight + 100;
    if (isNearBottom) {
        markMessagesAsSeen();
    }
};

watch(messages, async () => {
    await nextTick()
    scrollToBottom()
})

onMounted(() => {
    fetchMessages().then(() => {
        scrollToBottom()
        setupIntersectionObserver()
    })

    // Add scroll event listener
    if (chatContainer.value) {
        chatContainer.value.addEventListener('scroll', handleScroll);
    }

    // Listen for new messages
    window.Echo.private(`order-item.${orderItemId.value}`)
        .listen('NewMessageSent', (e) => {
            if (e.message.sender_id !== senderId.value) {
                messages.value.push(e.message)
                hasNewMessages.value = true
            }
        })
})

// Clean up observer and scroll listener
onUnmounted(() => {
    if (observer.value) {
        observer.value.disconnect();
    }
    if (chatContainer.value) {
        chatContainer.value.removeEventListener('scroll', handleScroll);
    }
})
</script>

<style scoped></style>
