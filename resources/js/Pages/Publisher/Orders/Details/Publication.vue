<template>
    <div class="space-y-6">
        <div class="border-b pb-4">
            <h3 class="text-lg font-medium text-gray-900">Publication Details</h3>
            <p class="mt-1 text-sm text-gray-500">Please provide the published URL in this section.</p>
        </div>
        <div v-if="props.item.state_name === orderItemStates.PublicationRevisionRequestedByAdvertiser"
            class="p-4 border-b border-gray-100">
            <div class="rounded-md bg-yellow-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Revision Requested</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p class="font-medium">Reason: {{ props.revisions || 'No reason provided' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <form @submit.prevent="submit">
            <div class="space-y-4 ">
                <div>
                    <label for="publishedUrl" class="block text-sm font-medium text-gray-700">Published URL</label>
                    <div class="mt-1">
                        <input type="url" id="publishedUrl" v-model="publishedUrl"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="https://www.example.com" />
                    </div>
                </div>
                <button type="submit"
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Update Published URL
                </button>
            </div>
        </form>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { router } from '@inertiajs/vue3'
import { usePage } from '@inertiajs/vue3';


const props = defineProps({
    item: {
        type: Object,
        required: true,
        default: () => ({})
    },
    revisions: {
        type: String,
        required: true,
        default: () => ({})
    }
})

const publishedUrl = ref(props.item.publication?.publication_url || '');
const notify = inject('$notify');
const page = usePage();
const orderItemStates = page.props.orderItemStates;

const submit = () => {
    if (!publishedUrl.value.trim()) {
        notify('Please provide a published URL', { type: 'error' })
        return
    }

    try {
        new URL(publishedUrl.value)
    } catch (e) {
        notify('Please provide a valid URL', { type: 'error' })
        return
    }

    var routeName = "publisher.orders.item.publish";

    router.post(route(routeName, props.item.id), {
        published_url: publishedUrl.value
    }, {
        preserveScroll: true,
        onSuccess: () => {
            notify('Published URL submitted successfully', { type: 'success' })
            //publishedUrl.value = ''
            router.reload({ only: ['order'] })
        },
        onError: (errors) => {
            notify('Failed to submit published URL', { type: 'error' })
            console.error(errors)
        }
    });

}



</script>
