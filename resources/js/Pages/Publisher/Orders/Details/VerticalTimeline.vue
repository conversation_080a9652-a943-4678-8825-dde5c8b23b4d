<template>
    <div class="flex flex-col items-start  relative">
        <!-- Vertical Line -->
        <div class="pb-0 w-full -top-0">
            <h3 class="text-base/7 font-semibold text-gray-900  w-full">Status</h3>
        </div>
        <div class="flex flex-col items-start space-y-6 relative">
            <!-- Horizontal line with visible height -->
            <div class=" w-full h-[0px] -top-0 absolute border-dashed border-b-3 border-b-gray-400 "></div>

            <!-- Vertical line -->
            <div class="absolute w-[1px] left-[16px]  border-s border-s-3 border-s-gray-400 border-dashed h-full transform -translate-x-1/2">
            </div>

            <!-- Timeline Items -->
            <div v-for="(item, index) in timeline" :key="item.id" class="flex items-center space-x-4">
                <!-- Timeline Circle & Icon -->
                <div class="relative flex items-center justify-center">
                    <div class="w-8 h-8 flex items-center justify-center rounded-full border-2" :class="item.completed ? 'border-green-500 bg-green-100' : 'border-gray-400 bg-white'">
                        <Check v-if="item.isCurrent" class="text-green-500 w-4 h-4" />
                        <Check v-else-if="item.completed" class="text-green-500 w-4 h-4" />
                        <Hourglass v-else class="text-gray-400 w-4 h-4" />

                    </div>
                </div>

                <!-- Timeline Label -->
                <span class="text-base/7  font-semibold text-gray-700">{{ item.label }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { stateMilestones } from '@/helpers/utils';
import { Check, Hourglass } from 'lucide-vue-next';
const props = defineProps({
    currentState: String,
});

const currentState = computed(() => props.currentState);


const timeline = computed(() => {
    const currentStep = stateMilestones[currentState.value] || 1;

    return [
        { id: 1, label: 'Order Placed', completed: currentStep >= 1 },
        { id: 2, label: 'Requirements', completed: currentStep >= 2 },
        { id: 3, label: 'Approve Requirements', completed: currentStep >= 3 },
        { id: 4, label: 'Content', completed: currentStep >= 4 },
        { id: 5, label: 'Approval', completed: currentStep >= 5 },
        { id: 6, label: 'In Progress', completed: currentStep >= 6 },
        { id: 7, label: 'Published', completed: currentStep >= 7 },
        { id: 8, label: 'Order Cancelled', completed: currentStep >= 8 }
    ].map((step, index, arr) => {
        // Mark the "current" step as the last completed one
        const isLastCompleted =
            step.completed &&
            (index === arr.length - 1 || !arr[index + 1].completed);

        return {
            ...step,
            isCurrent: isLastCompleted,
        };
    });
});


</script>

<style scoped>
/* Aligns the timeline items vertically */
.flex-col-reverse {
    display: flex;
    flex-direction: column-reverse;
}
</style>
