<template>
    <div class="space-y-6">
        <div class="border-b pb-4">
            <h3 class="text-lg font-medium text-gray-900">Requirements Details</h3>
            <p class="mt-1 text-sm text-gray-500">Review the requirements provided by the customer.</p>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm text-gray-400">Article Topic</label>
                <div class="mt-1 p-2 bg-gray-50 rounded-md">
                    <p class="text-sm text-gray-900">{{ requirements?.article_topic || 'Not provided' }}</p>
                </div>
            </div>

            <div>
                <label class="block text-sm text-gray-400">Anchor Text</label>
                <div class="mt-1 p-2 bg-gray-50 rounded-md">
                    <p class="text-sm text-gray-900">{{ requirements?.anchor_text || 'Not provided' }}</p>
                </div>
            </div>

            <div>
                <label class="block text-sm text-gray-400">Advertiser URL</label>
                <div class="mt-1 p-2 bg-gray-50 rounded-md">
                    <a :href="requirements?.advertiser_url" target="_blank"
                        class="text-sm text-blue-600 hover:text-blue-800">
                        {{ requirements?.advertiser_url || 'Not provided' }}
                    </a>
                </div>
            </div>

            <div v-if="requirements?.requirement_comments">
                <label class="block text-sm text-gray-400">Additional Comments</label>
                <div class="mt-1 p-2 bg-gray-50 rounded-md">
                    <p class="text-sm text-gray-900">{{ requirements.requirement_comments }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    requirements: {
        type: Object,
        required: true
    }
})
</script>