<template>
    <div class="space-y-6">
        <div class="border-b pb-4">
            <h3 class="text-lg font-medium text-gray-900">Content Details</h3>
            <p class="mt-1 text-sm text-gray-500">Review the content provided by the customer.</p>
        </div>
        <div v-if="content" class="space-y-6">
            <!-- Source and Writer in 3-column layout -->
            <div class="flex flex-col gap-4">



                <div>
                    <h4 class="text-sm text-gray-400">Title</h4>
                    <div class="mt-1 p-2 bg-gray-50 rounded-md">
                        <p class="mt-1 text-sm text-gray-700">{{ content?.title || 'No title provided' }}</p>
                    </div>
                </div>
            </div>

            <div class="flex flex-col gap-4">
                <div v-if="content?.content_url">
                    <h4 class="text-sm text-gray-400">Content URL</h4>
                    <div class="mt-1 p-2 bg-gray-50 rounded-md">
                        <a :href="content.content_url" target="_blank" class="mt-1 text-sm text-indigo-600 hover:text-indigo-500">
                            {{ content.content_url }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Content Body in 3-column layout -->
            <div class="flex flex-col gap-4 ">
                <div class="col-span-3 ">
                    <h4 class="text-sm text-gray-400">Content</h4>
                    <div class="mt-1 p-2 bg-gray-50 rounded-md">
                        <div class="mt-1 prose prose-sm max-w-none">
                            <div v-html="content?.content_body || 'No content provided'"></div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Content URL and Files in 3-column layout -->
            <div class="flex flex-col gap-4">

                <div v-if="media?.length" class="col-span-3 mt-6">
                    <h4 class="text-sm text-gray-400">Attached Files</h4>
                    <div class="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                        <div v-for="file in media" :key="file.id" class="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
                            <div class="min-w-0 flex-1">
                                <p class="text-sm text-gray-400 truncate">{{ file.name }}</p>
                                <p class="text-sm text-gray-500 truncate">{{ formatFileSize(file.size) }}</p>
                            </div>
                            <div class="flex-shrink-0 flex items-center space-x-2">
                                <a :href="file.url" target="_blank"
                                    class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    View
                                </a>
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comments and Timestamps in 3-column layout -->
            <div class="flex flex-col gap-4">
                <div v-if="content?.comments" class="mt-1 p-2 bg-gray-50 rounded-md">
                    <h4 class="text-sm text-gray-400">Comments</h4>
                    <p class="mt-1 text-sm text-gray-700">{{ content.comments }}</p>
                </div>
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <h4 class="text-sm text-gray-400">Content Source</h4>
                        <div class="mt-1 p-2 bg-gray-50 rounded-md">
                            <p class="mt-1 text-sm text-gray-700">{{ content?.content_source === 'customer' ? 'Customer' :
                                'Team' }}</p>
                        </div>

                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm text-gray-400">Writer</h4>

                        <div class="mt-1 p-2 bg-gray-50 rounded-md">
                            <p class="mt-1 text-sm text-gray-700">{{ content?.writer?.name || 'Not assigned' }}</p>
                        </div>
                    </div>

                </div>
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-400">Created</h4>
                        <div class="mt-1 p-2 bg-gray-50 rounded-md">
                            <p class="mt-1 text-sm text-gray-700">{{ content?.created_at_formatted || 'N/A' }}</p>
                        </div>
                    </div>
                    <div class="flex-1">

                        <h4 class="text-sm font-medium text-gray-400">Last Updated</h4>
                        <div class="mt-1 p-2 bg-gray-50 rounded-md">

                            <p class="mt-1 text-sm text-gray-700">{{ content?.updated_at_formatted || 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else class="text-center py-4">
            <p class="text-gray-500">No content available</p>
        </div>
    </div>
</template>

<script setup>
defineProps({
    media: {
        type: Array,
        required: true,
        default: () => ({})
    },
    content: {
        type: Object,
        required: true,
        default: () => ({})
    }
})

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
