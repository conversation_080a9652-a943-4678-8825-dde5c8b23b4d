<template>
    <div class="grid grid-cols-5 gap-4 mt-4">
        <!-- Search by Keyword -->
        <div class="col-span-2">
            <label class="block text-sm text-gray-500 mb-1">Search by Keyword</label>
            <div class="relative">
                <input v-model="form.searchTerm" type="text" placeholder="Search by domain..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm px-3 py-2" />
                <button v-if="form.searchTerm" @click="clearSearch" class="absolute right-2 top-2 text-gray-400 hover:text-gray-600">
                    <XCircle class="w-5 h-5" />
                </button>
            </div>
        </div>

        <!-- Status Filter -->
        <div>
            <label class="block text-sm text-gray-500 mb-1">Website Status</label>
            <select v-model="form.status" @change="applyFilters" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm px-3 py-2">
                <option value="">All</option>
                <option value="1">Active</option>
                <option value="0">Inactive</option>
            </select>
        </div>

        <!-- Per Page -->
        <div>
            <label class="block text-sm text-gray-500 mb-1">Display Per Page</label>
            <select v-model="form.perPage" @change="applyFilters" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm px-3 py-2">
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { useForm, router } from "@inertiajs/vue3";
import { XCircle } from "lucide-vue-next";
import debounce from "lodash/debounce";

// Props
const props = defineProps({
    searchTerm: String,
    status: String,
    perPage: [String, Number],
});

// Form state
const form = useForm({
    searchTerm: props.searchTerm ?? "",
    status: props.status ?? "",
    perPage: props.perPage ?? "10",
});

// Clear search
const clearSearch = () => {
    form.searchTerm = "";
    applyFilters();
};

// Apply filters with debounce
const applyFilters = debounce(() => {
    const query = {};

    if (form.searchTerm.trim()) query.searchTerm = form.searchTerm;
    if (form.status !== "") query.status = form.status;
    query.perPage = form.perPage;

    router.get(route("publisher.websites.index"), query, {
        preserveState: true,
        preserveScroll: true,
    });
}, 500);

// Trigger search on input
watch(() => form.searchTerm, () => {
    applyFilters();
});
</script>