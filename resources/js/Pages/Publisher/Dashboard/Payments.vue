<template>
    <div>
        <h3 class="text-base font-semibold text-gray-900">PAYMENTS</h3>
        <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3">
            <div v-for="item in stats" :key="item.name" 
            class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow-md border sm:p-6">
                <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ item.stat }}</dd>
                <dt class="truncate text-sm font-medium text-gray-500">{{ item.name }}</dt>
            </div>
        </dl>
    </div>
</template>

<script setup>

const stats = [
    { name: 'Balance Available', stat: '$100' },
    { name: 'Balance Pending', stat: '$100' },
    { name: 'Total Eearning', stat: '$1000' },
]
</script>


<style lang="scss" scoped></style>