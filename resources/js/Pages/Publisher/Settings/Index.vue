<template>
    <div class="space-y-6">
        <!-- Header -->
        <div class="lg:flex lg:items-center lg:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="mt-2 text-2xl font-bold text-gray-900 sm:text-3xl sm:tracking-tight">
                    Settings
                </h2>
            </div>
        </div>

        <!-- Cards Grid -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">

            <!-- Card -->
            <Link :href="route('publisher.payment-settings.index')" class="relative">
            <div class="bg-white p-6 rounded-2xl shadow hover:shadow-md transition absolute hover:scale-[102%] hover:bg-gray-50">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Payment Methods</h3>
                <p class="text-gray-600 text-sm">
                    Manage your payment methods to receive payments from your customers.
                </p>
            </div>
            </Link>

            


            <!-- Card -->
            <div class="hidden bg-white p-6 rounded-2xl shadow hover:shadow-md transition">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Profile</h3>
                <p class="text-gray-600 text-sm">
                    Manage your account information and personal details.
                </p>
            </div>


            <!-- Card -->
            <div class="hidden bg-white p-6 rounded-2xl shadow hover:shadow-md transition">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Notifications</h3>
                <p class="text-gray-600 text-sm">
                    Control how and when you receive updates from us.
                </p>
            </div>

            <!-- Card -->
            <div class="hidden bg-white p-6 rounded-2xl shadow hover:shadow-md transition">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Support</h3>
                <p class="text-gray-600 text-sm">
                    Get help and find answers to common questions.
                </p>
            </div>
        </div>
    </div>
</template>

<script setup>
// No script logic needed for static layout
</script>