<template>
    <div class="grid grid-cols-12 gap-x-4 items-end">
        <!-- Search Input -->
        <div class="col-span-6">
            <label class="text-xs text-gray-400">Search</label>
            <input v-model="form.search" type="text" placeholder="Search by website, topic..." class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600" />
        </div>

        <!-- Assignment Type Dropdown -->
        <div class="col-span-3">
            <label class="text-xs text-gray-400">Assignment Type</label>
            <select v-model="form.assignmentType" class="mt-2 block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-gray-300 focus:outline-indigo-600">
                <option value="">All</option>
                <option value="assigned">Assigned</option>
                <option value="completed">Completed</option>
            </select>
        </div>
    </div>
</template>

<script setup>
import { useForm, router } from '@inertiajs/vue3'
import { watch } from 'vue'
import debounce from 'lodash/debounce'

const props = defineProps({
    search: { type: String, default: '' },
    assignmentType: { type: String, default: 'assigned' }
})

const emit = defineEmits(['update'])

const form = useForm({
    search: props.search,
    assignmentType: props.assignmentType
})

// Watch changes and emit to parent after debounce
const applyFilters = debounce(() => {
    emit('update', { ...form })
}, 300)

watch(() => form.search, applyFilters)
watch(() => form.assignmentType, applyFilters)
</script>
