<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <div class="flex flex-row gap-2 items-center">
                    <h2 class="title-1">My Assignment Stats</h2>
                </div>
                <p class="subtitle-1">Assignment stats.</p>

            </div>
        </div>

        <div class="my-4">
            <DateRangeFilter :preset-range="filters.preset_range" :start-date="filters.start_date" :end-date="filters.end_date" @update:filters="onFiltersChange" />
        </div>


        <div>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-6">
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.content_assigned }}</div>
                    <div class="stats-block-label">Assigned</div>
                </div>
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.advertiser_review }}</div>
                    <div class="stats-block-label">Advertiser Review</div>
                </div>
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.revision_requested_by_advertiser }}</div>
                    <div class="stats-block-label">Revision Requested</div>
                </div>
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.awaiting_publisher_approval }}</div>
                    <div class="stats-block-label">Awaiting Publisher Approval</div>
                </div>
                <div class="stats-block badge-completed">
                    <div class="stats-block-number">{{ stats.completed }}</div>
                    <div class="stats-block-label">Completed</div>
                </div>
            </div>

        </div>
    </div>
</template>

<script setup>

import DateRangeFilter from '@/Components/DateRangeFilter.vue'

defineProps({
    filters: Object,
    stats: Object,
});

const onFiltersChange = (filters) => {
    console.log(filters);
}


</script>
