<template>
    <div>
        <div class="lg:flex lg:items-center lg:justify-between">
            <div class="min-w-0 flex-1">
                <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    {{ capitalizedRole }} Dashboard
                </h2>
                <p class="subtitle-1">Displaying stats for the selected date range.</p>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <dt class="truncate text-sm font-medium text-gray-500">Total Orders</dt>
                <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ stats.totalOrders }}</dd>
            </div>

            <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <dt class="truncate text-sm font-medium text-gray-500">Active Orders</dt>
                <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ stats.activeOrders }}</dd>
                <div class="mt-2">
                    <span class="text-sm text-gray-500">Currently in progress</span>
                </div>
            </div>

            <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <dt class="truncate text-sm font-medium text-gray-500">Total Spent</dt>
                <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">${{ stats.totalSpent }}</dd>
            </div>

            <div class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
                <dt class="truncate text-sm font-medium text-gray-500">Pending Requirements</dt>
                <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ stats.pendingRequirements }}
                </dd>
            </div>
        </div>

        <!-- Order Status Distribution -->
        <div class="mt-8 grid grid-cols-1 gap-5 lg:grid-cols-1">


            <!-- Recent Orders -->
            <div class="overflow-hidden rounded-lg bg-white shadow">
                <div class="p-6">
                    <h3 class="text-base font-semibold leading-6 text-gray-900">Recent Orders</h3>
                    <div class="mt-6 flow-root">
                        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead>
                                        <tr>
                                            <th scope="col"
                                                class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                                Order ID</th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Items
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Amount
                                            </th>
                                            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                                <span class="sr-only">View</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr v-for="order in recentOrders" :key="order.id">
                                            <td
                                                class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                #{{ order.id }}</td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm">
                                                <span :class="getStatusClass(order.status)">{{ order.status }}</span>
                                            </td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                                order.items_count }}</td>
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">${{
                                                order.price_paid }}</td>
                                            <td
                                                class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                                <a :href="route('advertiser.order-details', order.id)"
                                                    class="text-indigo-600 hover:text-indigo-900">View</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Needing Attention -->
        <div class="mt-8 overflow-hidden rounded-lg bg-white shadow">
            <div class="p-6">
                <h3 class="text-base font-semibold leading-6 text-gray-900">Items Needing Attention</h3>
                <div class="mt-6 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead>
                                    <tr>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                            #</th>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                            Order ID</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Due Date
                                        </th>
                                        <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                            <span class="sr-only">Action</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr v-for="item in attentionNeeded" :key="item.id">
                                        <td
                                            class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                            {{ item.id }}</td>
                                        <td
                                            class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                            #{{ item.order_id }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm">
                                            <span :class="getStatusClass(item.state)">{{
                                                item.state }}</span>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            item.estimated_publication_date }}</td>
                                        <td
                                            class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                            <a :href="route('advertiser.order-item-details', item.id)"
                                                class="text-indigo-600 hover:text-indigo-900">View</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { computed } from 'vue';
import { usePage } from "@inertiajs/vue3";
import { getStatusClass } from '@/helpers/utils.js';


//==========================================================
// Props & Data
//==========================================================
const props = defineProps({
    stats: {
        type: Object,
        required: true,
        default: () => ({
            totalOrders: 0,
            activeOrders: 0,
            totalSpent: 0,
            pendingRequirements: 0
        })
    },
    recentOrders: {
        type: Array,
        required: true,
        default: () => []
    },
    attentionNeeded: {
        type: Array,
        required: true,
        default: () => []
    }
});

// ==========================================================
// Computed Properties
// ==========================================================
const capitalizedRole = computed(() => {
    const role = usePage().props.auth.user.role;
    return role.charAt(0).toUpperCase() + role.slice(1);
});
</script>

<style lang="scss" scoped>
.subtitle-1 {
    @apply text-sm text-gray-500;
}
</style>