<template>
    <div>
        <div class="lg:flex lg:items-center lg:justify-between border-b pb-8 mb-8">
            <div class="min-w-0 flex-1">
                <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    My Wallet
                </h2>
            </div>

            <Link v-if="balance > 0" :href="route('wallet.withdraw')" class="btn-indigo">
            <CircleArrowOutUpRight class="h-5 w-5" />
            <span>Transfer to Bank Account</span>
            </Link>
        </div>
        <div class="border-b pb-8 mb-8">
            <div class="lg:flex lg:items-center lg:justify-between mt-5">
                <div class="min-w-0 flex flex-row flex-wrap">
                    <Wallet class="w-32 h-32" />
                    <div class="flex flex-col items-start gap-1 ms-2 mt-2">
                        <span class="text-2xl font-bold">Wallet Details</span>
                        <span class="text-lg font-bold text-green-700">${{ balance }} (USD)</span>
                        <span class="font-bold text-gray-400">Your Wallet Balance</span>
                    </div>
                </div>
                <!-- <div>
                    <div
                        class="p-4 flex flex-col gap-4 justify-center w-[350px] text-sm border rounded-md border-gray-300">
                        <div>
                            Enter amount to be added to your wallet (USD)
                        </div>
                        <div>
                            <input v-model.number="form.amount" type="number" placeholder="Type amount..."
                                class="block w-full rounded-md bg-white text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" />
                            <p v-if="form.errors.amount" class="text-red-500 text-sm">{{ form.errors.amount }}</p>
                        </div>
                        <div>
                            <button @click="submitForm"
                                class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md flex items-center justify-center gap-2">
                                <CircleDollarSign class="h-5 w-5" />
                                <span>ADD MONEY TO YOUR WALLET</span>
                            </button>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>

        <div>
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="mt-8 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <table class="min-w-full divide-y divide-gray-300"  v-if="transactions.data && transactions.data.length">
                                <thead>
                                    <tr>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                            TID</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Reference
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">User</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Order ID
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Debit</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Credit
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Date</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status
                                        </th>
                                        <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-3">
                                            <span class="sr-only">View</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white">
                                    <tr v-for="transaction in transactions" :key="transaction.id"
                                        class="even:bg-gray-50">
                                        <td
                                            class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                                            {{ transaction.id }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.reference }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.user.name }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                            <a v-if="transaction.order_id !== 'N/A'"
                                                :href="route('advertiser.order-details', { id: transaction.order_id })"
                                                class="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
                                                {{ transaction.order_id }}
                                            </a>
                                            <span v-else>N/A</span>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm"
                                            :class="transaction.debit ? 'text-red-600 font-bold' : 'text-gray-500'">
                                            {{ transaction.debit }}
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm"
                                            :class="transaction.credit ? 'text-green-700 font-bold' : 'text-gray-500'">
                                            {{ transaction.credit }}
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.date }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.status }}</td>
                                        <td
                                            class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                                            <Link
                                                :href="route('advertiser.transactions.details', { id: transaction.id })"
                                                class="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
                                            <Eye class="h-5 w-5" />
                                            <span>View</span>
                                            </Link>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <div v-else>
                                <NoDataMessage message1="No transaction records found." />

                            </div>
                            <PaginationLinks v-if="transactions.data && transactions.data.length"
                                :links="transactions.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { CircleArrowOutUpRight, CircleDollarSign, Eye, Wallet } from 'lucide-vue-next';
import { useForm } from "@inertiajs/vue3";
import NoDataMessage from '@/Components/NoDataMessage.vue';

const props = defineProps({
    balance: {
        type: Number,
        required: true
    },
    transactions: {
        type: Array,
        required: true
    }
});

// Form setup
const form = useForm({
    amount: 0,
});

// Function to handle form submission
const submitForm = () => {
    form.post(route("admin.wallet.add-money"), {
        preserveScroll: true,
        onSuccess: () => {
            alert("Money added successfully!");
        },
        onError: (errors) => {
            console.error("Error:", errors);
        },
    });
};
</script>

<style lang="scss" scoped></style>