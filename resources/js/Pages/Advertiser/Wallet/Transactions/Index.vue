<template>
    <div>
        <div class="lg:flex lg:items-center lg:justify-between">
            <div class="min-w-0 flex-1">

                <h2
                    class="flex items-center justify-start gap-1 mt-2 text-2xl font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    <Link :href="getBackRoute()" class="link-indigo-icon">
                    <CircleChevronLeft class="w-6 h-6" />
                    </Link>
                    <span>
                        Transaction Details
                    </span>
                </h2>
            </div>
        </div>
        <div class="mt-5 inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <table class="min-w-full  divide-gray-300">
                <thead>

                </thead>
                <tbody class=" divide-gray-200">
                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Amount:</td>
                        <td class="py-4 pl-4 pr-3 ">${{ (props.transaction.amount / 100).toFixed(2) }}</td>
                    </tr>

                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Action:</td>
                        <td class="py-4 pl-4 pr-3 ">{{ props.transaction.type === 'deposit' ? 'CREDIT' : 'DEBIT' }}
                        </td>
                    </tr>
                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Type:</td>
                        <td class="py-4 pl-4 pr-3 ">{{ props.transaction.type }}</td>
                    </tr>
                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Reference:</td>
                        <td class="py-4 pl-4 pr-3 ">{{ props.transaction.meta?.reference || 'N/A' }}</td>
                    </tr>
                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Transaction At:</td>
                        <td class="py-4 pl-4 pr-3 ">{{ new Date(props.transaction.created_at).toLocaleString() }}
                        </td>
                    </tr>
                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Transaction Note:</td>
                        <td class="py-4 pl-4 pr-3 ">{{ props.transaction.meta?.note || 'N/A' }}</td>
                    </tr>
                    <tr class="border-t">
                        <td class="w-[200px] font-bold py-4 pl-4 pr-3 bg-gray-50">Status:</td>
                        <td class="py-4 pl-4 pr-3 ">{{ props.transaction.confirmed ? 'Approved' : 'Pending' }}</td>
                    </tr>

                </tbody>
            </table>
        </div>

    </div>
</template>

<script setup>
import { CircleChevronLeft } from 'lucide-vue-next';
import Balance from '../Partials/Balance.vue';
import { inject } from 'vue';

const page = inject("page");

const role = page.props.auth?.user?.role;

const props = defineProps({
    transaction: {
        type: Object,
        required: true,
    },
});

function getBackRoute() {
    if (role === "publisher") {
        return route("publisher.wallet");
    } else if (role === "advertiser") {
        return route("advertiser.wallet.index");
    }
}

</script>

<style lang="scss" scoped></style>