<template>
    <div class="px-4 sm:px-6 lg:px-8">

        <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead>
                            <tr>
                                <th scope="col"
                                    class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">TID
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                    Reference</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Debit
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Credit
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Date
                                </th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status
                                </th>
                                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-3">
                                    <span class="sr-only">Edit</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white">

                            <tr v-for="person in people" :key="person.email" class="even:bg-gray-50">
                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                                    {{ person.tid }}</td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ person.reference }}
                                </td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ person.debit }}</td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ person.credit }}</td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ person.date }}</td>
                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ person.status }}</td>
                                <td
                                    class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                                    <Link :href="getWalletDetailsRoute()"
                                        class="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
                                    <Eye class="h-5 w-5" />
                                    <span>
                                        View
                                    </span>
                                    </Link>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>

import { inject } from "vue";

import { Eye } from 'lucide-vue-next';

const page = inject("page");
const role = page.props.auth?.user?.role;

const getWalletDetailsRoute = () => {
    if (role === page.props.roleEnums.Advertiser) {
        return route('advertiser.wallet.details');
    } else if (role === page.props.roleEnums.Publisher) {
        return route('publisher.wallet.details');
    }
    return '/'; // Fallback in case of an unknown role
};


const people = [
    { tid: '1231234', reference: 'Transfer to Bank Account', debit: '', credit: '$200', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Added by Advertiser', debit: '$200', credit: '', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Transfer to Bank Account', debit: '', credit: '$200', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Added by Advertiser', debit: '$200', credit: '', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Added by Advertiser', debit: '$200', credit: '', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Transfer to Bank Account', debit: '', credit: '$200', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Added by Advertiser', debit: '$200', credit: '', status: 'Approved', date: '2023-01-01' },
    { tid: '1231234', reference: 'Added by Advertiser', debit: '$200', credit: '', status: 'Approved', date: '2023-01-01' },
]
</script>