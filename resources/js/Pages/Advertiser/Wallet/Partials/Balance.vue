<template>
    <div class="lg:flex lg:items-center lg:justify-between mt-5">
        <div class="min-w-0 flex flex-row flex-wrap">
            <Wallet class="w-32 h-32" />
            <div class="flex flex-col items-start gap-1 ms-2 mt-2">
                <span class="text-2xl font-bold">Wallet Details</span>
                <span class="text-lg font-bold text-green-700">$1200 (USD)</span>
                <span class="font-bold text-gray-400">Your Wallet Balance</span>
            </div>
        </div>
        <div>
            <div class="p-4 flex flex-col gap-4 justify-center w-[350px] text-sm border rounded-md border-gray-300">
                <div>
                    Enter amount to be added to your wallet (USD)
                </div>
                <div>
                    <input v-model.number="form.amount" type="number" placeholder="Type amount..." class="block w-full rounded-md bg-white text-base text-gray-900 outline-gray-300 focus:outline-indigo-600 sm:text-sm" />
                    <p v-if="form.errors.amount" class="text-red-500 text-sm">{{ form.errors.amount }}</p>
                </div>
                <div>
                    <button @click="submitForm" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md flex items-center justify-center gap-2">
                        <CircleDollarSign class="h-5 w-5" />
                        <span>ADD MONEY TO YOUR WALLET</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { CircleDollarSign, Wallet } from 'lucide-vue-next';
import { useForm } from "@inertiajs/vue3";

// Form setup
const form = useForm({
    amount: 0,
});

// Function to handle form submission
const submitForm = () => {
    form.post(route("admin.wallet.add-money"), {
        preserveScroll: true,
        onSuccess: () => {
            alert("Money added successfully!");
        },
        onError: (errors) => {
            console.error("Error:", errors);
        },
    });
};
</script>
