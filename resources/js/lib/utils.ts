import type { Updater } from "@tanstack/vue-table";
import type { Ref } from "vue";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

// ====================================================
// Merge Tailwind class names safely using clsx + twMerge
// ====================================================
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// ====================================================
// Update a Vue Ref using either a direct value or a function updater
// Supports TanStack Table's update pattern
// ====================================================
export function valueUpdater<T extends Updater<any>>(
  updaterOrValue: T,
  ref: Ref
) {
  ref.value =
    typeof updaterOrValue === "function"
      ? updaterOrValue(ref.value)
      : updaterOrValue;
}

export const returnWebsiteUrl = (website) => {
  let url = website.url || website.website_url || website.website_domain;

  // Ensure URL starts with http or https
  if (!/^https?:\/\//i.test(url)) {
    url = "https://" + url;
  }

  return url;
};
