// ==========================================
// Map order item state names to CSS classes
// ==========================================
export const getStatusClass = (stateName) => {
    const map = {
        // Requirement Phase
        'requirements-pending': 'status-pending',
        'requirement-awaiting-publisher-approval': 'status-pending',
        'requirement-revision-requested': 'status-pending',

        // Content Phase
        'content-pending': 'status-inprogress',
        'content-awaiting-publisher-approval': 'status-inprogress',
        'content-assigned-to-writer': 'status-inprogress',
        'content-advertiser-review': 'status-inprogress',
        'content-revision-requested-by-advertiser': 'status-inprogress',

        // Publication Phase
        'publication-revision-requested-by-advertiser': 'status-inprogress',
        'publication-in-process': 'status-inprogress',
        'publication-delivered': 'status-delivered',

        // Completion
        'order-item-completed': 'status-completed',

        // Cancelled / Refunded
        'order-item-cancelled': 'status-cancelled',
        'order-item-refund-requested': 'status-cancelled',
        'refunded-to-wallet': 'status-refunded',
    };

    return map[stateName] || 'status-pending';
};

// ==========================================
// Timeline Milestone Mapping for Order Items
// Used to render the timeline UI steps on order item details page
// ==========================================
export const stateMilestones = {
    // Requirements Phase
    'requirements-pending': 1,
    'requirement-awaiting-publisher-approval': 2,
    'requirement-revision-requested': 3,

    // Content Phase
    'content-pending': 4,
    'content-assigned-to-writer': 5,
    'content-advertiser-review': 6,
    'content-revision-requested-by-advertiser': 7,
    'content-awaiting-publisher-approval': 8,

    // Publication Phase
    'publication-in-process': 9,
    'publication-delivered': 10,
    'publication-revision-requested-by-advertiser': 11,

    // Completion States
    'order-item-completed': 12,
    'order-item-cancelled': 13,
    'order-item-refund-requested': 14,
    'refunded-to-wallet': 15
};

// ==========================================
// Abbreviate number (e.g. 1200 -> 1.2K)
// Used for traffic, stats, etc.
// ==========================================
export function abbreviateNumber(value, decimals = 1) {
    if (!value) return '0';

    const suffixes = ["", "K", "M", "B", "T"];
    const i = Math.floor(Math.log10(Math.abs(value)) / 3);

    if (i === 0) return value.toString();

    const scaled = value / Math.pow(10, i * 3);
    return scaled.toFixed(decimals) + suffixes[i];
}
