import Dropzone from 'dropzone';
import 'dropzone/dist/dropzone.css';

// Disable auto discover
Dropzone.autoDiscover = false;

// Initialize Dropzone
document.addEventListener('livewire:initialized', () => {
    const dropzoneElement = document.getElementById('myDropzone');

    if (!dropzoneElement) {
        console.warn('Dropzone element not found. Make sure the element with id="myDropzone" exists in the DOM.');
        return;
    }

    try {
        const myDropzone = new Dropzone("#myDropzone", {
            url: window.location.origin + '/upload',
            maxFilesize: 10, // MB
            acceptedFiles: ".png,.jpg,.jpeg,.pdf",
            addRemoveLinks: true,
            dictDefaultMessage: "Drop files here or click to upload",
            dictRemoveFile: "Remove file",
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            init: function () {
                const mediaIds = [];

                this.on("success", function (file, response) {
                    // Add media ID to the array
                    mediaIds.push(response.media_id);
                    // Update the hidden input
                    document.getElementById('media_ids').value = JSON.stringify(mediaIds);
                    // Emit event to Livewire 3
                    window.Livewire.dispatch('media-uploaded', { mediaId: response.media_id });
                });

                this.on("removedfile", function (file) {
                    // Remove media ID from the array
                    const mediaId = file.mediaId;
                    const index = mediaIds.indexOf(mediaId);
                    if (index > -1) {
                        mediaIds.splice(index, 1);
                        // Update the hidden input
                        document.getElementById('media_ids').value = JSON.stringify(mediaIds);
                        // Emit event to Livewire 3
                        window.Livewire.dispatch('media-removed', { mediaId: mediaId });
                    }
                });

                this.on("error", function (file, message) {
                    // Handle upload error
                    console.error("Upload error:", message);
                    if (typeof message === 'object' && message.message) {
                        alert(message.message);
                    } else {
                        alert("An error occurred while uploading the file.");
                    }
                });
            }
        });
    } catch (error) {
        console.error('Error initializing Dropzone:', error);
    }
}); 