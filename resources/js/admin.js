// ====================================================
// Load Laravel + Vite Bootstrap
// ====================================================
import "./bootstrap";

// ====================================================
// Core Vue + Inertia Imports
// ====================================================
import { createApp, h } from "vue";
import { createInertiaApp, Link, usePage } from "@inertiajs/vue3";
import Layout from "./Layouts/Admin/Layout.vue";

// ====================================================
// Ziggy for Laravel route() in Vue
// ====================================================
import { ZiggyVue } from "ziggy-js";
import { route } from "ziggy-js"; // Make route() globally available

// ====================================================
// Vue 3 Toast Notifications
// ====================================================
import { toast } from "vue3-toastify";
import "vue3-toastify/dist/index.css"; // Import toast styles

// ====================================================
// Inertia App Setup
// ====================================================
createInertiaApp({
  // Load pages from ./Pages folder
  resolve: (name) => {
    const pages = import.meta.glob("./Pages/**/*.vue", { eager: true });
    const page = pages[`./Pages/${name}.vue`];

    // Set default layout if none defined in page
    page.default.layout = page.default.layout || Layout;

    return page;
  },

  // App initialization
  setup({ el, App, props, plugin }) {
    const app = createApp({ render: () => h(App, props) });

    // Provide usePage globally via Vue inject
    app.provide("page", usePage());

    // Global notification handler
    const notify = (message, options = {}) => {
      toast(message, { autoClose: 2000, ...options });
    };

    app.provide("$notify", notify);
    app.config.globalProperties.$notify = notify;

    // Mount app with Inertia, Ziggy, and global Link component
    app.use(plugin)
      .use(ZiggyVue)
      .component("Link", Link)
      .mount(el);
  },
});
