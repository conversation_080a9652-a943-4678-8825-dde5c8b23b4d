<template>
  <div>
    <Sidebar v-if="user && user.role != 'advertiser'" :sidebar-open="sidebarOpen" :is-collapsed="isCollapsed"
      @close-sidebar="sidebarOpen = false" @toggle-sidebar="toggleSidebar" />

    <div :class="['transition-all duration-300',
      user && user.role != 'advertiser' ? (isCollapsed ? 'lg:pl-20' : 'lg:pl-72') : 'pl-0']">
      <Header @open-sidebar="sidebarOpen = true"></Header>

      <main class="py-10">
        <div class="px-4 sm:px-6 lg:px-8">
          <slot></slot>
        </div>
      </main>
    </div>
  </div>
</template>


<script setup>
// ================================
// Imports
// ================================

import { ref, inject } from 'vue'
import Header from "./Header.vue"
import Sidebar from "./Sidebar.vue"


// ================================
// Sidebar State
// ================================

// Controls mobile sidebar visibility (for small screens)
const sidebarOpen = ref(false)

// Controls sidebar collapsed/expanded state (for large screens)
const isCollapsed = ref(false)

const page = inject("page");
const user = page.props.auth.user;

// Toggle the sidebar collapsed state
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
  console.log("Sidebar Toggled:", isCollapsed.value)
}
</script>
