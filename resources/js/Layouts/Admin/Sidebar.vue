<template>

    <TransitionRoot as="template" :show="sidebarOpen">
        <Dialog class="relative z-50 lg:hidden" @close="$emit('close-sidebar')">
            <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0"
                enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100"
                leave-to="opacity-0">
                <div class="fixed inset-0 bg-gray-900/80"></div>
            </TransitionChild>

            <div class="fixed inset-0 flex">
                <TransitionChild as="template" enter="transition ease-in-out duration-300 transform"
                    enter-from="-translate-x-full" enter-to="translate-x-0"
                    leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0"
                    leave-to="-translate-x-full">
                    <DialogPanel class="relative mr-16 flex w-full max-w-xs flex-1">
                        <TransitionChild as="template" enter="ease-in-out duration-300" enter-from="opacity-0"
                            enter-to="opacity-100" leave="ease-in-out duration-300" leave-from="opacity-100"
                            leave-to="opacity-0">
                            <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                                <button type="button" class="-m-2.5 p-2.5" @click="$emit('close-sidebar')">
                                    <span class="sr-only">Close sidebar</span>
                                    <XMarkIcon class="size-6 text-white" aria-hidden="true" />
                                </button>
                            </div>
                        </TransitionChild>
                        <div
                            class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4 ring-1 ring-white/10">
                            <div class="flex h-16 shrink-0 items-center">
                                <Logo />
                            </div>
                            <nav class="flex flex-1 flex-col">
                                <ul role="list" class="flex flex-1 flex-col gap-y-7">
                                    <li>
                                        <ul role="list" class="-mx-2 space-y-1">
                                            <li v-for="item in navigation" :key="item.name">
                                                <!-- Main link -->
                                                <div v-if="!item.children">
                                                    <Link :href="route(item.href)" @click="$emit('close-sidebar')"
                                                        :class="[
                                                            isActive(item.href).value ? 'bg-indigo-600 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white',
                                                            'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
                                                        ]">
                                                    <component :is="item.icon" class="size-6 shrink-0"
                                                        aria-hidden="true" />
                                                    <span :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">{{
                                                        item.name }}</span>
                                                    </Link>
                                                </div>

                                                <!-- Collapsible Teams Menu -->
                                                <div v-else>
                                                    <button @click="openMenus[item.name] = !openMenus[item.name]"
                                                        class="w-full group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white">
                                                        <component :is="item.icon" class="size-6 shrink-0"
                                                            aria-hidden="true" />
                                                        <span class="whitespace-nowrap overflow-hidden text-ellipsis"
                                                            :class="{ 'lg:hidden': isCollapsed }">
                                                            {{ item.name }}
                                                        </span>
                                                        <svg :class="['mt-1 ml-auto transition-transform']"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            class="h-4 w-4 text-gray-400" fill="none"
                                                            viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M9 5l7 7-7 7" />
                                                        </svg>
                                                    </button>
                                                    <transition name="fade">
                                                        <ul v-show="openMenus[item.name]" class="ml-2 space-y-1">
                                                            <li v-for="child in item.children" :key="child.name"
                                                                class="py-1">
                                                                <Link :href="route(child.href)" :class="[
                                                                    isChildActive(child.href).value ? 'bg-indigo-600 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white',
                                                                    'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
                                                                ]">
                                                                <ChevronRight class="size-5 shrink-0"
                                                                    aria-hidden="true" />

                                                                <span
                                                                    :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">{{
                                                                        child.name }}</span>
                                                                </Link>
                                                            </li>
                                                        </ul>
                                                    </transition>
                                                </div>
                                            </li>


                                            <li v-if="role == page.props.roleEnums.Advertiser">
                                                <hr class="h-px my-3 bg-gray-700 border-0 dark:bg-gray-700">

                                                <div class="text-gray-400 text-2xl mb-2">Marketplace</div>
                                                <Link :href="route('advertiser.publisher-signup')" :class="[
                                                    isActive('advertiser.publisher-signup').value ? 'bg-indigo-600 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white',
                                                    'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
                                                ]">
                                                <component :is="UserCheck" class="size-6 shrink-0" aria-hidden="true" />
                                                <span :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">Become a
                                                    Publisher</span>
                                                </Link>
                                            </li>
                                        </ul>
                                    </li>
                                    <li class="mt-auto">
                                        <div>
                                            <hr class="h-px my-3 bg-gray-700 border-0 dark:bg-gray-700">
                                            <a href="#"
                                                class="group -mx-2 flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white">
                                                <MessageCircleQuestion class="size-6 shrink-0" aria-hidden="true" />
                                                <span :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">Help</span>
                                            </a>
                                            <a href="#"
                                                class="group -mx-2 flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white">
                                                <Languages class="size-6 shrink-0" aria-hidden="true" />
                                                <span
                                                    :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">Language</span>
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </DialogPanel>
                </TransitionChild>
            </div>
        </Dialog>
    </TransitionRoot>
    <!-- Static sidebar for desktop -->
    <div
        :class="['hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:flex-col transition-all duration-300', isCollapsed ? 'lg:w-[72px]' : 'lg:w-72']">
        <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
            <div class="flex h-16 shrink-0 items-center">
                <Logo :is-collapsed="isCollapsed" />

            </div>

            <nav class="flex flex-1 flex-col">
                <ul role="list" class="flex flex-1 flex-col gap-y-7">
                    <li>
                        <ul role="list" class="-mx-2 space-y-1">
                            <li v-for="item in navigation" :key="item.name">
                                <!-- Main link -->
                                <div v-if="!item.children">
                                    <Link :href="route(item.href)" :class="[
                                        isActive(item.href).value ? 'bg-indigo-600 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white',
                                        'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
                                    ]">
                                    <component :is="item.icon" class="size-6 shrink-0" aria-hidden="true" />
                                    <span :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">{{ item.name }}</span>
                                    </Link>
                                </div>

                                <!-- Collapsible Teams Menu -->
                                <div v-else>
                                    <button @click="openMenus[item.name] = !openMenus[item.name]"
                                        class="w-full group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white">
                                        <component :is="item.icon" class="size-6 shrink-0" aria-hidden="true" />
                                        <span class="whitespace-nowrap overflow-hidden text-ellipsis"
                                            :class="{ 'lg:hidden': isCollapsed }">
                                            {{ item.name }}
                                        </span>
                                        <svg :class="['mt-1 ml-auto transition-transform']"
                                            xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>
                                    <transition name="fade">
                                        <ul v-show="openMenus[item.name]" class="ml-2 space-y-1">
                                            <li v-for="child in item.children" :key="child.name" class="py-1">
                                                <Link :href="route(child.href)" :class="[
                                                    isChildActive(child.href).value ? 'bg-indigo-600 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white',
                                                    'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
                                                ]">
                                                <ChevronRight class="size-5 shrink-0" aria-hidden="true" />

                                                <span :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">{{
                                                    child.name }}</span>
                                                </Link>
                                            </li>
                                        </ul>
                                    </transition>
                                </div>
                            </li>


                            <li v-if="role == page.props.roleEnums.Advertiser">
                                <hr class="h-px my-3 bg-gray-700 border-0 dark:bg-gray-700">

                                <div class="text-gray-400 text-2xl mb-2">Marketplace</div>
                                <Link :href="route('advertiser.publisher-signup')" :class="[
                                    isActive('advertiser.publisher-signup').value ? 'bg-indigo-600 text-white' : 'text-gray-400 hover:bg-gray-800 hover:text-white',
                                    'group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold'
                                ]">
                                <component :is="UserCheck" class="size-6 shrink-0" aria-hidden="true" />
                                <span :class="[isCollapsed ? 'inline lg:hidden' : 'inline']">Become a Publisher</span>
                                </Link>
                            </li>
                        </ul>
                    </li>

                    <li class="mt-auto">
                        <div class="">
                            <hr class="h-px my-3 bg-gray-700 border-0 dark:bg-gray-700">

                            <a href="#"
                                class="group -mx-2 flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white">
                                <MessageCircleQuestion class="size-6 shrink-0" aria-hidden="true" />
                                <span v-if="!isCollapsed">Help</span>
                            </a>
                            <a href="#"
                                class="group -mx-2 flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white">
                                <Languages class="size-6 shrink-0" aria-hidden="true" />
                                <span v-if="!isCollapsed">Language</span>
                            </a>
                            <button @click="$emit('toggle-sidebar')"
                                class="group  -mx-2 flex gap-x-3 rounded-md p-2 min-w-10 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white w-full text-left me-0">
                                <SquareChevronLeft v-if="!isCollapsed" class="size-6 shrink-0" aria-hidden="true" />
                                <SquareChevronRight v-if="isCollapsed" class="size-6 shrink-0" aria-hidden="true" />
                                <span v-if="!isCollapsed"
                                    class="whitespace-nowrap overflow-hidden text-ellipsis">Collapse Menu</span>
                            </button>

                        </div>
                    </li>

                </ul>
            </nav>
        </div>
    </div>
    <!-- Static sidebar for desktop -->
</template>



<script setup lang="ts">
// ================================
// Imports
// ================================

import { computed, ref, inject } from 'vue';
import { route } from "ziggy-js";
import { PageProps } from "@inertiajs/core";

import Logo from "../Logo.vue";
import navigationData from '../../../../app/data/navigation.json';

import {
    Dialog,
    DialogPanel,
    TransitionChild,
    TransitionRoot,
} from '@headlessui/vue';

import {
    XMarkIcon,
} from '@heroicons/vue/24/outline';

import {
    Home,
    Users,
    ListTodo,
    SquareMousePointer,
    CircleDollarSign,
    Settings,
    MessageCircleQuestion,
    Languages,
    SquareChevronRight,
    SquareChevronLeft,
    UserCheck,
    Wallet,
    ChevronRight
} from 'lucide-vue-next';


// ================================
// Props / Emits
// ================================

defineProps({
    sidebarOpen: { type: Boolean, required: true },
    isCollapsed: { type: Boolean, required: true }
});

defineEmits(['close-sidebar', 'toggle-sidebar']);


// ================================
// State
// ================================

// Controls open/close for teams dropdown
// const teamsOpen = ref(false);
const openMenus = ref<{ [key: string]: boolean }>({});


// Inject current page context from Inertia
const page = inject("page") as any;

// Extract current user role from auth props
const role = page.props.auth?.user?.role;


// ================================
// Icon Mapping
// ================================

const iconMap: { [key: string]: any } = {
    Home,
    Users,
    ListTodo,
    SquareMousePointer,
    CircleDollarSign,
    Settings,
    MessageCircleQuestion,
    Languages,
    SquareChevronRight,
    SquareChevronLeft,
    UserCheck,
    Wallet,
    ChevronRight
};

// ================================
// Navigation Menu Items (Role Based)
// ================================

let navigation: any = [];

// Helper function to process navigation items and map icons
const processNavigationItems = (items: any[]) => {
    return items.map(item => ({
        ...item,
        icon: iconMap[item.icon],
        children: item.children ? processNavigationItems(item.children) : undefined
    }));
};

// Determine navigation based on role and access
if (page.props.superAdminAccess) {
    navigation = processNavigationItems(navigationData.superAdmin.navigation);
} else if (role === page.props.roleEnums.Outreach) {
    navigation = processNavigationItems(navigationData.outreach.navigation);
} else if (role === page.props.roleEnums.Writer) {
    navigation = processNavigationItems(navigationData.writer.navigation);
} else if (role === page.props.roleEnums.Sales) {
    navigation = processNavigationItems(navigationData.sales.navigation);
} else if (role === page.props.roleEnums?.Finance) {
    navigation = processNavigationItems(navigationData.finance.navigation);
} else if (role === page.props.roleEnums?.Publisher) {
    navigation = processNavigationItems(navigationData.publisher.navigation);
} else if (role === page.props.roleEnums?.Advertiser) {
    navigation = processNavigationItems(navigationData.advertiser.navigation);
}

// ================================
// Utility: Normalize Path
// ================================
const normalizePath = (path: string) => path.split('?')[0].replace(/\/+$/, '');

// ================================
// Current Path (Normalized)
// ================================
const currentPath = normalizePath(page.url);


// ================================
// Utility: Active Link Checker

// Checks if the current path is the target path.
//
// @param href - The target path to check
// @returns A computed boolean indicating if the current path is the target path
//
// ================================


const isActive = (href: string) => {
    return computed(() => {
        const currentPath = normalizePath(page.url);
        const targetPath = normalizePath(route(href, undefined, false));


        // Special handling for dashboard routes - only exact match
        if (href === 'admin.dashboard' || href === 'publisher.dashboard' || href === 'dashboard') {
            return currentPath === targetPath;
        }

        // Special handling for publisher.orders.index
        if (href === 'publisher.orders.index') {
            return currentPath === targetPath || currentPath.startsWith('/publisher/order/');
        }

        // Special handling for publisher.wallet.index
        if (href === 'publisher.wallet.index') {
            return currentPath === targetPath || currentPath.startsWith('/wallet/withdraw');
        }

        // Special handling for admin teams
        if (href === 'admin.outreach.stats') {
            return currentPath === targetPath || currentPath.startsWith('/pb-admin/teams/outreach/');
        }
        if (href === 'admin.writers.assignments') {
            return currentPath === targetPath || currentPath.startsWith('/pb-admin/teams/assignments/');
        }

        if (href === 'admin.writer.dashboard') {
            return currentPath === targetPath || currentPath.startsWith('/pb-admin/teams/writer/');
        }

        if (href === 'admin.writer.assignment.index') {
            return currentPath === targetPath || currentPath.startsWith('/pb-admin/teams/assignments/');
        }
        // General case: match if currentPath starts with targetPath and not root
        return currentPath === targetPath || (targetPath !== '/' && currentPath.startsWith(targetPath + '/'));
    });
};


// ================================
// Utility: Active Link Checker (Child)
//
// Checks if the current path is a child of the target path.
//
// @param childHref - The child path to check
// @returns A computed boolean indicating if the current path is a child of the target path
//
// ================================


const isChildActive = (childHref: string) => {
    return computed(() => {
        const currentPath = normalizePath(page.url);
        const childPath = normalizePath(route(childHref, undefined, false));
        return currentPath === childPath || currentPath.startsWith(childPath + '/');
    });
};

</script>


<style lang="scss" scoped></style>