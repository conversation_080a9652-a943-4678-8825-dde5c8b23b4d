//----------------------------------------------------------//
//--------------- Stripe Element Form Options --------------//
//----------------------------------------------------------//
// 1. Apperance variables for element div.                  //
// 2. Payment Element Options and fields..etc               //
//----------------------------------------------------------//
//----------------------------------------------------------//      
const appearance = {
  theme: 'stripe',

  variables: {
    colorPrimary: '#059669',
    colorBackground: '#ffffff',
    colorText: '#374151',
    colorDanger: '#dc2626',
    // spacingUnit: '5px',
    borderRadius: '6px',
    fontWeightNormal: '500',
    spacingGridRow: '1.5rem',
    fontLineHeight: '1.5rem',
  }
};


const paymentElementOptions = {
  layout: "tabs",
  // fields: {
  //   billingDetails: {
  //     name: 'never',
  //     email: 'never',
  //   }
  // }
};




let elements;

initialize();
checkStatus();

// listen for submit event
document.querySelector("#payment-form")
        .addEventListener("submit", handleSubmit);




// window.initializeStripe = function(){return initialize()}



//----------------------------------------------------------//
//--------------- Stripe Elements Initialize ---------------//
//----------------------------------------------------------//
// Fetches a payment intent from backend and captures the   //
// client secret via stripe js.                             //
// 1. Get intent id from backend                            //
// 2. Mount the form                                        //
//----------------------------------------------------------//
//----------------------------------------------------------//
async function initialize() {


  // Set up Stripe.js and Elements to use in checkout form, passing the client secret obtained in a previous step
  elements = await stripe.elements({clientSecret, appearance});

  // Create and mount the Payment Element
  const paymentElement = await elements.create('payment', paymentElementOptions);
  paymentElement.mount('#payment-element');


  // 1. get intent id from backend
  // const { clientSecret } = await fetch("/payment/stripe_intent", {
  //   method: "POST",
  //   headers: { "Content-Type": "application/json" }
  // }).then((r) => r.json());


  // // initialise stripe element
  // elements = stripe.elements({ clientSecret, appearance });


  // // mount the element
  // const paymentElement = elements.create("payment", paymentElementOptions);
  // paymentElement.mount("#payment-element");
}







//------------------------------------------------------------//
//------------------ Handle Form Submission ----------------- //
//------------------------------------------------------------//
async function handleSubmit(e) {
  e.preventDefault();
  setLoading(true);


  const { error } = await stripe.confirmPayment({
    elements,
    confirmParams: {
      return_url: paymentSuccessReturnURL, //payment completion page
      // receipt_email: emailAddress,
      // payment_method_data: {
      //   billing_details: {
      //     name: 'Jenny Rosen',
      //     email: '<EMAIL>',
      //   }
      // },
    },
  });






  //----------------------------------------------------------------------------//
  //---------------------------- Handling Errors ------------------------------ //
  //----------------------------------------------------------------------------//
  // This point will only be reached if there is an immediate error when        //
  // confirming the payment. Otherwise, your customer will be redirected to     //
  // your `return_url`. For some payment methods like iDEAL, your customer will //
  // be redirected to an intermediate site first to authorize the payment, then //
  // redirected to the `return_url`.                                            //
  //----------------------------------------------------------------------------//
  if (error.type === "card_error" || error.type === "validation_error") {
    showMessage(error.message);
  } else {
    showMessage("An unexpected error occurred.");
  }

  // loading icon
  setLoading(false);
}






//------------------------------------------------------------------------//
//------------------------- Check Payment Status -------------------------//
//------------------------------------------------------------------------//
// Fetches the payment status based on intent id after payment submission.//
// Can be used for frontend payment success verification.                 //
//------------------------------------------------------------------------//
//------------------------------------------------------------------------//
async function checkStatus() {
  const clientSecret = new URLSearchParams(window.location.search).get(
    "payment_intent_client_secret"
  );

  if (!clientSecret) {
    return;
  }

  const { paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);

  switch (paymentIntent.status) {
    case "succeeded":
      showMessage("Payment succeeded!");
      break;
    case "processing":
      showMessage("Your payment is processing.");
      break;
    case "requires_payment_method":
      showMessage("Your payment was not successful, please try again.");
      break;
    default:
      showMessage("Something went wrong.");
      break;
  }
}









//------------------------------------------------------------------------//
//------------------------------ UI Helpers ------------------------------//
//------------------------------------------------------------------------//
function showMessage(messageText) {
  const messageContainer = document.querySelector("#payment-message");

  messageContainer.classList.remove("hide-content");
  messageContainer.textContent = messageText;

  // setTimeout(function () {
  //   messageContainer.classList.add("hide-content");
  //   messageContainer.textContent = "";
  // }, 12000);
}


// Show a spinner on payment submission + text + active state
function setLoading(isLoading) {
  if (isLoading) {
    // Disable the button and show a spinner
    document.querySelector("#submit-button").disabled = true;
    document.querySelector("#spinner").classList.remove("hide-content");
    document.querySelector("#payment-button-text").classList.add("hide-content");
  } else {
    document.querySelector("#submit-button").disabled = false;
    document.querySelector("#spinner").classList.add("hide-content");
    document.querySelector("#payment-button-text").classList.remove("hide-content");
  }
}