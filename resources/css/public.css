/*************************/
/* Public Pages CSS File */
/* Homepage..etc */
/*************************/

/* Tailwind and design system */
@import './design-system.css';


/*****************/
/*---- Fonts ----*/
/*****************/
@font-face{
    font-family:title-h;
    src:url('https://pressbear.com/storage/resources/fonts/mw-300.woff2')format("woff");
    font-weight:300;
    font-style:normal;
    font-display: swap;
}

@font-face{
    font-family:title-h;
    src:url('https://pressbear.com/storage/resources/fonts/mw-400.woff2')format("woff");
    font-weight:400;
    font-style:normal;
    font-display: swap;
}

@font-face{
    font-family:title-h;
    src:url('https://pressbear.com/storage/resources/fonts/mw-500.woff2')format("woff");
    font-weight:500;
    font-style:normal;
    font-display: swap;
}

/*@font-face{
    font-family:content-h;
    src:url('https://pressbear.com/storage/resources/fonts/gk-300.woff')format("woff");
    font-weight:300;
    font-style:normal;
    font-display:fallback
}*/

@font-face{
    font-family:content-h;
    src:url('https://pressbear.com/storage/resources/fonts/gk-400.woff')format("woff");
    font-weight:400;
    font-style:normal;
    font-display: swap;
}
@font-face{
    font-family:content-h;
    src:url('https://pressbear.com/storage/resources/fonts/gk-400-italic.woff')format("woff");
    font-weight:400;
    font-style:italic;
    font-display: swap;
}
@font-face{
    font-family:content-h;
    src:url('https://pressbear.com/storage/resources/fonts/gk-500.woff')format("woff");
    font-weight:500;
    font-style:normal;
    font-display: swap;
}

@font-face{
    font-family:content-h;
    src:url('https://pressbear.com/storage/resources/fonts/gk-600.woff')format("woff");
    font-weight:600;
    font-style:normal;
    font-display:fallback
}


body{
  font-family: 'content-h', 'system-ui', 'Roboto';
}

.title-h{
  font-family:'title-h', 'system-ui', 'Roboto';
}

.content-h{
  font-family:'content-h', 'system-ui', 'Roboto';
}






/*****************/
/*---- MENU ----*/
/*****************/
/* Menu Top */
.menu-top-public{
  @apply inline-block py-1 px-2 font-medium text-muted-foreground rounded-md mx-2 hover:text-orange-600 hover:bg-background;
}

/* Public CSS */
.menu-top-public-dash{
  @apply inline-block py-1 px-3 font-semibold text-muted-foreground rounded-2xl mx-2  hover:text-orange-500 hover:bg-orange-50/50 bg-slate-100;
}









/*Chaning text home*/
#changing-word {
  @apply text-transparent bg-clip-text bg-gradient-to-br from-slate-700 to-slate-800;
}



/*************************/
/*---- BULLET POINTS ----*/
/*************************/
/*ul.tick-list li {
  list-style: none; 
  position: relative;
}
*/
/*ul.tick-list li::before {
  content: url('https://pressbear.test/storage/resources/icons/check-lucide.svg'); 
  position: absolute;
   left: -25px;
}*/




/******************/
/*---- FOOTER ----*/
/******************/
.footer-link{
  @apply text-base px-4 py-1.5 leading-6 text-slate-500 hover:text-slate-700 dark:text-slate-400 hover:bg-slate-50 rounded-lg;
}
 
.public-footer-link{
  @apply text-sm leading-6 text-slate-500 hover:text-orange-800 hover:bg-background px-2.5 py-2 rounded-md;
}

.public-footer-link-head{
    @apply text-sm leading-6 text-slate-600 px-2 font-bold ;
}

.footer-social-icon-single{
  @apply text-slate-500 hover:text-slate-700 p-2 hover:bg-slate-50 rounded-full;
}

