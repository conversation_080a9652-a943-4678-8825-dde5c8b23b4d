<!DOCTYPE html>
<html class="scroll-smooth" lang="{{ str_replace('_', '-', app()->getLocale()) }}">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <title>Admin - {{ config('app.name', 'SEO') }}</title>
        <link rel="icon" href="{{Storage::url('graphics/bear-smile-svgrepo-com4.png')}}" />
        <link rel="icon" type="image/svg+xml" sizes="any" href="{{Storage::url('favicon.svg')}}" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- livewire -->
        @livewireStyles
        @livewireScripts

    </head>

 
    {{-- **** --}}
    {{-- BODY --}}
    <body class="dark:bg-gray-900 flex flex-col">


        {{-- HEADER SECTION START --}}
        <div id="app-head">

          <div class="flex items-center border-b px-4 py-3 max-w-7xl ">


            {{-- logo --}}
            <div class="flex mx-8">
                <a wire:navigate href="{{route('admin-dashboard')}}" 
                    class="flex items-center space-x-2 font-extrabold text-gray-700 md:py-0">

                    <span class="flex items-center justify-center w-8 h-8 rounded-full">
                    
                      {{-- <x-icons.etc.link /> --}}
                      <x-icons.etc.bear class="w-8 h-8 text-gray-700 bg-slate-50 
                                              fill-gray-700 stroke-2" />
                    </span>
                    <span class="text-xl text-gray-700">PressBear</span>
                    <span class="ml-2 text-emerald-700 font-semibold uppercase text-xs">Admin</span>

                </a>
            </div>


            {{-- ADMIN MENU --}}
            <div id="menu-items">
               <div class="relative">
                  <ul class="flex items-center justify-center flex-1 p-1 space-x-1 
                             list-none text-muted-foreground group">

                     <li>
                        <a wire:navigate href="{{route('admin-orders')}}" 
                           class="top-menu-main-link group">
                           Orders
                        </a>
                     </li>

                     <li>
                        <a wire:navigate href="{{route('admin-websites')}}" 
                           class="top-menu-main-link group">
                           Websites
                        </a>
                     </li>


                     <li>
                        <a wire:navigate href="{{route('marketplace')}}" 
                           class="top-menu-main-link group">
                           Marketplace
                        </a>
                     </li>

                  </ul>
               </div>
            </div>
            
          </div>
        </div>
        {{-- HEADER SECTION END --}}




        {{-- CONTENT BODY --}}
        <div class="flex flex-col  min-h-screen bg-zinc-100 w-full">

            {{ $slot }}

            @yield('content')

        </div>


        <x-app.footer/>
        
    </body>
</html>




