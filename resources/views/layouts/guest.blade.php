{{-- USED BY LARAVEL FOR LOGIN PAGES --}}
<!DOCTYPE html>
<html class="scroll-smooth" lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Marketplace') }}</title>

    <link rel="icon" href="{{ Storage::url('graphics/bear-smile-svgrepo-com4.png') }}" />
    <link rel="icon" type="image/svg+xml" sizes="any" href="{{ Storage::url('favicon.svg') }}" />

    <meta name="robots" content="noindex, nofollow" />

    <!-- jQuery (MUST come before Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>


    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>


    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    {{-- Microsoft Clarity For Analytics --}}
    <x-app.services.clarity-microsoft />

</head>

<body>
    <div class="w-full px-2 sm:px-6 my-2 sm:my-6 antialiased bg-white border-gray-200 style="background-image: url({{ Storage::url('graphics/bg.svg') }}); background-size: cover;">
        {{ $slot }}
    </div>
    @stack('scripts')

</body>

</html>
