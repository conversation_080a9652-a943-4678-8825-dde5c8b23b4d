<!DOCTYPE html>
<html class="scroll-smooth max-w-full ">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>{{ $title ?? 'PressBear' }}</title>
  <meta name="description"
    content="{{ $description ?? 'PressBear is a marketplace providing sponsorship and link-building opportunities across thousands of diverse websites.' }}" />
  <meta name="robots" content="index, follow" />
  <link rel="canonical" href="{{ url()->current() }}" />
  <link rel="icon" href="{{Storage::url('graphics/bear-smile-svgrepo-com4.png')}}" />

  {{-- Social --}}
  <meta property="og:locale" content="en_US" />
  <meta property="og:type" content="website" />
  <meta property="og:title" content="PressBear - Marketplace for Sponsorship & Link Building" />
  <meta property="og:description"
    content="PressBear is a marketplace connecting brands with over 20k publishers for effortless sponsorships and link-building. Streamline PR, boost rankings, and grow your online presence with quality-driven, strategic partnerships." />
  <meta property="og:url" content="https://pressbear.com" />
  <meta property="og:site_name" content="PressBear" />
  <meta property="og:image" content="https://pressbear.com/storage/resources/homepage/hero-sep.png" />
  <meta property="og:image:width" content="1024" />
  <meta property="og:image:height" content="684" />
  <meta property="og:image:type" content="image/png" />

  {{-- Schema --}}
  <script type="application/ld+json" class="yoast-schema-graph">
    {"@context":"https://schema.org","@graph":[{"@type":"CollectionPage","@id":"https://pressbear.com/","url":"https://pressbear.com/","name":"PressBear","isPartOf":{"@id":"https://pressbear.com/#website"},"about":{"@id":"https://pressbear.com/#organization"},"description":"PressBear is a marketplace connecting brands with over 20k publishers for effortless sponsorships and link-building. Streamline PR, boost rankings, and grow your online presence with quality-driven, strategic partnerships.","inLanguage":"en-US"},{"@type":"WebSite","@id":"https://pressbear.com/#website","url":"https://pressbear.com/","name":"PressBear","description":"PressBear is a marketplace connecting brands with over 20k publishers for effortless sponsorships and link-building. Streamline PR, boost rankings, and grow your online presence with quality-driven, strategic partnerships.","publisher":{"@id":"https://pressbear.com/#organization"},"alternateName":"Link Building & sponsorships Platform","inLanguage":"en-US"},{"@type":"Organization","@id":"https://pressbear.com/#organization","name":"PressBear","alternateName":"Pressbear","url":"https://pressbear.com/","logo":{"@type":"ImageObject","inLanguage":"en-US","@id":"https://pressbear.com/#/schema/logo/image/","url":"https://pressbear.com/storage/graphics/bear-smile-svgrepo-com4.png","contentUrl":"https://pressbear.com/storage/graphics/bear-smile-svgrepo-com4.png","width":100,"height":100,"caption":"PressBear"},"image":{"@id":"https://pressbear.com/#/schema/logo/image/"},"sameAs":["{{ config('pressbear.facebook_url') }}","{{ config('pressbear.x_url') }}","{{ config('pressbear.instagram_url') }}","{{ config('pressbear.linkedin_url') }}"]}]}
  </script>

  @vite(['resources/css/public.css', 'resources/js/public.js'])
  @livewireScripts

  {{-- Microsoft Clarity For Analytics --}}
  <x-app.services.clarity-microsoft />
</head>



{{-- BODY --}}

<body class="transition-all ease-in-out delay-150 max-w-full overflow-x-hidden">


  {{-- header --}}
  <x-public.header.header-public />


  {{-- Public Content Slot --}}
  @yield('content')



  {{-- Footer --}}
  <x-public.footer-public />


</body>

</html>