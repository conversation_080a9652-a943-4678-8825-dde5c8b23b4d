{{-- Footer --}}
<section class="bg-slate-50/20 border-t-2 dark:bg-slate-800 border-slate-200">
    <div class="max-w-screen-xl px-6 pt-12 pb-8 mx-auto space-y-6 overflow-hidden  ">

        <nav class="flex flex-wrap justify-center space-x-2">
            <a href="{{route('home')}}" class="footer-link-app">
                Home
            </a>
            <a href="{{ route('marketplace') }}" class="footer-link-app">
                Marketplace
            </a>
            {{-- <a href="{{ route('features') }}" class="footer-link-app">
                Features
            </a> --}}
            {{-- <a href="{{ route('blog') }}" class="footer-link-app">
                Blog
            </a> --}}
            <a href="{{ route('terms.show') }}" class="footer-link-app">
                Terms
            </a>
            <a href="{{ route('policy.show') }}" class="footer-link-app">
                Privacy
            </a>
            <a href="{{route('about-us')}}" class="footer-link-app">
                About
            </a>
        </nav>


        <div class="flex justify-center mt-8 space-x-3">

            <a target="_blank" href="{{ config('pressbear.linkedin_url') }}" class="footer-social-icon-single-app">
                <span class="sr-only">Linkedin</span>
                <x-icons.lucide.linkedin />
            </a>

            <a href="{{ config('pressbear.facebook_url') }}" class="footer-social-icon-single-app">
                <span class="sr-only">Facebook</span>
                <x-icons.lucide.facebook />
            </a>

            <a href="{{ config('pressbear.instagram_url') }}" class="footer-social-icon-single-app">
                <span class="sr-only">Instagram</span>
                <x-icons.lucide.instagram />
            </a>

            <a href="{{ config('pressbear.x_url') }}" class="footer-social-icon-single-app">
                <span class="sr-only">Twitter</span>
                <x-icons.lucide.twitter />
            </a>

            {{-- <a href="#" class="text-slate-400 hover:text-slate-500 dark:text-slate-400">
                <span class="sr-only">GitHub</span>
                <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd"
                        d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                        clip-rule="evenodd" />
                </svg>
            </a> --}}


            {{-- <div class="flex items-center border-l-2 border-slate-200 ml-6 pl-6 dark:border-slate-400 ">

                <label class="sr-only">Theme</label>

                <button type="button">

                    <span class="dark:hidden ">
                        <svg viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="w-6 h-6 ">
                            <path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                                class="stroke-slate-200 dark:stroke-slate-500"></path>
                            <path
                                d="M12 4v1M17.66 6.344l-.828.828M20.005 12.004h-1M17.66 17.664l-.828-.828M12 20.01V19M6.34 17.664l.835-.836M3.995 12.004h1.01M6 6l.835.836"
                                class="stroke-slate-400 dark:stroke-slate-500">
                            </path>
                        </svg>
                    </span>

                    <span class="hidden dark:inline ">
                        <svg viewBox="0 0 24 24" fill="none" class="w-6 h-6 dark:text-white">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M17.715 15.15A6.5 6.5 0 0 1 9 6.035C6.106 6.922 4 9.645 4 12.867c0 3.94 3.153 7.136 7.042 7.136 3.101 0 5.734-2.032 6.673-4.853Z"
                                class="fill-transparent"></path>
                            <path
                                d="m17.715 15.15.95.316a1 1 0 0 0-1.445-1.185l.495.869ZM9 6.035l.846.534a1 1 0 0 0-1.14-1.49L9 6.035Zm8.221 8.246a5.47 5.47 0 0 1-2.72.718v2a7.47 7.47 0 0 0 3.71-.98l-.99-1.738Zm-2.72.718A5.5 5.5 0 0 1 9 9.5H7a7.5 7.5 0 0 0 7.5 7.5v-2ZM9 9.5c0-1.079.31-2.082.845-2.93L8.153 5.5A7.47 7.47 0 0 0 7 9.5h2Zm-4 3.368C5 10.089 6.815 7.75 9.292 6.99L8.706 5.08C5.397 6.094 3 9.201 3 12.867h2Zm6.042 6.136C7.718 19.003 5 16.268 5 12.867H3c0 4.48 3.588 8.136 8.042 8.136v-2Zm5.725-4.17c-.81 2.433-3.074 4.17-5.725 4.17v2c3.552 0 6.553-2.327 7.622-5.537l-1.897-.632Z"
                                class="fill-slate-400 dark:fill-slate-500"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M17 3a1 1 0 0 1 1 1 2 2 0 0 0 2 2 1 1 0 1 1 0 2 2 2 0 0 0-2 2 1 1 0 1 1-2 0 2 2 0 0 0-2-2 1 1 0 1 1 0-2 2 2 0 0 0 2-2 1 1 0 0 1 1-1Z"
                                class="fill-slate-400 dark:fill-slate-500"></path>
                        </svg>
                    </span>

                </button>

            </div>
            --}}


            {{-- <div x-data="{ switchOn: false,

                themeColorUpdate(switchOn){
                    if(switchOn == false){
                        localStorage.theme = 'light';
                        document.documentElement.classList.remove('dark');
                         console.log('sec-try');
                       console.log(switchOn);
                        return;  }

                    if(switchOn == true){
                        localStorage.theme = 'dark';
                        document.documentElement.classList.add('dark');
                         console.log('third-try');
                       console.log(switchOn);
                        return; }
                    }
                }" x-init="if(localStorage.getItem('theme') == 'dark'){
                           document.documentElement.classList.add('dark');
                           switchOn = true; }" class="flex items-center justify-center space-x-2">

                <input id="thisId" type="checkbox" name="switch" class="hidden" :checked="switchOn">
                <button x-ref="switchButton" type="button" @click="switchOn = ! switchOn, themeColorUpdate(switchOn)"
                    :class="switchOn ? 'bg-slate-500' : 'bg-neutral-200'"
                    class="relative inline-flex h-6 py-0.5 ml-4 focus:outline-none rounded-full w-10" x-cloak>
                    <span :class="switchOn ? 'translate-x-[18px]' : 'translate-x-0.5'"
                        class="w-5 h-5 duration-200 ease-in-out bg-white rounded-full shadow-md "></span>
                </button>

                <label @click="$refs.switchButton.click(); $refs.switchButton.focus()" :id="$id('switch')"
                    :class="{'text-slate-500': switchOn, 'text-slate-500': ! switchOn }"
                    class="text-sm mr-1 select-none font-medium dark:text-slate-400" x-cloak>
                    Dark Mode
                </label>
            </div>
            --}}
        </div>

        <p class="mt-8 text-base leading-6 text-center text-slate-400">
            &copy; {{ date('Y') }} PressBear, LLC. All rights reserved.
        </p>

    </div>
</section>