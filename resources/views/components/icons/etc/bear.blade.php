<svg width="28px" height="28px" class="{{ $class ?? '' }}"  
    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <g >
        <path fill="none" d="M0 0h24v24H0z"/>
        <path stroke="currentColor" stroke-width="0.7" d="M17.5 2a4.5 4.5 0 0 1 2.951 7.897c.355.967.549 2.013.549 3.103A9 9 0 1 1 3.55 9.897a4.5 4.5 0 1 1 6.791-5.744 9.05 9.05 0 0 1 3.32 0A4.494 4.494 0 0 1 17.5 2zm0 2c-.823 0-1.575.4-2.038 1.052l-.095.144-.718 1.176-1.355-.253a7.05 7.05 0 0 0-2.267-.052l-.316.052-1.356.255-.72-1.176A2.5 2.5 0 1 0 4.73 8.265l.131.123 1.041.904-.475 1.295A7 7 0 1 0 19 13c0-.716-.107-1.416-.314-2.083l-.112-.33-.475-1.295 1.04-.904A2.5 2.5 0 0 0 17.5 4zM10 13a2 2 0 1 0 4 0h2a4 4 0 1 1-8 0h2z"/>
    </g>
</svg>


{{-- <svg width="28px" height="28px" stroke-width="6" class="text-amber-600 bg-amber-50 fill-amber-600 "  version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
   viewBox="0 0 512.001 512.001" xml:space="preserve">
<g>
  <g>
    <g>
      <path d="M495.401,78.763c-13.529-18.61-33.411-30.698-55.984-34.037c-30.235-4.473-60.558,8.172-79.308,32.049
        C331.963,65.604,297.395,60.089,256,60.089s-75.963,5.515-104.11,16.687c-18.749-23.877-49.071-36.527-79.308-32.049
        C50.009,48.065,30.128,60.153,16.6,78.763C3.141,97.276-2.412,120.045,0.964,142.872c4.769,32.246,26.468,58.408,55.853,69.03
        c-3.108,22.967-3.845,46.891-3.845,70.496c0,58.27,19.662,106.219,56.863,138.666c35.33,30.815,85.873,47.103,146.166,47.103
        s110.835-16.287,146.166-47.103c37.2-32.446,56.863-80.395,56.863-138.666c0-23.607-0.738-47.529-3.845-70.496
        c29.386-10.622,51.085-36.785,55.853-69.03C514.413,120.045,508.859,97.276,495.401,78.763z M31.973,138.285
        c-2.168-14.662,1.376-29.255,9.982-41.092c8.534-11.741,21.042-19.362,35.214-21.458C94.2,73.218,111.3,79.38,123.226,91.547
        c-15.106,9.922-27.613,22.209-37.6,36.927c-10.619,15.649-17.843,33.272-22.739,52.044
        C46.58,172.826,34.77,157.196,31.973,138.285z M256,356.579c-3.962,0-12.476-4.301-20.45-12.27
        c-8.109-8.105-12.352-16.612-12.352-20.827c0-3.764,0.645-4.035,3.043-5.044c6.834-2.875,20.258-3.208,29.76-3.208
        c9.502,0,22.925,0.332,29.76,3.208c2.398,1.008,3.043,1.28,3.043,5.044c0,4.215-4.243,12.721-12.352,20.827
        C268.477,352.278,259.962,356.579,256,356.579z M381.561,397.442c-26.548,23.156-64.318,36.493-109.888,38.956v-51.602
        c12.306-4.749,22.329-13.711,26.936-18.315c6.477-6.473,21.54-23.62,21.54-42.998c0-15.853-7.896-27.905-22.233-33.936
        c-9.556-4.021-21.701-5.661-41.916-5.661c-20.215,0-32.36,1.64-41.916,5.661c-14.337,6.032-22.233,18.085-22.233,33.936
        c0,19.378,15.064,36.525,21.54,42.998c4.607,4.605,14.629,13.567,26.936,18.315v51.602c-45.57-2.463-83.34-15.799-109.888-38.956
        c-30.604-26.692-46.122-65.398-46.122-115.043c0-49.948,3.301-101.034,27.247-136.325c11.827-17.43,28.021-30.266,49.508-39.24
        C185.539,96.616,217.477,91.435,256,91.435c38.523,0,70.461,5.18,94.927,15.398c21.487,8.973,37.681,21.81,49.508,39.24
        c23.945,35.29,27.247,86.376,27.247,136.325C427.682,332.042,412.165,370.748,381.561,397.442z M480.027,138.286
        c-2.797,18.91-14.607,34.54-30.913,42.232c-4.897-18.772-12.122-36.395-22.74-52.044c-9.987-14.718-22.494-27.005-37.6-36.927
        c11.927-12.169,29.03-18.332,46.058-15.812c14.173,2.096,26.68,9.716,35.214,21.458
        C478.651,109.031,482.196,123.624,480.027,138.286z"/>
      <path d="M203.838,200.474c-11.34,0-20.568,9.323-20.568,20.835c0,11.51,9.228,20.842,20.568,20.842
        c11.355,0,20.582-9.332,20.582-20.842C224.42,209.798,215.194,200.474,203.838,200.474z"/>
      <path d="M308.007,200.312c-11.45,0-20.737,9.401-20.737,20.998s9.288,20.998,20.737,20.998c11.436,0,20.724-9.401,20.724-20.998
        C328.73,209.713,319.442,200.312,308.007,200.312z"/>
    </g>
  </g>
</g>
</svg> --}}