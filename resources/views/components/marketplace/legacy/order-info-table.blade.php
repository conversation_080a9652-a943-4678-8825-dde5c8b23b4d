<!-- Orders Table old -->
<div id="order-items-table-user" style="overflow-x:auto;"
  class="bg-white border border-zinc-200 rounded-md shadow my-12">
  <table class="min-w-full divide-y whitespace-nowrap divide-gray-200 dark:divide-gray-700 border-b bg-white ">

    <thead
      class="bg-gray-100 text-gray-700 border-b dark:bg-zinc-900 dark:text-zinc-100 tracking-wide uppercase text-xs font-semibold overflow-scroll">
      <tr>
        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            <span title="Website where your content will be published.">
              Website
            </span>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Niche
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="Content niche you selected at checkout." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Price
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="The amount you paid for this specific order item." />
            </div>
          </div>
        </th>


        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Status
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="Current Order Status" />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Topic Title
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="You can provide Topic or Title for content if you want us to write." />
            </div>
          </div>
        </th>


        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Content Link
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?"
                tooltip="Please provide Google Docs Link if you want to publish your own written content or else we will write it for you." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Primary Link
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?"
                tooltip="Your primary backlink url. You can add additional in requirements/comment section." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Link Anchor
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="The anchor text for your primary link." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Requirements
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="You can add your requirement here for this website." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div class="flex items-center gap-x-2">
            Published URL
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="URL where you post is published." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-4 py-3">
          <div title="last time this item was updated" class="flex items-center gap-x-2">
            Last Updated
          </div>
        </th>

    </thead>


    <tbody class="divide-y divide-gray-200 dark:divide-gray-700 text-gray-600 font-medium">
      <tr>
        <td class="whitespace-nowrap">

          <div class="p-4">

            <a class="text-sm font-bold text-emerald-900 hover:underline flex items-center mr-2" href="//lifewire.com">

              <img width="16px" height="16px" src="https://www.google.com/s2/favicons?sz=64&domain_url=lifewire.com"
                alt="" class="w-4 h-4 border-1 rounded-full mr-1.5">
              <span class="pr-1 tracking-wide">
                lifewire.com
              </span>
              <div><svg xmlns="http://www.w3.org/2000/svg" class=" opacity-80" width="14px" viewBox="0 0 24 24"
                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-external-link">
                  <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                  <polyline points="15 3 21 3 21 9" />
                  <line x1="10" x2="21" y1="14" y2="3" />
                </svg></div>
            </a>
          </div>
        </td>

        <td class=" whitespace-nowrap">
          <div class="p-4">
            <span class="text-sm">
              Regular
            </span>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4">
            <span class="text-sm">$300</span>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4">
            <span class="text-sm text-orange-900 text-wrap ">✽ Waiting Information</span>
          </div>
        </td>

        <td id="content-link" class="whitespace-nowrap">
          <div class="p-4 flex items-center">
            <input type="text" id="default-input"
              class="bg-zinc-50 border border-zinc-300 text-sm rounded-lg focus:ring-blue-300 focus:border-blue-300 focus:text-blue-950 focus:w-96 focus:font-medium block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-orange-500 dark:focus:border-orange-500 mr-1 w-40 transition-all duration-700 ease-out"
              name="url" placeholder="Optional" value="">
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4 flex items-center">
            <input type="text" id="default-input"
              class="bg-zinc-50 border border-zinc-300 text-sm rounded-lg focus:ring-blue-300 focus:border-blue-300 focus:text-blue-950 focus:w-96 focus:font-medium block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-orange-500 dark:focus:border-orange-500 mr-1 w-40 transition-all duration-700 ease-out"
              name="topic" placeholder="Optional" value="">
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4 flex items-center">
            <input type="text" id="default-input"
              class="bg-zinc-50 border border-zinc-300 text-sm rounded-lg focus:ring-blue-300 focus:border-blue-300 focus:text-blue-950 focus:w-96 focus:font-medium block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-orange-500 dark:focus:border-orange-500 mr-1 w-40 transition-all duration-700 ease-out"
              name="url" placeholder="Your link" value="https://abc.com/sponsor-post">
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4 flex items-center">
            <input type="text" id="default-input"
              class="bg-zinc-50 border border-zinc-300 text-sm rounded-lg focus:ring-blue-300 focus:border-blue-300 focus:text-blue-950 focus:w-60 focus:font-medium block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-orange-500 dark:focus:border-orange-500 mr-1 w-40 transition-all duration-700 ease-out"
              name="anchor" placeholder="optional" value="">
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4 flex items-center">
            <textarea rows="2"
              class="bg-zinc-50 border border-zinc-300 text-xs rounded-lg focus:ring-blue-300 focus:border-blue-300 focus:text-blue-950 focus:w-96 focus:font-medium block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-orange-500 dark:focus:border-orange-500 mr-1 w-50 transition-all duration-700 ease-out"
              name="requirements" placeholder="Add any additional requirements here."></textarea>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <a href="#" class="p-4 flex items-center">
            <span class="text-sm mr-1">lifewire.com/sponsor-post/</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 text-green-600" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-external-link">
              <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
              <polyline points="15 3 21 3 21 9" />
              <line x1="10" x2="21" y1="14" y2="3" />
            </svg>
          </a>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-4">
            <span class="text-sm">6 Jan 23</span>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<!-- End Table -->





<!-- Admin Table -->
<div id="orders-table" style="overflow-x: auto;" class="bg-white border border-zinc-200 rounded-md">
  <table class="divide-y whitespace-nowrap divide-gray-200 dark:divide-gray-700 border-b bg-white table-auto w-full">

    <thead
      class="bg-slate-50 border-b-2 dark:bg-zinc-900 text-slate-600 dark:text-zinc-100 tracking-wide uppercase text-xs font-semibold">
      <tr>

        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            <span title="website where content will be published.">
              Website
            </span>
          </div>
        </th>

        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Niche
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="Content Nature. (i.e: Casino is senstive) " />
            </div>
          </div>
        </th>

        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Charged
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="The amount we charged to the customer for this website." />
            </div>
          </div>
        </th>


        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Est Price
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?"
                tooltip="This is our estimated price for this website, based on previous sale or estimation." />
            </div>
          </div>
        </th>


        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            We Paid
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="The amount we negotiated and paid to this website." />
            </div>
          </div>
        </th>


        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Status
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="Current Order Status" />
            </div>
          </div>
        </th>

        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Content
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="Content URL / Google Docs Link" />
            </div>
          </div>
        </th>


        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Published URL
            <div class="tooltip opacity-30 hover:opacity-100 pt-1">
              <x-ui.tooltip text="?" tooltip="The URL where the blog post is published." />
            </div>
          </div>
        </th>

        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Publisher Email
          </div>
        </th>

        <th scope="col" class="px-6 py-3">
          <div class="flex items-center gap-x-2">
            Last Updated
          </div>
        </th>

    </thead>


    <tbody class="divide-y divide-gray-200 dark:divide-gray-700  text-zinc-600 ">


      @foreach($order->orderItems as $order)
      <tr>

        <td class="whitespace-nowrap">
          <div class="p-6">
            <a class="text-sm font-semibold text-emerald-800 decoration-2 hover:underline flex items-center"
              href="//{{$order->website->website_domain}}">
              <img width="16px" height="16px"
                src="https://www.google.com/s2/favicons?sz=64&domain_url={{$order->website->website_domain}}" alt=""
                class="w-4 h-4 border-1 rounded-full mr-1.5">
              <span class="pr-1 tracking-wide">
                {{$order->website->website_domain}}
              </span>

              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 text-green-600" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-external-link">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                <polyline points="15 3 21 3 21 9" />
                <line x1="10" x2="21" y1="14" y2="3" />
              </svg>
            </a>
          </div>
        </td>

        <td class=" whitespace-nowrap">
          <div class="p-6">
            <span class="text-sm">
              {{$order->niche}}
            </span>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6">
            <span class="text-sm">${{$order->price_paid}}</span>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6">
            <span class="text-sm">${{$order->price_paid/2}}</span>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6 flex items-center">
            <span class="font-semibold mr-1 text-zinc-600">$</span>
            <input type="number" id="price-paid"
              class="w-16 bg-zinc-50 border border-zinc-300 text-sm rounded-lg focus:ring-orange-300 focus:border-orange-400 focus:text-orange-900 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              placeholder="" value="${{$order->publisher_payment_paid}}">
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6">
            <select id="status"
              class="bg-zinc-50 border border-zinc-300 text-xs rounded-full block p-2 pl-3 dark:bg-gray-700 focus:ring-orange-300 focus:border-orange-400 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white min-w-min">
              <option value="active">{{$order->state}}</option>
              <option value="US">✽ Inprogress</option>
              <option value="FR">✍︎ Writing</option>
              <option value="DE">⏱︎ Waiting Publishing</option>
              <option value="DE">✔ Published</option>
              <option value="DE">✘ Rejected</option>
            </select>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6 flex items-center">
            <input type="text" id="default-input"
              class="bg-zinc-50 border border-zinc-300 text-xs rounded-lg focus:ring-orange-300 focus:border-orange-400 focus:text-yellow-900 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-orange-500 dark:focus:border-orange-500 mr-1"
              name="url" placeholder="post-url" value="">
            <a href="#">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 text-green-600" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-external-link">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                <polyline points="15 3 21 3 21 9" />
                <line x1="10" x2="21" y1="14" y2="3" />
              </svg>
            </a>
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6 flex items-center">
            <input type="text" id="default-input"
              class="bg-zinc-50 border border-zinc-300 text-zinc-800 text-xs rounded-lg focus:ring-orange-300 focus:border-orange-400 focus:text-yellow-900 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-orange-500 mr-1"
              name="url" placeholder="post-url" value="{{$order->publication->publication_url}}">
            <a href="#">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 text-green-600" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-external-link">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                <polyline points="15 3 21 3 21 9" />
                <line x1="10" x2="21" y1="14" y2="3" />
              </svg>
            </a>
          </div>
        </td>



        <td class="whitespace-nowrap">
          <div class="p-6 flex items-center">
            <input type="text" id="email-publisher"
              class="bg-zinc-50 border border-zinc-300 text-zinc-900 text-xs rounded-lg focus:ring-orange-300 focus:border-orange-400 focus:text-yellow-900 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-zinc-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-orange-500 mr-1"
              name="url" placeholder="post-url" value="{{$order->website->contact_email}}">
          </div>
        </td>

        <td class="whitespace-nowrap">
          <div class="p-6">
            <span class="text-sm">{{$order->updated_at}}</span>
          </div>
        </td>

      </tr>
      @endforeach

    </tbody>
  </table>
</div>
<!-- End Table -->