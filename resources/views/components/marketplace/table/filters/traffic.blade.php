{{-- 2. Traffic - Min/Max - (Single Filter) --}}
{{-- Alpine: open/close, data update, reset filters --}}
<div class="single-filter">

  <div x-data="{isOpen: false, 

                {{-- ********** --}}
                {{-- Data Update --}}
                dataUpdate(){
                  if($store.filters.traffic.min || $store.filters.traffic.max){

                    {{-- if one value is not set --}}
                    if(!$store.filters.traffic.min){ $store.filters.traffic.min = 0}
                    if(!$store.filters.traffic.max){ $store.filters.traffic.max = 100000000}

                    this.numberValueOrderSet(),

                    this.trafficLabel = humanNumber($store.filters.traffic.min) + 
                                        ' - ' + 
                                        humanNumber($store.filters.traffic.max),
                    this.isOpen=false,
                    $store.filters.filtersActive = true,
                    $store.filters.traffic.active = true

                  }else{
                    this.isOpen=false
                  }
                },
                

                {{-- ************ --}}
                {{-- Reset Filter --}}
                resetFilter(){
                  $store.filters.traffic.min=null,
                  $store.filters.traffic.max=null,
                  this.trafficLabel=false,
                  this.isOpen=false,
                  $store.filters.traffic.active=false
                },


                {{-- ********************** --}}
                {{-- Number Order Value Set --}}
                numberValueOrderSet(){

                  min = +$store.filters.traffic.min
                  max = +$store.filters.traffic.max

                  if(min > max){
                    $store.filters.traffic.min = $store.filters.traffic.max,
                    $store.filters.traffic.max = min
                  }
              },

            }">



    {{-- *********** --}}
    {{-- Main Visual Button --}}
    <button x-on:click="isOpen = !isOpen"
            type="button"
            :class="$store.filters.traffic.active ? 'filters-dropdown-active' : ''"
            class="filters-dropdown group" >

      <div class="filters-dropdown-main-label">
        <x-icons.lucide.trending-up class="filter-label-icon" />
        <div id="label"     class="filter-name">Traffic</div>
        <div id="data-text" class="filter-data-label" x-cloak x-show="trafficLabel" x-text="trafficLabel"></div>
      </div>

      <span class="transition" :class="{ 'rotate-180': isOpen }">
        <x-icons.etc.down-filled class="filter-dropdown-icon" />
      </span>
    </button>


    {{-- ************** --}}
    {{-- Dialog preview --}}
    <div  x-cloak
          x-trap="isOpen"
          x-transition
          x-show="isOpen"
          x-on:click.away="isOpen = false"
          x-on:keydown.escape.window="isOpen = false"
          class="dialog-preview">

      <div class="filters-dropdown-div">

        <header class="filters-dropdown-header">
          <span class="filters-dropdown-selection-details">
            Website Traffic
          </span>
          <button 
            type="button"
            class="reset-text-filters"
            @click="resetFilter">
            Reset
          </button>
        </header>



        {{-- ********** --}}
        {{-- input data --}}
        <div class="filter-input-top" @keydown.enter.prevent="dataUpdate">

          <div class="filter-data-wrapper">
            <label class="filters-list-label">
              <span  class="filters-list-label-data">Min</span>
              <input  name="trafficMin"
                      placeholder="From" 
                      class="filters-list-li-input-box"
                      x-mask:dynamic="$input.startsWith('0') ? '9' : '999999999'" 
                      x-model="$store.filters.traffic.min"/>
            </label>

            <label   class="filters-list-label">
              <span  class="filters-list-label-data">Max</span>
              <input  name="trafficMax"
                      placeholder="To"
                      class="filters-list-li-input-box"
                      x-mask:dynamic="$input.startsWith('0') ? '9' : '999999999'" 
                      x-model="$store.filters.traffic.max"/>
            </label>
          </div>
        </div>


        {{-- ************* --}}
        {{-- Submit Button --}}
        <div class="filter-submit-button-wrapper">
          <button x-ref="applybutton" 
                  class="filter-submit-button" 
                  @click.prevent="dataUpdate">
                  Apply
          </button>
        </div>

      </div>
    </div>

  </div>
</div>
