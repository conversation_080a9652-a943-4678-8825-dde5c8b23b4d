{{-- 1. DR - Min/Max - (Single Filter) --}}
{{-- Alpine: open/close, data update, reset filters --}}
<div class="single-filter">

  <div x-data="{isOpen: false, 
                

                {{-- ****************** --}}
                {{-- Update Data Action --}}
                dataUpdate(){
                  
                  if($store.filters.dr.min || $store.filters.dr.max){

                    {{-- if one value is not set --}}
                    if(!$store.filters.dr.min){ $store.filters.dr.min = 0}
                    if(!$store.filters.dr.max){ $store.filters.dr.max = 100}

                    this.numberValueOrderSet(),

                    this.drLabel = $store.filters.dr.min + ' - ' + $store.filters.dr.max,
                    $store.filters.filtersActive = true,
                    $store.filters.dr.active = true,
                    this.isOpen=false

                  }else{
                    this.isOpen=false
                  }
                },


                {{-- *********** --}}
                {{-- Rest Filter --}}
                resetFilter(){
                  this.drLabel=false,
                  this.isOpen=false,
                  $store.filters.dr.min=null,
                  $store.filters.dr.max=null,
                  $store.filters.dr.active=false
                },


                {{-- *********** --}}
                {{-- Validate DR --}}
                drValidate($input){

                  if($input.startsWith('0')){return '0'}
                  if($input.startsWith('100')){return '100'}
                  
                  return '99'
                },


                {{-- ***************** --}}
                {{-- Min/Max Order Set --}}
                numberValueOrderSet(){

                   min = +$store.filters.dr.min
                   max = +$store.filters.dr.max

                  if(min > max){
                    $store.filters.dr.min = $store.filters.dr.max,
                    $store.filters.dr.max = min
                  }
                }

              }">



    {{-- ****************** --}}
    {{-- Main Visual Button --}}
    <button x-on:click="isOpen = !isOpen"
            type="button"
            :class="$store.filters.dr.active ? 'filters-dropdown-active' : ''"
            class="filters-dropdown group" >

      <div class="filters-dropdown-main-label" >
        <x-icons.lucide.sparkles class="filter-label-icon" />
        <div id="label"     class="filter-name">DR</div>
        <div id="data-text" class="filter-data-label" x-cloak x-show="drLabel" x-text="drLabel"></div>
      </div>

      <span class="transition" :class="{ 'rotate-180': isOpen }">
        <x-icons.etc.down-filled class="filter-dropdown-icon" />
      </span>
    </button>



    {{-- *********** --}}
    {{-- Dialog Open --}}
    <div  x-cloak
          x-trap="isOpen"
          x-transition
          x-show="isOpen"
          x-on:click.away="isOpen = false"
          x-on:keydown.escape.window="isOpen = false"
          class="dialog-preview">

      <div class="filters-dropdown-div">


        {{-- ************ --}}
        {{-- Dialog Label --}}
        <header class="filters-dropdown-header">
          <span class="filters-dropdown-selection-details">
            Domain Rating
          </span>
          <button 
            type="button"
            class="reset-text-filters"
            @click="resetFilter">
            Reset
          </button>
        </header>


        {{-- ********** --}}
        {{-- input data --}}
        <div class="filter-input-top" @keydown.enter.prevent="dataUpdate">

          <div class="filter-data-wrapper">
            <label class="filters-list-label">

              <input type="hidden" name="drFilter" x-model="$store.filters.dr.active">
              <span class="filters-list-label-data">Min</span>
              <input  name="drMin" 
                      x-model="$store.filters.dr.min"
                      {{-- x-mask:dynamic="$input.startsWith('0') ? '0' : '99'"  --}}
                      x-mask:dynamic="drValidate($input)"
                      placeholder="From" 
                      class="filters-list-li-input-box"/>
            </label>

            <label class="filters-list-label">
              <span class="filters-list-label-data">Max</span>
              <input  
                      name="drMax"
                      x-model="$store.filters.dr.max"
                      x-mask:dynamic="drValidate($input)" 
                      placeholder="To" 
                      class="filters-list-li-input-box" />
            </label>
          </div>

        </div>


        {{-- ****** --}}
        {{-- Submit --}}
        <div class="filter-submit-button-wrapper">
          <button class="filter-submit-button" @click.prevent="dataUpdate">
            Apply
          </button>
        </div>

      </div>
    </div>

  </div>
</div>

