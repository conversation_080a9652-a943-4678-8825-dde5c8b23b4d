{{-- 4. Category - Dropdown - Filter --}}
<div class="single-filter">

  {{-- Variables and functionality for filter --}}
  <div x-data="{isOpen: false,
                searchText: '',
                selectedCount: 0,


                {{-- ********* --}}
                {{-- Data List --}}
                list: {{ json_encode($categories) }}, 



                {{-- *********** --}}
                {{-- DATA UPDATE --}}
                {{-- selected state update + data variables --}}
                {{-- if none selected then reset --}}
                dataUpdate(){
                  this.selectedCount = $store.filters.category.selected.length,
                  this.categoryLabel = this.selectedCount + ' Selected',
                  $store.filters.filtersActive = true,
                  $store.filters.category.active=true

                  if(this.selectedCount < 1){
                    this.resetFilter()
                  }
                },


                {{-- ************ --}}
                {{-- Reset Filter --}}
                {{-- Selected state reset, data variables update --}}
                resetFilter(){

                  $store.filters.category.selected = [],
                  $store.filters.category.active=false,

                  this.unCheckAll(),

                  this.categoryLabel=false,
                  this.isOpen=false,

                  this.selectedCount = $store.filters.category.selected.length
                },

                
                {{-- *********** --}}
                {{-- SEARCH LIST --}}
                {{-- Used for searching list items --}}
                get theList() {

                    if (this.searchText === '') {
                      return this.list;
                    }

                    return this.list.filter((item) => {
                      return item.category
                        .toLowerCase()
                        .includes(this.searchText.toLowerCase());
                    })
                },


                {{-- ****************** --}}
                {{-- CATEGORY SELECTION --}}
                {{-- if already exist in selected then remove --}}
                {{-- if not selected then add to list + update other variable --}}
                categoryUpdate(id){

                  if($store.filters.category.selected.includes(id)){

                    index = $store.filters.category.selected.indexOf(id);
                    if (index !== -1) {
                      $store.filters.category.selected.splice(index, 1);
                    }

                  }else{
                    $store.filters.category.selected.push(id)
                  }

                  this.dataUpdate()
                },


                {{-- ***************** --}}
                {{-- Uncheck All Items --}}
                unCheckAll() {
                  let inputs = document.querySelectorAll('#checkbox-category')
                  for (let i = 0; i < inputs.length; i++) {
                      inputs[i].checked = false;
                  }
                } 

             }">



    {{-- ********************** --}}
    {{-- Category Visual Button --}}
    <button x-on:click="isOpen = !isOpen"
            type="button"
            :class="$store.filters.category.active ? 'filters-dropdown-active' : ''"
            class="filters-dropdown group">

      <div class="filters-dropdown-main-label">
        <x-icons.lucide.layout-list class="filter-label-icon" />
        <div id="label"     class="filter-name">Category</div>
        <div id="data-text" class="filter-data-label" x-cloak x-show="categoryLabel" 
              x-text="categoryLabel">All</div>
      </div>

      <span class="transition" :class="{ 'rotate-180': isOpen }">
        <x-icons.etc.down-filled class="filter-dropdown-icon" />
      </span>
    </button>



    {{-- ***************** --}}
    {{-- Category Dropdown --}}
    <div  x-cloak
          x-trap="isOpen"
          x-transition
          x-show="isOpen"
          x-on:click.away="isOpen = false"
          x-on:keydown.escape.window="isOpen = false"
          class="dialog-preview">

      <div class="filters-dropdown-div">

        <header class="filters-dropdown-header">
          <span class="filters-dropdown-selection-details">
            <span x-text="selectedCount">0</span>
            Selected
          </span>
          <button type="button" @click="resetFilter()"
            class="reset-text-filters">
            Reset
          </button>
        </header>


        {{-- Search --}}
        <div class="search">
          <input  class="dialog-serch-bar" 
                  placeholder="Search..." 
                  type="text" 
                  name="search" 
                  x-model="searchText">
        </div>



        {{-- ******************* --}}
        {{-- Dropdown list items --}}
        <ul class="filters-list-ul">
           <template x-for="item in theList" :key="item.id">
            <li>
              <label class="filters-list-label">
                <input  type="checkbox"
                        id="checkbox-category"
                        name="category" 
                        :value="item.id" 
                        class="filters-list-li-input"
                        @click="categoryUpdate(item.id)" 
                        {{-- x-bind:checked="item.active" --}}
                        />
                <span class="filters-list-label-data" x-text="item.category"></span>
              </label>
            </li>
           </template>
        </ul>

      </div>
    </div>

  </div>
</div>