<div id="language-filter" class="single-filter relative">

      {{-- Variables and functionality for filter --}}
      <div x-data="{isOpen: false,
                    searchText: '',
                    selectedCount: 0,


                    {{-- ********* --}}
                    {{-- Data List --}}
                    list: {{ json_encode($languages) }},


                    {{-- ********** --}}
                    {{-- Data Update --}}
                    {{-- selected state update + data variables --}}
                    {{-- if none selected then reset --}}
                    dataUpdate(){
                      this.selectedCount = $store.filters.language.selected.length,
                      this.languageLabel = this.selectedCount + ' Selected',
                      $store.filters.filtersActive  = true,
                      $store.filters.language.active= true

                      if(this.selectedCount < 1){
                        this.resetFilter()
                      }
                    },


                    {{-- ************ --}}
                    {{-- Reset Filter --}}
                    {{-- Selected state reset, data variables update --}}
                    resetFilter(){

                      $store.filters.language.selected = [],
                      $store.filters.language.active=false,

                      this.unCheckAll(),

                      this.languageLabel=false,
                      this.isOpen=false,

                      this.selectedCount = $store.filters.language.selected.length
                    },

                    
                    {{-- ****** --}}
                    {{-- SEARCH --}}
                    {{-- Used for searching list items --}}
                    get theList() {

                        if (this.searchText === '') {
                          return this.list;
                        }

                        return this.list.filter((item) => {
                          return item.name
                            .toLowerCase()
                            .includes(this.searchText.toLowerCase());
                        })
                    },


                    {{-- ******************** --}}
                    {{-- Categories Selection --}}
                    {{-- if already exist in selected then remove
                    if not selected then add to list + update other variable --}}
                    languageUpdate(id){

                      if($store.filters.language.selected.includes(id)){

                        index = $store.filters.language.selected.indexOf(id);
                        if (index !== -1) {
                          $store.filters.language.selected.splice(index, 1);
                        }

                      }else{
                        $store.filters.language.selected.push(id)
                      }

                      this.dataUpdate()
                    },


                    {{-- *********************** --}}
                    {{-- Uncheck all items in Ui --}}
                    unCheckAll() {
                      let inputs = document.querySelectorAll('#checkbox-language')
                      for (let i = 0; i < inputs.length; i++) {
                          inputs[i].checked = false;
                      }
                    } 

                 }">


        

        {{-- ********************** --}}
        {{-- Language Visual Button --}}
        <button x-on:click="isOpen = !isOpen"
                type="button"
                :class="$store.filters.language.active ? 'filters-dropdown-active' : ''"
                class="filters-dropdown group">

          <div class="filters-dropdown-main-label">
            <x-icons.lucide.language class="filter-label-icon"/>
            <div id="label"     class="filter-name">Language</div>
            <div id="data-text" class="filter-data-label"  x-cloak x-show="languageLabel" 
                 x-text="languageLabel"></div>
          </div>

          <span class="transition" :class="{ 'rotate-180': isOpen }">
            <x-icons.etc.down-filled class="filter-dropdown-icon" />
          </span>
        </button>


        {{-- ************** --}}
        {{-- Dialog preview --}}
        <div  x-cloak
              x-trap="isOpen"
              x-transition
              x-show="isOpen"
              x-on:click.away="isOpen = false"
              x-on:keydown.escape.window="isOpen = false"
              class="dialog-preview">

          <div class="filters-dropdown-div">

            <header class="filters-dropdown-header">
              <span class="filters-dropdown-selection-details">
                <span x-text="selectedCount">0</span>
                Selected
              </span>
              <button type="button" @click="resetFilter()"
                class="reset-text-filters">
                Reset
              </button>
            </header>


            {{-- ****** --}}
            {{-- Search --}}
            <div class="search">
              <input  class="dialog-serch-bar" 
                      placeholder="Search..." 
                      type="text" 
                      name="search" 
                      x-model="searchText">
            </div>


            {{-- ******************* --}}
            {{-- Dropdown list items --}}
            <ul class="filters-list-ul">
               <template x-for="item in theList" :key="item.id">
                <li>
                  <label class="filters-list-label">
                    <input  type="checkbox"
                            id="checkbox-language"
                            name="language" 
                            :value="item.id" 
                            class="filters-list-li-input"
                            @click="languageUpdate(item.id)" 
                            {{-- x-bind:checked="item.active" --}}
                            />
                    <span class="filters-list-label-data" x-text="item.name"></span>
                  </label>
                </li>
               </template>
            </ul>

          </div>
        </div>

      </div>
    </div>