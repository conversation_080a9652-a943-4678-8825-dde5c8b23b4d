{{-- 7. spamScore - Min/Max - (Single Filter) --}}
{{-- Alpine: open/close, data update, reset filters --}}
<div class="single-filter relative">

  <div x-data="{isOpen:false,

                {{-- *********** --}}
                {{-- Data Update --}}
                dataUpdate(){
                  if($store.filters.spamScore.min || $store.filters.spamScore.max){

                    {{-- if one value is not set --}}
                    if(!$store.filters.spamScore.min){$store.filters.spamScore.min = 0}
                    if(!$store.filters.spamScore.max){$store.filters.spamScore.max = 100}

                    this.numberValueOrderSet()

                    this.spamScoreLabel = 
                                      $store.filters.spamScore.min +
                                      ' - ' + 
                                      $store.filters.spamScore.max

                    this.isOpen=false
                    $store.filters.filtersActive=true
                    $store.filters.spamScore.active=true

                  }else{
                    this.isOpen=false
                  }
                },


                {{-- ************ --}}
                {{-- Reset Filter --}}
                resetFilter(){
                  $store.filters.spamScore.min=null,
                  $store.filters.spamScore.max=null,
                  $store.filters.spamScore.active=false,
                  this.spamScoreLabel=false,
                  this.isOpen=false
                },


                {{-- ********************** --}}
                {{-- Number Order Value Set --}}
                numberValueOrderSet(){

                  min = +$store.filters.spamScore.min
                  max = +$store.filters.spamScore.max

                  if(min > max){
                    $store.filters.spamScore.min = $store.filters.spamScore.max
                    $store.filters.spamScore.max = min
                  }
              },

            }">



    {{-- ********************* --}}
    {{-- ********************* --}}
    {{-- Main Visual Ui Button --}}
    <button
      x-on:click="isOpen = !isOpen"
      type="button"
      :class="$store.filters.spamScore.active ? 'filters-dropdown-active' : ''"
      class="filters-dropdown group" >

      <div class="filters-dropdown-main-label">
        <x-icons.lucide.shield-minus class="filter-label-icon"/>
        <div id="label"     class="filter-name">Spam Score</div>
        <div id="data-text" class="filter-data-label" x-cloak x-show="spamScoreLabel"  x-text="spamScoreLabel"></div>
      </div>

      <span class="transition" :class="{ 'rotate-180': isOpen }">
        <x-icons.etc.down-filled class="filter-dropdown-icon" />
      </span>
    </button>


    {{-- ************** --}}
    {{-- Dialog preview --}}
    <div  x-cloak
          x-trap="isOpen"
          x-transition
          x-show="isOpen"
          x-on:click.away="isOpen = false"
          x-on:keydown.escape.window="isOpen = false"
          class="dialog-preview">

      <div class="filters-dropdown-div">

        <header class="filters-dropdown-header">
          <span class="filters-dropdown-selection-details">
            Spam Score Range
          </span>
          <button 
            type="button"
            class="reset-text-filters"
            @click="resetFilter">
            Reset
          </button>
        </header>


        {{-- ********** --}}
        {{-- input data --}}
        <div class="filter-input-top" @keydown.enter.prevent="dataUpdate">

          <div class="filter-data-wrapper">

            <label  class="filters-list-label">
              <span  class="filters-list-label-data">Min:</span>
              <input  name="spamScoreMin"
                      class="filters-list-li-input-box"
                      x-mask:dynamic="$input.startsWith('0') ? '0' : '99'" 
                      x-model="$store.filters.spamScore.min"/>
            </label>

            <label  class="filters-list-label">
              <span  class="filters-list-label-data">Max:</span>
              <input name="spamScoreMax"
                     class="filters-list-li-input-box"
                     x-mask:dynamic="$input.startsWith('0') ? '0' : '99'"  
                     x-model="$store.filters.spamScore.max"/>
            </label>
          </div>

        </div>


        {{-- ************* --}}
        {{-- Submit Button --}}
        <div class="filter-submit-button-wrapper">
          <button  x-ref="applybutton" 
                  class="filter-submit-button" 
                  @click.prevent="dataUpdate">
                    Apply
          </button>
        </div>

      </div>
    </div>

  </div>
</div>
