{{-- 3. Price - Min/Max - (Single Filter) --}}
{{-- Alpine: open/close, data update, reset filters --}}
<div class="single-filter">
  <div x-data="{isOpen:false,

                {{-- *********** --}}
                {{-- Data Update --}}
                dataUpdate(){
                  if($store.filters.price.min || $store.filters.price.max){

                    {{-- if one value is not set --}}
                    if(!$store.filters.price.min){$store.filters.price.min = 0}
                    if(!$store.filters.price.max){$store.filters.price.max = 10000}

                    this.numberValueOrderSet()

                    this.priceLabel = '$' +
                                      Intl.NumberFormat().format($store.filters.price.min) +
                                      ' - ' + '$' + 
                                      Intl.NumberFormat().format($store.filters.price.max)

                    this.isOpen=false
                    $store.filters.filtersActive=true
                    $store.filters.price.active=true

                  }else{
                    this.isOpen=false
                  }
                },


                {{-- ************ --}}
                {{-- Reset Filter --}}
                resetFilter(){
                  $store.filters.price.min=null,
                  $store.filters.price.max=null,
                  $store.filters.price.active=false,
                  this.priceLabel=false,
                  this.isOpen=false
                },


                {{-- ********************** --}}
                {{-- Number Value Order Set --}}
                numberValueOrderSet(){

                  min = +$store.filters.price.min
                  max = +$store.filters.price.max

                  if(min > max){
                    $store.filters.price.min = $store.filters.price.max
                    $store.filters.price.max = min
                  }
              },

            }">



    {{-- *********** --}}
    {{-- Main Visual Button --}}
    <button
      x-on:click="isOpen = !isOpen"
      type="button"
      :class="$store.filters.price.active ? 'filters-dropdown-active' : ''"
      class="filters-dropdown group" >

      <div class="filters-dropdown-main-label">
        <x-icons.lucide.dollar class="filter-label-icon" />
        <div id="label"     class="filter-name">Price</div>
        <div id="data-text" class="filter-data-label" x-cloak x-show="priceLabel" x-text="priceLabel">All</div>
      </div>

      <span class="transition" :class="{ 'rotate-180': isOpen }">
        <x-icons.etc.down-filled class="filter-dropdown-icon" />
      </span>
    </button>



    {{-- ************** --}}
    {{-- Dialog preview --}}
    <div  x-cloak
          x-trap="isOpen"
          x-transition
          x-show="isOpen"
          x-on:click.away="isOpen = false"
          x-on:keydown.escape.window="isOpen = false"
          class="dialog-preview">

      <div class="filters-dropdown-div">

        <header class="filters-dropdown-header">
          <span class="filters-dropdown-selection-details">
            Price Range
          </span>
          <button 
            type="button"
            class="reset-text-filters"
            @click="resetFilter">
            Reset
          </button>
        </header>


        {{-- ********** --}}
        {{-- input data --}}
        <div class="filter-input-top" @keydown.enter.prevent="dataUpdate">

          <div class="filter-data-wrapper">

            <label   class="filters-list-label">
              <span  class="filters-list-label-data mr-2">Min</span>
              <span  class="relative text-gray-400 -mr-7">$</span>
              <input  name="priceMin"
                      class="filters-list-li-input-box"
                      style="padding-left: 1.5rem;"
                      x-mask:dynamic="$input.startsWith('0') ? '9' : '99999'" 
                      x-model="$store.filters.price.min"/>
            </label>

            <label   class="filters-list-label">
              <span  class="filters-list-label-data mr-2">Max</span>
              <span  class="relative text-gray-400 -mr-7">$</span>
              <input name="priceMax"
                     class="filters-list-li-input-box"
                     style="padding-left: 1.5rem;"
                     x-mask:dynamic="$input.startsWith('0') ? '0' : '99999'"  
                     x-model="$store.filters.price.max"/>
            </label>
          </div>

        </div>


        {{-- ************* --}}
        {{-- Submit Button --}}
        <div class="filter-submit-button-wrapper">
          <button x-ref="applybutton" 
                  class="filter-submit-button" 
                  @click.prevent="dataUpdate">
                    Apply
          </button>
        </div>

      </div>
    </div>

  </div>
</div>
