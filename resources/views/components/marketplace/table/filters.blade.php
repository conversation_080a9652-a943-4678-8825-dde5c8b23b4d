{{-- *********************** --}}
{{--          Filters        --}}
{{-- Data stored in AlpineJS --}}
{{-- store @ marketplace.js  --}}
{{-- *********************** --}}


<div id="filters-section" class="border shadow px-4 py-2 rounded-lg bg-white mb-6">

  {{-- Alpine Main Component --}}
  {{-- 1. Registering All filters data like label and values --}}
  {{-- 2. Updating Frontend Page State when page is loaded based on url --}}
  <div  class="filters-list flex w-full gap-3 md:gap-4 my-4 pb-2 md:pb-0 flex-wrap" 
        x-data="{

                {{-- defailt labels --}}
                drLabel:        false,
                trafficLabel:   false,
                priceLabel:     false,
                categoryLabel:  false,
                languageLabel:  false,
                countryLabel:   false,
                spamScoreLabel: false,


                {{-- reset filters labels --}}
                resetLabels(){
                  this.drLabel       = false,
                  this.trafficLabel  = false,
                  this.priceLabel    = false,
                  this.categoryLabel = false,
                  this.languageLabel = false,
                  this.countryLabel  = false,
                  this.spamScoreLabel= false
                },


                {{-- Reset Filter Button Handle --}}
                resetAllFilters(){
                  this.resetLabels(),
                  resetStoreFilters(),

                  {{-- Clear all checkbox states in dropdown filters --}}
                  this.clearAllCheckboxes(),

                  updateTable('homepage')
                  {{-- reload table --}}
                },



                {{-- Clear all checkbox states for dropdown filters --}}
                clearAllCheckboxes(){
                  {{-- Clear category checkboxes --}}
                  let categoryInputs = document.querySelectorAll('#checkbox-category')
                  for (let i = 0; i < categoryInputs.length; i++) {
                      categoryInputs[i].checked = false;
                  }

                  {{-- Clear country checkboxes --}}
                  let countryInputs = document.querySelectorAll('#checkbox-country')
                  for (let i = 0; i < countryInputs.length; i++) {
                      countryInputs[i].checked = false;
                  }

                  {{-- Clear language checkboxes --}}
                  let languageInputs = document.querySelectorAll('#checkbox-language')
                  for (let i = 0; i < languageInputs.length; i++) {
                      languageInputs[i].checked = false;
                  }
                },

        }">



    {{-- 1. DR Filter --}}
    <x-marketplace.table.filters.dr />


    {{-- Traffic --}}
    <x-marketplace.table.filters.traffic />


    {{-- 3. Price - Min/Max - (Single Filter) --}}
    <x-marketplace.table.filters.price />


    {{-- 4. Categories --}}
    <x-marketplace.table.filters.category :categories="$filtersData['categories']" />


    {{-- 5. language - Dropdown - Filter --}}
    <x-marketplace.table.filters.language :languages="$filtersData['languages']" />
    

    {{-- 6. Country --}}
    <x-marketplace.table.filters.country :countries="$filtersData['countries']" />


    {{-- 7. Spam Score --}}
    <x-marketplace.table.filters.spam-score />
    





    

    {{-- Filters Action Buttons --}}
    <div class="md:ml-auto flex">
    
      {{-- Apply Filters --}}
      <div 
          x-show="$store.filters.filtersActive"
          x-cloak  
          class="mr-4 x-transition">
        <input @click.prevent="updateTable()" class="text-sm cursor-pointer px-3 py-1 font-semibold flex items-center rounded-lg border-2 border-emerald-700 shadow-sm bg-emerald-700 text-white hover:bg-white hover:text-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-700 focus:ring-offset-2 group whitespace-nowrap transition duration-150 ease-in-out" type="submit" value="Show Results">
      </div>
        

      {{-- Reset Filters --}}
      <div
          {{-- x-show="$store.filters.filtersActive"  --}}
          class="x-transition">
          <button @click.prevent="resetAllFilters()" class="text-sm cursor-pointer px-3 py-1 font-semibold flex items-center rounded-lg border-2  shadow-sm bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 group whitespace-nowrap transition duration-150 ease-in-out" type="button">
            Reset Filters
          </button>
      </div>

    </div>


  </div>

</div>





{{-- Filters Button Vertical --}}
{{-- <div class="space-y-2">
  <div
    x-data="{ isOpen: false }"
    class="overflow-hidden rounded border border-gray-300"
    >
    <button
      x-on:click="isOpen = !isOpen"
      type="button"
      class="flex w-full cursor-pointer items-center justify-between gap-2 bg-white p-4 text-gray-900 transition"
    >
      <span class="text-sm font-medium"> Availability </span>

      <span class="transition" :class="{ '-rotate-180': isOpen }">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="h-4 w-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M19.5 8.25l-7.5 7.5-7.5-7.5"
          />
        </svg>
      </span>
    </button>

    <div
      x-cloak
      x-show="isOpen"
      x-on:keydown.escape.window="isOpen = false"
      class="border-t border-gray-200 bg-white"
    >
      <header class="flex items-center justify-between p-4">
        <span class="text-sm text-gray-700"> 0 Selected </span>

        <button
          type="button"
          class="text-sm text-gray-900 underline underline-offset-4"
        >
          Reset
        </button>
      </header>

      <ul class="space-y-1 border-t border-gray-200 p-4">
        <li>
          <label for="FilterInStock" class="inline-flex items-center gap-2">
            <input
              type="checkbox"
              id="FilterInStock"
              class="h-5 w-5 rounded border-gray-300"
            />

            <span class="text-sm font-medium text-gray-700">
              In Stock (5+)
            </span>
          </label>
        </li>

        <li>
          <label for="FilterPreOrder" class="inline-flex items-center gap-2">
            <input
              type="checkbox"
              id="FilterPreOrder"
              class="h-5 w-5 rounded border-gray-300"
            />

            <span class="text-sm font-medium text-gray-700">
              Pre Order (3+)
            </span>
          </label>
        </li>

        <li>
          <label for="FilterOutOfStock" class="inline-flex items-center gap-2">
            <input
              type="checkbox"
              id="FilterOutOfStock"
              class="h-5 w-5 rounded border-gray-300"
            />

            <span class="text-sm font-medium text-gray-700">
              Out of Stock (10+)
            </span>
          </label>
        </li>
      </ul>
    </div>
  </div>
</div>

 --}}


   
