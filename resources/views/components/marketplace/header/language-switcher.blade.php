@php
$languages = \App\Http\Controllers\MarketplaceMetaDataController::languagesList()->filter(function ($language) {
return $language->code === 'en' || $language->code === 'es';
});
$currentLocale = app()->getLocale();
@endphp

<div x-data="{ isOpen: false }" class="relative">
    <button @click="isOpen = !isOpen" class="p-3 hover:bg-gray-100 rounded-full cursor-pointer flex items-center gap-2">
        <x-icons.lucide.language class="w-5 h-5" />
        <span class="hidden md:inline-block text-sm font-medium">{{ strtoupper($currentLocale) }}</span>
    </button>

    <div x-show="isOpen" @click.away="isOpen = false" x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95"
        class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
        <div class="py-1">
            @foreach($languages as $language)
            <a href="{{ route('language.switch', $language->code) }}"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ $language->code === $currentLocale ? 'bg-gray-50' : '' }}">
                {{ $language->name }}
            </a>
            @endforeach
        </div>
    </div>
</div>