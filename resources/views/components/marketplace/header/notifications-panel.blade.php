{{-- Actively used --}}
<div x-data="{visible: false}" id="">


    {{-- Icons --}}
    <div id="notification-icon-top-menu" @click="visible = !visible" :class="!visible || 'bg-gray-100'"
        class="p-3 hover:bg-gray-100 rounded-full cursor-pointer">
        <x-icons.etc.notification-bell />
    </div>


    {{-- Notification Panel --}}
    <div x-show="visible" x-cloak id="notifications-panel" @click.away="visible=false"
        x-transition:enter="ease-out duration-200" x-transition:enter-start="-translate-y-2"
        x-transition:enter-end="translate-y-0" class="absolute right-32 z-50 mt-2 min-h-32 max-h-96 overflow-y-scroll w-80 origin-top-right border rounded-md 
                    bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none items-center"
        role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">


        {{-- Get Notifications Data --}}
        @php
        $notifications = auth()->user()->notifications;
        @endphp


        <div class="text-center font-bold border-b p-2 text-xs text-gray-500 uppercase tracking-wide">
            Notifications
        </div>

        {{-- Notifications List --}}
        @if(count($notifications) > 0)

        <div class="flex flex-col items-center justify-center content-center">

            {{-- Single Notification item --}}
            @foreach($notifications as $notification)
            @php
            if(isset($notification->data['order_id'])){
            $order_item_id =
            \App\Models\MarketplaceSingleOrderItem::with('order')->find($notification->data['order_id']);
            }
            if(isset($notification->data['data']['is_open_chat'])){
            $href = isset($order_item_id)
            ? route('advertiser.order-details', $order_item_id->order->id).'?openChat=true'
            : '#';
            }else{
            $href = isset($order_item_id)
            ? route('advertiser.order-details', $order_item_id->order->id)
            : '#';
            }
            @endphp
            <a href="{{ $href }}" wire:navigate
                class="flex flex-col text-sm space-y-1 border-b px-5 py-4 hover:bg-gray-100">
                <div class="text-sm font-bold text-gray-600">
                    {{ $notification->data['title'] ?? '' }}
                </div>
                <div class="text-xs text-gray-600">
                    {{ $notification->data['message'] ?? '' }}
                </div>
                <div class="text-xs text-gray-400 text-right">
                    {{ $notification->created_at->diffForHumans() }}
                </div>
            </a>

            @php
            if($loop->iteration > 20){
            break;
            }
            @endphp

            @endforeach

        </div>

        {{-- Empty State: No Notifications --}}
        @else
        <div class="flex flex-col items-center justify-center content-center p-4">
            <x-icons.etc.drawer class="w-3 h-3 stroke-gray-500" />
            <span class="text-gray-500 p-2">No Notifications</span>
        </div>
        @endif

    </div>

</div>