@props([
'item' => null,
])

@php
$stateLabel = $item->state_label ?? 'Pending';
$color = $item->badge_color ?? 'orange';
$icon = $item->badge_icon ?? 'triangle-alert';
@endphp

<span
  class="inline-flex items-center gap-1.5 py-1 px-3 rounded-full text-xs lg:text-sm
              font-medium bg-{{ $color }}-100 text-{{ $color }}-800 dark:bg-{{ $color }}-900 dark:text-{{ $color }}-200">
  @if($icon)
  @component("components.icons.lucide.{$icon}", ['class' => 'w-4 h-4 stroke-2'])
  @endcomponent
  @endif
  {{ $stateLabel }}
</span>