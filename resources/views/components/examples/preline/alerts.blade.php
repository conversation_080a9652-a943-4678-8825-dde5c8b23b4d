{{-- <PERSON>erts Multi --}}
{{-- https://preline.co/docs/alerts.html --}}




{{-- Warning --}}
<div class="bg-yellow-50 border border-yellow-200 rounded-md p-4" role="alert">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-4 w-4 text-yellow-400 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
      </svg>
    </div>
    <div class="ml-4">
      <h3 class="text-sm text-yellow-800 font-semibold">
        Cannot connect to the database
      </h3>
      <div class="mt-1 text-sm text-yellow-700">
        We are unable to save any progress at this time.
      </div>
    </div>
  </div>
</div>


{{-- Error / Problem --}}
<div class="bg-red-50 border border-red-200 rounded-md p-4" role="alert">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-4 w-4 text-red-400 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
      </svg>
    </div>
    <div class="ml-4">
      <h3 class="text-sm text-red-800 font-semibold">
        A problem has been occurred while submitting your data.
      </h3>
      <div class="mt-2 text-sm text-red-700">
        <ul class="list-disc space-y-1 pl-5">
          <li>
            This username is already in use
          </li>
          <li>
            Email field can't be empty
          </li>
          <li>
            Please enter a valid phone number
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>


{{-- Actions --}}
<div class="bg-blue-50 border border-blue-200 rounded-md p-4" role="alert">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-4 w-4 text-blue-600 mt-1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
      </svg>
    </div>
    <div class="ml-4">
      <h3 class="text-gray-800 font-semibold">
        YouTube would like you to send notifications
      </h3>
      <div class="mt-2 text-sm text-gray-600">
        Notifications may include alerts, sounds and icon badges. These can be configured in Settings.
      </div>
      <div class="mt-4">
        <div class="flex space-x-3">
          <button type="button" class="inline-flex justify-center items-center gap-2 rounded-md border border-transparent font-medium text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all text-sm">
            Don't allow
          </button>
          <button type="button" class="inline-flex justify-center items-center gap-2 rounded-md border border-transparent font-medium text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all text-sm">
            Allow
          </button>
        </div>
      </div>
    </div>
  </div>
</div>


{{-- Info light with right link --}}
<div class="bg-gray-50 border border-gray-200 rounded-md p-4" role="alert">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-4 w-4 text-gray-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
      </svg>
    </div>
    <div class="flex-1 md:flex md:justify-between ml-4">
      <p class="text-sm text-gray-700">
        A new software update is available. See what's new in version 3.0.7
      </p>
      <p class="text-sm mt-3 md:mt-0 md:ml-6">
        <a class="text-gray-700 hover:text-gray-500 font-medium whitespace-nowrap" href="#">Details</a>
      </p>
    </div>
  </div>
</div>


{{-- Highlighted Info with Box Shadow - For FeaturesDiscovery --}}
<div class="bg-white border rounded-md shadow-lg p-4 dark:bg-gray-800 dark:border-gray-700" role="alert">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-4 w-4 text-blue-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
      </svg>
    </div>
    <div class="ml-4">
      <h3 class="text-gray-800 font-semibold dark:text-white">
        New version published
      </h3>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-400">
        Chris Lynch published a new version of this page. Refresh to see the changes.
      </p>
    </div>
  </div>
</div>


{{-- Success Alert - Green --}}

<div class="bg-green-50 border border-green-200 text-sm text-green-600 rounded-md p-4" role="alert">
  <span class="font-bold">Success</span> alert! You should check in on some of those fields below.
</div>