<div x-data="{
        progress: 0,
        progressInterval: null,
    }"
    x-init="
        progressInterval = setInterval(() => {
            progress = progress + 1;
            if (progress >= 100) {
                clearInterval(progressInterval);
            }
        }, 100);
    "
    class="relative w-full h-3 overflow-hidden rounded-full bg-neutral-100">
    <span :style="'width:' + progress + '%'" class="absolute w-24 h-full duration-300 ease-linear bg-neutral-900" x-cloak></span>
</div>