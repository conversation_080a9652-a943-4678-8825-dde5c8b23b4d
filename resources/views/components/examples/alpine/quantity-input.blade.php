<div x-data="{ productQuantity: 1 }">
  
  <label for="Quantity" class="sr-only">Quantity</label>

  <div class="flex items-center justify-center">

    {{-- Decrease --}}
    <button type="button" x-on:click="productQuantity--"
        :disabled="productQuantity === 0" class="rounded-full opacity-90 hover:opacity-100 bg-slate-400 p-[4.2px] text-white focus:bg-red-600 hover:bg-red-600  focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-3 w-3"><path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
    </button>



    {{-- Quantity --}}
    <div class="px-1 text-center text-zinc-700 font-semibold w-7 text-sm ">
      <span
        id="Quantity"
        name="quanity"
        x-model="productQuantity"
        x-text="productQuantity">
        </span>
    </div>



    {{-- Increase --}}
    <button type="button" x-on:click="productQuantity++" class="rounded-full opacity-90 hover:opacity-100 bg-slate-400 p-[4.2px] text-white hover:bg-emerald-600 focus:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-3 w-3"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
    </button>

  </div>

</div>


