<div x-data="{
    radioGroupSelectedValue: null,
    radioGroupOptions: [
        {
            title: 'Tailwind CSS',
            description: 'A utility-first CSS framework for rapid UI development.',
            value: 'tailwind'
        },
        {
            title: 'Alpine JS',
            description: 'A rugged and lightweight JavaScript framework.',
            value: 'alpine'
        },
        {
            title: 'Laravel',
            description: 'The PHP Framework for Web Artisans.',
            value: 'laravel'
        }
    ]
}" class="space-y-3">
    <template x-for="(option, index) in radioGroupOptions" :key="index">
        <label @click="radioGroupSelectedValue=option.value" class="flex items-start p-5 space-x-3 bg-white border rounded-md shadow-sm hover:bg-gray-50 border-neutral-200/70">
            <input type="radio" name="radio-group" :value="option.value" class="text-gray-900 translate-y-px focus:ring-gray-700" />
            <span class="relative flex flex-col text-left space-y-1.5 leading-none">
                <span x-text="option.title" class="font-semibold"></span>
                <span x-text="option.description" class="text-sm opacity-50"></span>
            </span>
        </label>
    </template>
</div>