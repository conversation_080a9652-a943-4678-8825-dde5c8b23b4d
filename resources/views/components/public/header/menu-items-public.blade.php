@php
use App\Enums\Role;
@endphp
{{-- <a href="{{route('contact')}}" class="menu-top-public">Contact</a> --}}
@auth
<a href="{{route('marketplace') }}" class="menu-top-public-dash">
  Marketplace
</a>
@if(auth()->user()->isSuperAdmin() || auth()->user()->isAdmin())
<a href="{{route('admin.dashboard') }}" class="menu-top-public-dash">
  Admin Dashboard
</a>
@endif

@if(auth()->user()->role == Role::Outreach->value)
<a href="{{route('admin.dashboard') }}" class="menu-top-public-dash">
  Outreach Dashboard
</a>
@endif

@if(auth()->user()->role == Role::Sales->value)
<a href="{{route('admin.dashboard') }}" class="menu-top-public-dash">
  Sales Dashboard
</a>
@endif

@if(auth()->user()->role == Role::Finance->value)
<a href="{{route('admin.dashboard') }}" class="menu-top-public-dash">
  Finance Dashboard
</a>
@endif

@if(auth()->user()->role == Role::Publisher->value)
<a href="{{route('publisher.dashboard') }}" class="menu-top-public-dash">
  Publisher Dashboard
</a>
@endif
@if(auth()->user()->role == Role::Advertiser->value)
<a href="{{route('advertiser.dashboard') }}" class="menu-top-public-dash">
  Advertiser Dashboard
</a>
@endif
@if(auth()->user()->role == Role::Writer->value)
<a href="{{route('admin.writer.dashboard') }}" class="menu-top-public-dash">
  Writer Dashboard
</a>
@endif
@else
<a href="{{route('login') }}" class="menu-top-public font-medium">
  Login
</a>
<a href="{{route('register') }}"
  class="inline-flex  items-center bg-gradient-to-br from-slate-600 to-slate-700 w-full px-3.5 pt-1.5 pb-2 text-sm font-medium leading-none text-white md:w-auto md:rounded-full hover:from-slate-600 hover:to-slate-900 hover:shadow focus:outline-none md:focus:ring-2 focus:ring-0 focus:ring-offset-2 focus:ring-slate-800">
  Sign up
</a>
@endauth