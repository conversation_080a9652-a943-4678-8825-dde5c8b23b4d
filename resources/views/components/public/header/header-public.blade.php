<section id="public-header" class="w-full mt-4 px-4 antialiased sticky top-0  z-50">

  <div class="mx-auto lg:max-w-8xl ">

    <nav  class="container backdrop-blur-sm bg-white/50 z-50 select-none rounded-full py-2 md:py-3 px-4 
                  border-2 border-slate-200 " 
          x-data="{ showMenu: false }">

        <div class="container relative flex items-center  flex-wrap mx-auto overflow-hidden font-medium md:overflow-visible justify-between px-2 md:px-2 lg:px-0">


            {{-- LOGO --}}
            <div class="flex ">
                <a href="{{route('home')}}" class="flex items-center font-extrabold text-slate-700
                                                   md:py-0 hover:bg-orange-50/50 p-1 rounded-full">
                  <x-icons.etc.bear class="w-6 h-6 mt-0.5 mr-0.5 fill-slate-700 stroke-2" />
                  <span class="title-h text-lg md:text-xl tracking-wider text-slate-700 ">PressBear</span>
                </a>
            </div>



            {{-- Desktop Menu --}}
            <div class="hidden md:flex">
                  <div class="flex flex-col w-full md:items-center content-h md:flex-row md:py-0">
                       <x-public.header.menu-items-public />
                  </div>
            </div>


          {{-- Mobile Menu Button --}}
          <div @click="showMenu = !showMenu" class="absolute right-0 flex flex-col items-end justify-center p-2 bg-white rounded-full cursor-pointer md:hidden hover:bg-slate-100">
              <svg x-show="!showMenu" class="w-6 h-6 text-slate-700" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
              <svg x-show="showMenu" x-cloak class="w-6 h-6 text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
          </div>
          
        </div>



        {{-- Mobile Menu --}}
        <div  x-data 
              class="flex border-2 right-0  mt-4 items-start w-fit flex-col h-auto p-4 
                    text-sm rounded-lg bg-white border-slate-200"
              x-cloak
              :class="{'flex fixed': showMenu, 'hidden': !showMenu }">

              <div class="flex flex-col content-h text-center space-y-2 divide-y divide-slate-100">
                 <x-public.header.menu-items-public />
              </div>
        </div>
    </nav>
</div>

</section>