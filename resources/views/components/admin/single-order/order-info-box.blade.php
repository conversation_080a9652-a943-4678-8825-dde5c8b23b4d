<div id="order-details" class="box-section-wrapper grid grid-cols-3 lg:grid-cols-4 gap-y-6">

  <div class="col-span-3">
    <ul class="order-meta space-y-2 columns-1 md:columns-2 lg:columns-3">

      <li>
        <span class="o-label font-bold text-slate-600">Order id:</span>
        <span class="o-data text-emerald-900 font-medium">#{{$order->id}}</span>
      </li>

      <li>
        <span class="o-label font-bold text-slate-600">Date Ordered:</span>
        <span class="o-data text-emerald-900 font-medium">{{date_format($order->created_at, 'd M Y')}}</span>
      </li>

      <li>
        <span class="o-label font-bold text-slate-600">Time Passed:</span>
        <span class="o-data text-emerald-900 font-medium">{{$order->created_at->diffForHumans()}}</span>
      </li>

      <li>
        <span class="o-label font-bold text-slate-600">Amount Paid:</span>
        <span class="o-data text-emerald-900 font-medium">${{$order->price_paid}}</span>
      </li>

      <li>
        <span class="o-label font-bold text-slate-600">Payment Id:</span>
        <span class="o-data text-emerald-900 font-medium">{{$order->payment_id}}</span>
      </li>

      <li>
        <span class="o-label font-bold text-slate-600">Customer Email:</span>
        <span class="o-data text-emerald-900 font-medium">{{$order->user->email}}</span>
      </li>

      <li>
        <span class="o-label font-bold text-slate-600">Publications:</span>
        <span class="o-data text-emerald-900 font-medium">
          {{ $orderStatus['delivered']['count'] }} /
          {{ $orderStatus['itemsCount']}}
        </span>
      </li>

      <li class="flex">
        <span class="o-label font-bold text-slate-600 mr-1">Order Status:</span>
        @php
        $statusClass = match($order->status) {
        'delivered' => 'bg-green-100 text-green-800',
        'inprogress' => 'bg-yellow-100 text-yellow-800',
        'pending' => 'bg-yellow-100 text-gray-800',
        'cancelled' => 'bg-red-100 text-red-800',
        default => 'bg-gray-100 text-gray-800',
        };
        @endphp

        <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $statusClass }}">
          {{ ucfirst($order->status) }}
        </span>
      </li>


      <li class="flex">
        <span class="o-label font-bold text-slate-600 mr-1">Invoice:</span>
        <span class="o-data text-emerald-900 font-medium items-center flex">
          <a target="_blank" href="{{ route( 'admin-invoice', $order->id) }}">View</a>
        </span>
      </li>

    </ul>
  </div>



  <div class="col-span-3 lg:col-span-1 flex flex-col  items-center justify-center">

    @if($order->order_completed == false)
    <div class="actions flex  ">
      <div wire:click="markCompleted"
        class="button flex border-gray-400 text-gray-500 text-sm font-medium border-2 px-4 py-2 rounded-lg hover:bg-emerald-800 hover:border-emerald-700 hover:text-white cursor-pointer">
        Mark As Completed
      </div>
    </div>
    <div class="text-red-600 text-center text-sm p-2">{{ $updateMessage }}</div>

    @else
    <div class="text-emerald-700 p-2 h-fit w-fit font-bold">√ Order Marked Completed</div>
    @endif

  </div>
</div>