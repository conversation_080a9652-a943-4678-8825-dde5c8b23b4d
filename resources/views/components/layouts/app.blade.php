<!DOCTYPE html>
<html class="scroll-smooth" lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="robots" content="noindex, nofollow" />

    <title>{{ config('app.name', 'Marketplace') }}</title>

    <meta name="robots" content="noindex, nofollow" />
    <link rel="icon" href="{{Storage::url('graphics/bear-smile-svgrepo-com4.png')}}" />
    {{--
    <link rel="icon" type="image/svg+xml" sizes="any" href="{{Storage::url('favicon.svg')}}" /> --}}

    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/marketplace.js'])

    <!-- Livewire / Alpine -->
    @livewireStyles
    @livewireScripts

    {{-- including on all pages as recommended by stripe --}}
    <script src="https://js.stripe.com/v3/"></script>

    {{-- Microsoft Clarity For Analytics --}}
    <x-app.services.clarity-microsoft />

</head>



{{-- APP BODY --}}

<body class="dark:bg-gray-800 max-w-full overflow-x-hidden">

    @auth
    <x-app.header />
    @endauth
    <!-- Page Content -->
    <main class="min-h-screen">
        {{ $slot }}
    </main>

    @stack('modals')

    {{-- Components --}}
    <x-app.footer />
    <x-ui.toast />

</body>


</html>