@component('mail::message')
# Order Item Status Updated

Hello <strong>{{ $data['customerName'] ?? 'N/A' }}</strong>,

We wanted to inform you that the status of your order item has been updated.

@component('mail::panel')
<strong>{{ $data['statusLabel'] ?? 'N/A' }}</strong>
@endcomponent

@component('mail::panel')
Congratulations! Your publication has received a new pitch to collaborate from {{ $data['customerName'] }} Kindly review
the
details and approve the pitch by clicking "Review Pitch Request" below.
@endcomponent

@component('mail::panel')
<strong>Requirements Details:</strong>
- <strong>Article Topic:</strong> {{ $data['requirements']->article_topic ?? 'N/A' }}
- <strong>Anchor Text:</strong> {{ $data['requirements']->anchor_text ?? 'N/A' }}
- <strong>Advertiser URL:</strong> {{ $data['requirements']->advertiser_url ?? 'N/A' }}
@if(isset($data['requirements']->requirement_comments) && $data['requirements']->requirement_comments)
- <strong>Additional Comments:</strong> {{ $data['requirements']->requirement_comments }}
@endif
@endcomponent

Please review the requested revisions at your earliest convenience.
If you have any questions or concerns, feel free to reach out to our support team.


@component('mail::button', ['url' => $data['reviewUrl']])
Review Pitch Request
@endcomponent


<p style="font-size: 0.75rem; color: #6B7280; text-align: center;">
    If you didn't expect this email, please disregard it or contact our support team.
</p>
@endcomponent