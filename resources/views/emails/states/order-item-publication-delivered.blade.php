@component('mail::message')
# Order Item Status Updated

Hello <strong>{{ $data['customerName'] ?? 'N/A' }}</strong>,

We wanted to inform you that the status of your order item has been updated.

@component('mail::panel')
<strong>{{ $data['statusLabel'] ?? 'N/A' }}</strong>
@endcomponent

@component('mail::panel')
Congratulations! Your publication has been published.
@endcomponent

@component('mail::panel')
<strong>Published URL:</strong> {{ $data['published_url'] ?? 'N/A' }}
@endcomponent

Please review the requested revisions at your earliest convenience.
If you have any questions or concerns, feel free to reach out to our support team.


@component('mail::button', ['url' => $data['published_url']])
View Published URL
@endcomponent


<p style="font-size: 0.75rem; color: #6B7280; text-align: center;">
    If you didn't expect this email, please disregard it or contact our support team.
</p>
@endcomponent