@component('mail::message')
# Order Item Status Updated

Hello <strong>{{ $data['customerName'] ?? 'N/A' }}</strong>,

We wanted to inform you that the status of your order item has been updated.

@component('mail::panel')
<strong>{{ $data['statusLabel'] ?? 'N/A' }}</strong>
@endcomponent

@component('mail::panel')
Congratulations! Content has been Approved And Publication Started.
@endcomponent


@component('mail::panel')
<strong>Content Details:</strong>
- <strong>Title:</strong> {{ $data['content']->title ?? 'N/A' }}
- <strong>Content URL:</strong> {{ $data['content']->content_url ?? 'N/A' }}
- <strong>Content:</strong> {{ $data['content']->content_body ?? 'N/A' }}
- <strong>Comments:</strong> {{ $data['content']->comments ?? 'N/A' }}
@endcomponent

@if($data['content']->media->count() > 0)
@component('mail::panel')
<div style="background-color: #F9FAFB; border-radius: 8px; padding: 16px; margin-top: 16px;">
    <h3 style="color: #111827; font-size: 16px; font-weight: 600; margin-bottom: 12px;">Attached Media Files</h3>
    <div style="display: grid; gap: 12px;">
        @foreach($data['content']->media as $media)
        <div
            style="background-color: white; border: 1px solid #E5E7EB; border-radius: 6px; padding: 12px; display: flex; align-items: center; gap: 12px;">
            <div
                style="background-color: #F3F4F6; border-radius: 4px; padding: 8px; min-width: 40px; text-align: center;">
                @if(str_contains($media->mime_type, 'image'))
                <span style="color: #4F46E5;">📷</span>
                @else
                <span style="color: #4F46E5;">📄</span>
                @endif
            </div>
            <div style="flex: 1;">
                <a href="{{ $media->url }}"
                    style="color: #4F46E5; text-decoration: none; font-weight: 500; display: block; margin-bottom: 4px;">{{
                    $media->meta['name'] }}</a>
                <div style="color: #6B7280; font-size: 13px;">
                    {{ number_format($media->meta['size'] / 1024, 2) }} KB • {{
                    Str::upper(pathinfo($media->meta['name'],
                    PATHINFO_EXTENSION)) }}
                </div>
            </div>
            <a href="{{ $media->url }}"
                style="background-color: #EEF2FF; color: #4F46E5; padding: 6px 12px; border-radius: 4px; text-decoration: none; font-size: 13px; font-weight: 500;">View</a>
        </div>
        @endforeach
    </div>
</div>
@endcomponent
@endif

If you have any questions or concerns, feel free to reach out to our support team.


<p style="font-size: 0.75rem; color: #6B7280; text-align: center;">
    If you didn't expect this email, please disregard it or contact our support team.
</p>
@endcomponent