<x-guest-layout>

    @php
        $firstErrorField = $errors->keys()[0] ?? null;
    @endphp
    <x-laravel.authentication-card>
        <x-slot name="logo">
           
            <x-laravel.authentication-card-logo />
        </x-slot>

        <x-laravel.validation-errors class="mb-4" />

        <form method="POST" action="{{ route('publisher.register.store') }}">
            @csrf
            <x-honeypot />

            <div class="text-2xl py-4 pb-6">Publisher Registration</div>

            <!-- Name -->
            <div>
                <x-laravel.label for="name" value="{{ __('Name') }}" />
                <x-laravel.input id="name" name="name" type="text" :value="old('name')" required autocomplete="name"
                :autofocus="$firstErrorField === 'name'"  class="block mt-1 w-full {{ $errors->has('name') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                @error('name')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>
            <!-- Primary Domain -->
            <div class="mt-4">
                <x-laravel.label for="primary_domain" value="Primary Domain" />
                <x-laravel.input id="primary_domain" name="primary_domain" type="text" :value="old('primary_domain')" required placeholder="example.com"
                :autofocus="$firstErrorField === 'primary_domain'"  class="block mt-1 w-full {{ $errors->has('primary_domain') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                @error('primary_domain')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>


            <!-- Email -->
            <div class="mt-4">
                <x-laravel.label for="email" value="{{ __('Email (Must match domain)') }}" />
                <x-laravel.input id="email" name="email" type="email" :value="old('email')" required autocomplete="username" placeholder="<EMAIL>"
                :autofocus="$firstErrorField === 'email'"  class="block mt-1 w-full {{ $errors->has('email') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                @error('email')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>



            <!-- Country -->
            <div class="mt-4">
                <x-laravel.label for="country_id" value="Country" />
                <select id="country_id" name="country_id" tabindex="0"
                    class="select2 block mt-1 w-full rounded-md shadow-sm border-gray-300 focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 @error('country') border-red-500 ring-red-500 ring-1 @enderror"
                    required>
                    <option value="">Select your country</option>
                    @foreach ($countries as $country)
                        <option value="{{ $country->id }}" {{ old('country_id') == $country->id ? 'selected' : '' }}>
                            {{ $country->name }}
                        </option>
                    @endforeach
                </select>
                @error('country_id')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Password -->
            <div class="mt-4">
                <x-laravel.label for="password" value="{{ __('Password') }}" />
                <x-laravel.input id="password" name="password" type="password" required autocomplete="new-password"
                    class="block mt-1 w-full {{ $errors->has('password') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                @error('password')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Confirm Password -->
            <div class="mt-4">
                <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" />
                <x-laravel.input id="password_confirmation" name="password_confirmation" type="password" required autocomplete="new-password"
                    class="block mt-1 w-full {{ $errors->has('password_confirmation') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                @error('password_confirmation')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Terms and Privacy -->
            @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
                <div class="mt-4">
                    <x-laravel.label for="terms">
                        <div class="flex items-center">
                            <x-laravel.checkbox name="terms" id="terms" required />
                            <div class="ml-2">
                                {!! __('I agree to the :terms_of_service and :privacy_policy', [
                                    'terms_of_service' =>
                                        '<a target="_blank" href="' .
                                        route('terms.show') .
                                        '" class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">' .
                                        __('Terms of Service') .
                                        '</a>',
                                    'privacy_policy' =>
                                        '<a target="_blank" href="' .
                                        route('policy.show') .
                                        '" class="unde`rline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">' .
                                        __('Privacy Policy') .
                                        '</a>',
                                ]) !!}
                            </div>
                        </div>
                    </x-laravel.label>
                </div>
            @endif

            <!-- Submit -->
            <div class="flex items-center justify-end mt-4">
                <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('login') }}">
                    {{ __('Already registered?') }}
                </a>

                <x-laravel.button class="ml-4">
                    {{ __('Register') }}
                </x-laravel.button>
            </div>
        </form>

    </x-laravel.authentication-card>
    @push('scripts')
        <script>
            $(function() {
                const $select = $('#country_id');

                $select.select2({
                    placeholder: 'Select your country',
                    width: '100%',
                    selectionCssClass: 'tailwind-select',
                    dropdownCssClass: 'tailwind-dropdown',
                    minimumResultsForSearch: 5,
                });

                // Focus search input when dropdown opens
                $select.on('select2:open', function() {
                    let searchInput = document.querySelector('.select2-container--open .select2-search__field');
                    if (searchInput) {
                        searchInput.focus();
                    }
                });
                // Fix tabbing behavior
                $select.on('select2:select', function() {
                    // Move focus to the next element after selection
                    const formElements = $('form').find(':input:visible');
                    const index = formElements.index($select);
                    if (index > -1 && index + 1 < formElements.length) {
                        formElements.eq(index + 1).focus();
                    }
                });

                // Custom handling for tab into Select2 (email -> country)
                $('#email').on('keydown', function(e) {
                    if (e.key === 'Tab' && !e.shiftKey) { // Tab forward from email
                        e.preventDefault();
                        $select.select2('open');
                    }
                });

                // Optional: handle shift+tab from Select2 back to email
                $('.select2-selection').on('keydown', function(e) {
                    if (e.key === 'Tab' && e.shiftKey) {
                        e.preventDefault();
                        $('#email').focus();
                    }
                });
            });
        </script>
    @endpush

</x-guest-layout>
