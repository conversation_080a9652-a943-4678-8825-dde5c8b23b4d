<x-guest-layout>
    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <x-laravel.validation-errors class="mb-4" />

        <form method="POST" action="{{ route('register') }}">
            @csrf
            <x-honeypot />

            <div>
                <x-laravel.label for="name" value="{{ __('Name') }}" />
                <x-laravel.input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name')"
                    required autofocus autocomplete="name" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="email" value="{{ __('Email') }}" />
                <x-laravel.input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')"
                    required autocomplete="username" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="password" value="{{ __('Password') }}" />
                <x-laravel.input id="password" class="block mt-1 w-full" type="password" name="password" required
                    autocomplete="new-password" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" />
                <x-laravel.input id="password_confirmation" class="block mt-1 w-full" type="password"
                    name="password_confirmation" required autocomplete="new-password" />
            </div>

            @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
            <div class="mt-4">
                <x-laravel.label for="terms">
                    <div class="flex items-center">
                        <x-laravel.checkbox name="terms" id="terms" required />

                        <div class="ml-2">
                            {!! __('I agree to the :terms_of_service and :privacy_policy', [
                            'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'"
                                class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">'.__('Terms
                                of Service').'</a>',
                            'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'"
                                class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">'.__('Privacy
                                Policy').'</a>',
                            ]) !!}
                        </div>
                    </div>
                </x-laravel.label>
            </div>
            @endif

            <div class="flex flex-col sm:flex-row items-center justify-between mt-6 gap-4">
                <div class="flex flex-col sm:flex-row items-center gap-4 text-sm">
                    <a class="text-gray-600 hover:text-gray-900 transition-colors duration-150"
                        href="{{ route('publisher.register') }}">
                        {{ __('Signup as a publisher') }}
                    </a>
                    <a class="text-gray-600 hover:text-gray-900 transition-colors duration-150"
                        href="{{ route('login') }}">
                        {{ __('Already Registered?') }}
                    </a>
                </div>

                <x-laravel.button class="w-full sm:w-auto">
                    {{ __('Register') }}
                </x-laravel.button>
            </div>
        </form>
    </x-laravel.authentication-card>
</x-guest-layout>