<x-guest-layout>
    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <x-laravel.validation-errors class="mb-4" />

        <form method="POST" action="{{ route('password.update') }}">
            @csrf
            <x-honeypot />

            <input type="hidden" name="token" value="{{ $request->route('token') }}">

            <div class="block">
                <x-laravel.label for="email" value="{{ __('Email') }}" />
                <x-laravel.input id="email" class="block mt-1 w-full" type="email" name="email"
                    :value="old('email', $request->email)" required autofocus autocomplete="username" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="password" value="{{ __('Password') }}" />
                <x-laravel.input id="password" class="block mt-1 w-full" type="password" name="password" required
                    autocomplete="new-password" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" />
                <x-laravel.input id="password_confirmation" class="block mt-1 w-full" type="password"
                    name="password_confirmation" required autocomplete="new-password" />
            </div>

            <div class="flex items-center justify-end mt-4">
                <x-laravel.button>
                    {{ __('Reset Password') }}
                </x-laravel.button>
            </div>
        </form>
    </x-laravel.authentication-card>
</x-guest-layout>