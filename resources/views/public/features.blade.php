@extends('layouts.public')


@section('content')




<div class="px-6 pt-16 lg:pt-28 pb-16 md:pb-24">
      <h1 class="pb-6 text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-headline font-black tracking-snug text-center leading-12 sm:leading-15 md:leading-19 lg:leading-26 text-gray-800">
        <span class="">The thoughtful way</span>
        <br class="hidden sm:block">
        <span class="text-transparent bg-clip-text bg-gradient-to-br from-yellow-300 to-red-500">to find a time to meet</span>
      </h1>
      <p class="pb-10 text-gray-600 text-lg md:text-xl lg:text-2xl text-center lg:tracking-tight leading-normal md:leading-normal lg:leading-9">
        <span>You’ll love it for the advanced features to keep you in control of your calendar.</span>
        <br class="hidden md:block">
        <span>They’ll love it for the personalized scheduling experience.</span>
      </p>

      <div class="mx-auto flex justify-center">
        <a href="/signup" class="btn btn-lg">Get started free</a>
      </div>
    </div>


    <!-- Features -->
<div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
  <div class="relative p-6 md:p-16">
    <!-- Grid -->
    <div class="relative z-10 lg:grid lg:grid-cols-12 lg:gap-16 lg:items-center">
      <div class="mb-10 lg:mb-0 lg:col-span-6 lg:col-start-8 lg:order-2">
        <h2 class="text-2xl text-gray-800 font-bold sm:text-3xl dark:text-gray-200">
          Fully customizable rules to match your unique needs
        </h2>

        <!-- Tab Navs -->
        <nav class="grid gap-4 mt-5 md:mt-10" aria-label="Tabs" role="tablist">
          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-md hs-tab-active:hover:border-transparent text-left hover:bg-gray-200 p-4 md:p-5 rounded-xl dark:hs-tab-active:bg-slate-900 dark:hover:bg-gray-700 active" id="tabs-with-card-item-1" data-hs-tab="#tabs-with-card-1" aria-controls="tabs-with-card-1" role="tab">
            <span class="flex">
              <svg class="flex-shrink-0 mt-2 h-6 w-6 md:w-7 md:h-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M5.5 2A3.5 3.5 0 0 0 2 5.5v5A3.5 3.5 0 0 0 5.5 14h5a3.5 3.5 0 0 0 3.5-3.5V8a.5.5 0 0 1 1 0v2.5a4.5 4.5 0 0 1-4.5 4.5h-5A4.5 4.5 0 0 1 1 10.5v-5A4.5 4.5 0 0 1 5.5 1H8a.5.5 0 0 1 0 1H5.5z"/>
                <path d="M16 3a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
              </svg>
              <span class="grow ml-6">
                <span class="block text-lg font-semibold hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-gray-200">Advanced tools</span>
                <span class="block mt-1 text-gray-800 dark:hs-tab-active:text-gray-200 dark:text-gray-200">Use Preline thoroughly thought and automated libraries to manage your businesses.</span>
              </span>
            </span>
          </button>

          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-md hs-tab-active:hover:border-transparent text-left hover:bg-gray-200 p-4 md:p-5 rounded-xl dark:hs-tab-active:bg-slate-900 dark:hover:bg-gray-700" id="tabs-with-card-item-2" data-hs-tab="#tabs-with-card-2" aria-controls="tabs-with-card-2" role="tab">
            <span class="flex">
              <svg class="flex-shrink-0 mt-2 h-6 w-6 md:w-7 md:h-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M0 0h1v15h15v1H0V0Zm14.817 3.113a.5.5 0 0 1 .07.704l-4.5 5.5a.5.5 0 0 1-.74.037L7.06 6.767l-3.656 5.027a.5.5 0 0 1-.808-.588l4-5.5a.5.5 0 0 1 .758-.06l2.609 2.61 4.15-5.073a.5.5 0 0 1 .704-.07Z"/>
              </svg>
              <span class="grow ml-6">
                <span class="block text-lg font-semibold hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-gray-200">Smart dashboards</span>
                <span class="block mt-1 text-gray-800 dark:hs-tab-active:text-gray-200 dark:text-gray-200">Quickly Preline sample components, copy-paste codes, and start right off.</span>
              </span>
            </span>
          </button>

          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-md hs-tab-active:hover:border-transparent text-left hover:bg-gray-200 p-4 md:p-5 rounded-xl dark:hs-tab-active:bg-slate-900 dark:hover:bg-gray-700" id="tabs-with-card-item-3" data-hs-tab="#tabs-with-card-3" aria-controls="tabs-with-card-3" role="tab">
            <span class="flex">
              <svg class="flex-shrink-0 mt-2 h-6 w-6 md:w-7 md:h-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M5.52.359A.5.5 0 0 1 6 0h4a.5.5 0 0 1 .474.658L8.694 6H12.5a.5.5 0 0 1 .395.807l-7 9a.5.5 0 0 1-.873-.454L6.823 9.5H3.5a.5.5 0 0 1-.48-.641l2.5-8.5zM6.374 1 4.168 8.5H7.5a.5.5 0 0 1 .478.647L6.78 13.04 11.478 7H8a.5.5 0 0 1-.474-.658L9.306 1H6.374z"/>
              </svg>
              <span class="grow ml-6">
                <span class="block text-lg font-semibold hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-gray-200">Powerful features</span>
                <span class="block mt-1 text-gray-800 dark:hs-tab-active:text-gray-200 dark:text-gray-200">Reduce time and effort on building modern look design with Preline only.</span>
              </span>
            </span>
          </button>
        </nav>
        <!-- End Tab Navs -->
      </div>
      <!-- End Col -->

      <div class="lg:col-span-6">
        <div class="relative">
          <!-- Tab Content -->
          <div>
            <div id="tabs-with-card-1" role="tabpanel" aria-labelledby="tabs-with-card-item-1">
              <img class="shadow-xl shadow-gray-200 rounded-xl dark:shadow-gray-900/[.2]" src="https://images.unsplash.com/photo-1605629921711-2f6b00c6bbf4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=987&h=1220&q=80" alt="Image Description">
            </div>

            <div id="tabs-with-card-2" class="hidden" role="tabpanel" aria-labelledby="tabs-with-card-item-2">
              <img class="shadow-xl shadow-gray-200 rounded-xl dark:shadow-gray-900/[.2]" src="https://images.unsplash.com/photo-1665686306574-1ace09918530?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=987&h=1220&q=80" alt="Image Description">
            </div>

            <div id="tabs-with-card-3" class="hidden" role="tabpanel" aria-labelledby="tabs-with-card-item-3">
              <img class="shadow-xl shadow-gray-200 rounded-xl dark:shadow-gray-900/[.2]" src="https://images.unsplash.com/photo-1598929213452-52d72f63e307?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=987&h=1220&q=80" alt="Image Description">
            </div>
          </div>
          <!-- End Tab Content -->

          <!-- SVG Element -->
          <div class="hidden absolute top-0 right-0 translate-x-20 md:block lg:translate-x-20">
            <svg class="w-16 h-auto text-orange-500" width="121" height="135" viewBox="0 0 121 135" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 16.4754C11.7688 27.4499 21.2452 57.3224 5 89.0164" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
              <path d="M33.6761 112.104C44.6984 98.1239 74.2618 57.6776 83.4821 5" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
              <path d="M50.5525 130C68.2064 127.495 110.731 117.541 116 78.0874" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
            </svg>
          </div>
          <!-- End SVG Element -->
        </div>
      </div>
      <!-- End Col -->
    </div>
    <!-- End Grid -->

    <!-- Background Color -->
    <div class="absolute inset-0 grid grid-cols-12 w-full h-full">
      <div class="col-span-full lg:col-span-7 lg:col-start-6 bg-gray-100 w-full h-5/6 rounded-xl sm:h-3/4 lg:h-full dark:bg-white/[.075]"></div>
    </div>
    <!-- End Background Color -->
  </div>
</div>
<!-- End Features -->

<section id="features" class="container space-y-6 bg-slate-50 py-8 dark:bg-transparent md:py-12 lg:py-24">
  <div class="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
    <h2 class="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">Features</h2>
    <p class="text-muted-foreground max-w-[85%] leading-normal sm:text-lg sm:leading-7">This project is an experiment to see how a modern app, with features like auth, subscriptions, API routes, and static pages would work in Next.js 13 app dir.</p>
  </div>
  <div class="mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem] md:grid-cols-3">
    <div class="bg-background relative overflow-hidden rounded-lg border p-2">
      <div class="flex h-[180px] flex-col justify-between rounded-md p-6">
        <svg viewBox="0 0 24 24" class="h-12 w-12 fill-current"><path d="M11.572 0c-.176 0-.31.001-.358.007a19.76 19.76 0 0 1-.364.033C7.443.346 4.25 2.185 2.228 5.012a11.875 11.875 0 0 0-2.119 5.243c-.096.659-.108.854-.108 1.747s.012 1.089.108 1.748c.652 4.506 3.86 8.292 8.209 9.695.779.25 1.6.422 2.534.525.363.04 1.935.04 2.299 0 1.611-.178 2.977-.577 4.323-1.264.207-.106.247-.134.219-.158-.02-.013-.9-1.193-1.955-2.62l-1.919-2.592-2.404-3.558a338.739 338.739 0 0 0-2.422-3.556c-.009-.002-.018 1.579-.023 3.51-.007 3.38-.01 3.515-.052 3.595a.426.426 0 0 1-.206.214c-.075.037-.14.044-.495.044H7.81l-.108-.068a.438.438 0 0 1-.157-.171l-.05-.106.006-4.703.007-4.705.072-.092a.645.645 0 0 1 .174-.143c.096-.047.134-.051.54-.051.478 0 .558.018.682.154.035.038 1.337 1.999 2.895 4.361a10760.433 10760.433 0 0 0 4.735 7.17l1.9 2.879.096-.063a12.317 12.317 0 0 0 2.466-2.163 11.944 11.944 0 0 0 2.824-6.134c.096-.66.108-.854.108-1.748 0-.893-.012-1.088-.108-1.747-.652-4.506-3.859-8.292-8.208-9.695a12.597 12.597 0 0 0-2.499-.523A33.119 33.119 0 0 0 11.573 0zm4.069 7.217c.347 0 .408.005.486.047a.473.473 0 0 1 .237.277c.018.06.023 1.365.018 4.304l-.006 4.218-.744-1.14-.746-1.14v-3.066c0-1.982.01-3.097.023-3.15a.478.478 0 0 1 .233-.296c.096-.05.13-.054.5-.054z"></path></svg>
        <div class="space-y-2">
          <h3 class="font-bold">Next.js 13</h3>
          <p class="text-muted-foreground text-sm">App dir, Routing, Layouts, Loading UI and API routes.</p>
        </div>
      </div>
    </div>
    <div class="bg-background relative overflow-hidden rounded-lg border p-2">
      <div class="flex h-[180px] flex-col justify-between rounded-md p-6">
        <svg viewBox="0 0 24 24" class="h-12 w-12 fill-current"><path d="M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38a2.167 2.167 0 0 0-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44a23.476 23.476 0 0 0-3.107-.534A23.892 23.892 0 0 0 12.769 4.7c1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442a22.73 22.73 0 0 0-3.113.538 15.02 15.02 0 0 1-.254-1.42c-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.345-.034-.46 0-.915.01-1.36.034.44-.572.895-1.096 1.345-1.565zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87a25.64 25.64 0 0 1-4.412.005 26.64 26.64 0 0 1-1.183-1.86c-.372-.64-.71-1.29-1.018-1.946a25.17 25.17 0 0 1 1.013-1.954c.38-.66.773-1.286 1.18-1.868A25.245 25.245 0 0 1 12 8.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933a25.952 25.952 0 0 0-1.345-2.32zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493a23.966 23.966 0 0 0-1.1-2.98c.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98a23.142 23.142 0 0 0-1.086 2.964c-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39a25.819 25.819 0 0 0 1.341-2.338zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143a22.005 22.005 0 0 1-2.006-.386c.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295a1.185 1.185 0 0 1-.553-.132c-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.345.034.46 0 .915-.01 1.36-.034-.44.572-.895 1.095-1.345 1.565-.455-.47-.91-.993-1.36-1.565z"></path></svg>
        <div class="space-y-2">
          <h3 class="font-bold">React 18</h3>
          <p class="text-sm">Server and Client Components. Use hook.</p>
        </div>
      </div>
    </div>
    <div class="bg-background relative overflow-hidden rounded-lg border p-2">
      <div class="flex h-[180px] flex-col justify-between rounded-md p-6">
        <svg viewBox="0 0 24 24" class="h-12 w-12 fill-current"><path d="M0 12C0 5.373 5.373 0 12 0c4.873 0 9.067 2.904 10.947 7.077l-15.87 15.87a11.981 11.981 0 0 1-1.935-1.099L14.99 12H12l-8.485 8.485A11.962 11.962 0 0 1 0 12Zm12.004 12L24 12.004C23.998 18.628 18.628 23.998 12.004 24Z"></path></svg>
        <div class="space-y-2">
          <h3 class="font-bold">Database</h3>
          <p class="text-muted-foreground text-sm">ORM using Prisma and deployed on PlanetScale.</p>
        </div>
      </div>
    </div>
    <div class="bg-background relative overflow-hidden rounded-lg border p-2">
      <div class="flex h-[180px] flex-col justify-between rounded-md p-6">
        <svg viewBox="0 0 24 24" class="h-12 w-12 fill-current"><path d="M12.001 4.8c-3.2 0-5.2 1.6-6 4.8 1.2-1.6 2.6-2.2 4.2-1.8.913.228 1.565.89 2.288 1.624C13.666 10.618 15.027 12 18.001 12c3.2 0 5.2-1.6 6-4.8-1.2 1.6-2.6 2.2-4.2 1.8-.913-.228-1.565-.89-2.288-1.624C16.337 6.182 14.976 4.8 12.001 4.8zm-6 7.2c-3.2 0-5.2 1.6-6 4.8 1.2-1.6 2.6-2.2 4.2-1.8.913.228 1.565.89 2.288 1.624 1.177 1.194 2.538 2.576 5.512 2.576 3.2 0 5.2-1.6 6-4.8-1.2 1.6-2.6 2.2-4.2 1.8-.913-.228-1.565-.89-2.288-1.624C10.337 13.382 8.976 12 6.001 12z"></path></svg>
        <div class="space-y-2">
          <h3 class="font-bold">Components</h3>
          <p class="text-muted-foreground text-sm">UI components built using Radix UI and styled with Tailwind CSS.</p>
        </div>
      </div>
    </div>
    <div class="bg-background relative overflow-hidden rounded-lg border p-2">
      <div class="flex h-[180px] flex-col justify-between rounded-md p-6">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" class="h-12 w-12 fill-current"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
        <div class="space-y-2">
          <h3 class="font-bold">Authentication</h3>
          <p class="text-muted-foreground text-sm">Authentication using NextAuth.js and middlewares.</p>
        </div>
      </div>
    </div>
    <div class="bg-background relative overflow-hidden rounded-lg border p-2">
      <div class="flex h-[180px] flex-col justify-between rounded-md p-6">
        <svg viewBox="0 0 24 24" class="h-12 w-12 fill-current"><path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.594-7.305h.003z"></path></svg>
        <div class="space-y-2">
          <h3 class="font-bold">Subscriptions</h3>
          <p class="text-muted-foreground text-sm">Free and paid subscriptions using Stripe.</p>
        </div>
      </div>
    </div>
  </div>
  <div class="mx-auto text-center md:max-w-[58rem]"><p class="text-muted-foreground leading-normal sm:text-lg sm:leading-7">Taxonomy also includes a blog and a full-featured documentation site built using Contentlayer and MDX.</p></div>
</section>



<div class="bg-gray-900 doodles-light mt-16 md:mt-24">
  <div class="mx-auto max-w-screen-xl px-6 md:px-10 py-16 md:py-24">
    <h2 class="pb-4 text-3xl md:text-6xl font-headline font-black text-white tracking-snug leading-10 md:leading-18 text-center">A fundamentally different <br class="hidden lg:block">scheduling experience</h2>
    
    
    <div class="pt-6 lg:pt-12 lg:flex space-y-6 lg:space-y-0 lg:space-x-6 w-full text-center">
      <div class="p-6 md:p-10 lg:w-1/2 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mx-auto flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-red-500 text-gray-900">
          <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
        </div>
        <div class="flex justify-center">
          <div class="mt-3 bg-red-500 text-gray-900 px-4 rounded-full font-medium text-sm sm:text-base leading-6 sm:leading-8">Other Scheduling Tools</div>
        </div>
        <h3 class="pt-6 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Force the scheduler to toggle between their calendar and your list of time slots</h3>
        
        <div class="flex justify-center">
          <div class="pt-12 max-w-lg">
            <svg width="490" viewBox="0 0 490 298" class="w-full" xmlns="http://www.w3.org/2000/svg"><g transform="translate(0 .857)" fill="#FFF" fill-rule="evenodd" fill-opacity=".5"><ellipse cx="101.157" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="101.157" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="101.157" cy="230.696" rx="15.044" ry="15.018"></ellipse><ellipse cx="101.157" cy="275.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="57.582" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="57.582" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="57.582" cy="230.696" rx="15.044" ry="15.018"></ellipse><ellipse cx="57.582" cy="275.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="15.045" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="15.045" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="15.045" cy="230.696" rx="15.044" ry="15.018"></ellipse><ellipse cx="15.045" cy="275.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="143.695" cy="101.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="143.695" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="143.695" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="143.695" cy="230.696" rx="15.044" ry="15.018"></ellipse><ellipse cx="143.695" cy="275.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="187.27" cy="101.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="187.27" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="187.27" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="187.27" cy="230.696" rx="15.044" ry="15.018"></ellipse><ellipse cx="187.27" cy="275.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="229.808" cy="101.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="229.808" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="229.808" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="229.808" cy="230.696" rx="15.044" ry="15.018"></ellipse><ellipse cx="273.383" cy="101.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="273.383" cy="144.732" rx="15.044" ry="15.018"></ellipse><ellipse cx="273.383" cy="188.232" rx="15.044" ry="15.018"></ellipse><ellipse cx="273.383" cy="230.696" rx="15.044" ry="15.018"></ellipse><rect x=".001" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="42.538" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="86.113" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="128.651" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="172.226" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="214.764" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="258.339" y="53.071" width="30.088" height="10.357" rx="5.179"></rect><rect x="318" y="53" width="172" height="50.75" rx="10"></rect><rect width="144" height="22" rx="6"></rect><rect x="318" y="116.143" width="172" height="50.75" rx="10"></rect><rect x="318" y="181.143" width="172" height="50.75" rx="10"></rect><rect x="318" y="246.143" width="172" height="50.75" rx="10"></rect></g></svg>
          </div>
        </div>
      </div>
      <div class="p-6 md:p-10 lg:w-1/2 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mx-auto flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-yellow-300 text-gray-900">
          <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check"><polyline points="20 6 9 17 4 12"></polyline></svg>
        </div>
        <div class="flex justify-center">
          <div class="mt-3 bg-yellow-300 text-gray-900 px-4 rounded-full font-medium text-sm sm:text-base leading-6 sm:leading-8">The SavvyCal Experience</div>
        </div>
        <h3 class="pt-6 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">A beautiful, interactive interface that allows the scheduler to overlay their calendar</h3>
        
        <div class="flex justify-center">
          <div class="pt-12 max-w-lg">
            <svg width="490" viewBox="0 0 490 298" class="w-full" xmlns="http://www.w3.org/2000/svg"><g transform="translate(0 .375)" fill="none" fill-rule="evenodd"><g fill="#FFF"><text font-size="13" letter-spacing="-.204" transform="translate(16)"><tspan x="7.317" y="12">Monday</tspan></text><text font-size="23" font-weight="500" letter-spacing="-.361" transform="translate(16)"><tspan x="16.109" y="39">27</tspan></text></g><g fill="#FFF"><text font-size="13" letter-spacing="-.204" transform="translate(116)"><tspan x="6.854" y="12">Tuesday</tspan></text><text font-size="23" font-weight="500" letter-spacing="-.361" transform="translate(116)"><tspan x="15.306" y="39">28</tspan></text></g><g fill="#FFF"><text font-size="13" letter-spacing="-.204" transform="translate(209)"><tspan x="4.537" y="12">Wednesday</tspan></text><text font-size="23" font-weight="500" letter-spacing="-.361" transform="translate(209)"><tspan x="22.363" y="39">29</tspan></text></g><g fill="#FFF"><text font-size="13" letter-spacing="-.204" transform="translate(316)"><tspan x="4.065" y="12">Thursday</tspan></text><text font-size="23" font-weight="500" letter-spacing="-.361" transform="translate(316)"><tspan x="14.998" y="39">30</tspan></text></g><g fill="#FFF"><text font-size="13" letter-spacing="-.204" transform="translate(416)"><tspan x="12.833" y="12">Friday</tspan></text><text font-size="23" font-weight="500" letter-spacing="-.361" transform="translate(416)"><tspan x="23.813" y="39">1</tspan></text></g><rect fill-opacity=".5" fill="#FFF" y="55.482" width="90" height="242" rx="10"></rect><rect fill-opacity=".5" fill="#FFF" x="100" y="55.482" width="90" height="89" rx="10"></rect><rect fill="#FAC916" x="100" y="154.482" width="90" height="29" rx="6"></rect><rect fill-opacity=".5" fill="#FFF" x="100" y="193.482" width="90" height="104" rx="10"></rect><rect fill-opacity=".5" fill="#FFF" x="200" y="55.482" width="90" height="242" rx="10"></rect><rect fill-opacity=".5" fill="#FFF" x="300" y="55.482" width="90" height="242" rx="10"></rect><rect fill-opacity=".5" fill="#FFF" x="400" y="55.482" width="90" height="242" rx="10"></rect></g></svg>
          </div>
        </div>
      </div>
    </div>

    <div class="pt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-blue-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-layers"><polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Calendar overlay</h3>
          <p class="text-gray-400 md:text-lg">No need to switch back and forth between screens any more.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-pink-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-at-sign"><circle cx="12" cy="12" r="4"></circle><path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94"></path></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Prefill recipient info</h3>
          <p class="text-gray-400 md:text-lg">Save the scheduler time by prefilling their info.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-red-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-sliders"><line x1="4" y1="21" x2="4" y2="14"></line><line x1="4" y1="10" x2="4" y2="3"></line><line x1="12" y1="21" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="3"></line><line x1="20" y1="21" x2="20" y2="16"></line><line x1="20" y1="12" x2="20" y2="3"></line><line x1="1" y1="14" x2="7" y2="14"></line><line x1="9" y1="8" x2="15" y2="8"></line><line x1="17" y1="16" x2="23" y2="16"></line></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Ranked availability</h3>
          <p class="text-gray-400 md:text-lg">Present your preferred availability instead of just all your availability.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-purple-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-pause-circle"><circle cx="12" cy="12" r="10"></circle><line x1="10" y1="15" x2="10" y2="9"></line><line x1="14" y1="15" x2="14" y2="9"></line></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Limit scheduling frequency</h3>
          <p class="text-gray-400 md:text-lg">Cap meetings per day, week, and month.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-orange-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-git-branch"><line x1="6" y1="3" x2="6" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Per-link calendar settings</h3>
          <p class="text-gray-400 md:text-lg">Route different links to different calendars.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-pink-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-clock"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Multiple durations</h3>
          <p class="text-gray-400 md:text-lg">Offer several options on one scheduling link.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-blue-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-briefcase"><rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Time zone scheduling</h3>
          <p class="text-gray-400 md:text-lg">We'll update your time zone for you when you travel.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-purple-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-key"><path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Delegate account access</h3>
          <p class="text-gray-400 md:text-lg">Allow your assistant to manage your account for you.</p>
        </div>
      </div>
      <div class="p-6 md:p-10 rounded-xl bg-gray-800 transform origin-center hover:scale-105 hover:bg-gray-700 transition duration-150 ease-in-out">
        <div class="mb-6 flex items-center h-10 text-yellow-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-stop-circle"><circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect></svg>
        </div>
        <div>
          <h3 class="pb-1 md:pb-2 text-gray-100 font-semibold text-lg md:text-2xl md:tracking-tight leading-6">Single-use links</h3>
          <p class="text-gray-400 md:text-lg">Automatically archive a link after someone uses it.</p>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="pt-16 md:pt-24">
  <div class="mx-auto max-w-screen-xl text-center">
    <div class="sm:flex w-full divide-y sm:divide-y-0 sm:divide-x divide-dashed divide-gray-400">
      <div class="sm:w-1/2 md:w-1/3">
        <div class="px-6 py-12 text-xl lg:text-2xl leading-7 lg:leading-8">
          <img src="https://d7hyo3d3e1qv5.cloudfront.net/assets/testimonials/jetboost-chris-db6efaf990821fd887e7ee34e76d2d16.png?vsn=d" width="400" height="400" class="block w-16 h-16 rounded-full mx-auto" alt="Chris" loading="lazy">
          <p class="flex-grow py-6 text-gray-600 lg:tracking-tight">“SavvyCal is such a joy to use! Still makes me happy every time.”</p>
          <div class="text-xl text-gray-700 font-medium">Chris Spags</div>
          <div class="text-lg text-gray-500">Founder of Jetboost</div>
        </div>
      </div>


      <div class="sm:w-1/2 md:w-1/3">
        <div class="px-6 py-12 text-xl lg:text-2xl leading-7 lg:leading-8">
          <img src="https://d7hyo3d3e1qv5.cloudfront.net/assets/testimonials/summit-matt-93187123dc9e9d5b567069fc604823b9.jpg?vsn=d" width="400" height="400" class="block w-16 h-16 rounded-full mx-auto" alt="Matt" loading="lazy">
          <p class="flex-grow py-6 text-gray-600 lg:tracking-tight">“I use SavvyCal so my customers can easily compare my availability to their own. Making their lives a little easier is a treat I’m happy to pay for.”</p>
          <div class="text-xl text-gray-700 font-medium">Matt Wensing</div>
          <div class="text-lg text-gray-500">Founder of Summit</div>
        </div>
      </div>

      <div class="sm:w-1/2 md:w-1/3">
        <div class="px-6 py-12 text-xl lg:text-2xl leading-7 lg:leading-8">
          <img src="https://d7hyo3d3e1qv5.cloudfront.net/assets/testimonials/tinyseed-tracy-3415f917c4c0929ec6b601a1833e030e.jpg?vsn=d" width="400" height="400" class="block w-16 h-16 rounded-full mx-auto" alt="Tracy" loading="lazy">
          <p class="flex-grow py-6 text-gray-600 lg:tracking-tight">“My favorite is the personalized links. SavvyCal links feel more natural and save everyone time.”</p>
          <div class="text-xl text-gray-700 font-medium">Tracy Osborn</div>
          <div class="text-lg text-gray-500">Program Director at TinySeed</div>
        </div>
      </div>
    </div>
  </div>
</div>


{{-- SEMRush Features Titles --}}

<ul class="tools__list">
  <li class="tools__item tool-card">
    <div class="tool-card__content">
      <h2 class="title tool-card__title" id="tool-1">On Page SEO Checker</h2>
      <p>The tool provides you with the exact action points needed to improve your website. The recommendations are based on the analysis of your competitors (those in Google's Top 10 results) and your own site.</p>
    </div>
    <img class="tool-card__img" src="https://cdn.semrush.com/features/static/features/second-level/on-page-seo-checker.6dae78e2936b.svg" alt="" width="172" height="172" />
    <div class="tool-card__btns">
      <a class="button button--card button--m" href="/signup/?src=features&amp;redirect_to=%2Fon-page-seo-checker%2F" target="_blank" data-ga-event-onclick="" data-ga-category="page:features" data-ga-action="click:button" data-ga-label="middle"> Get Ideas </a>

      <a class="tool-card__link" href="/features/on-page-seo-checker/?second_level_id=16" target="_blank" data-ga-event-onclick="" data-ga-category="page:features" data-ga-action="click:link" data-ga-label="middle" id="learn-more-1" aria-labelledby="learn-more-1 tool-1"> Learn more </a>
    </div>
  </li>
  <li class="tools__item tool-card">
    <div class="tool-card__content">
      <h2 class="title tool-card__title" id="tool-2">SEO Writing Assistant</h2>
      <p>This intuitive tool subtly provides you with optimization ideas for your text just as you're bringing it to life. Get recommendations about readability, words to use, title, word count, and tone of voice.</p>
    </div>
    <img class="tool-card__img" src="https://cdn.semrush.com/features/static/features/second-level/seo-writing-assistant.4d011311baa2.svg" alt="" width="172" height="172" />
    <div class="tool-card__btns">
      <a class="button button--card button--m" href="/signup/?src=features&amp;custom=cmp&amp;redirect_to=%2Fcontent-marketing%2Fswa%2F" target="_blank" data-ga-event-onclick="" data-ga-category="page:features" data-ga-action="click:button" data-ga-label="middle">Start Writing</a>

      <a class="tool-card__link" href="/features/seo-writing-assistant/?second_level_id=16" target="_blank" data-ga-event-onclick="" data-ga-category="page:features" data-ga-action="click:link" data-ga-label="middle" id="learn-more-2" aria-labelledby="learn-more-2 tool-2"> Learn more </a>
    </div>
  </li>
</ul>



<style>
  
.tools__list {
  flex-direction: row;
  flex-wrap: wrap;
  gap: 32px;
}
.tools__list {
  display: flex;
  flex-direction: row;
}
.tool-card {
  padding: 20px 20px 32px 32px;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
}
.tool-card {
  background: #fff;
  border: 1px solid #d1d4db;
  border-radius: 16px;
  padding: 24px 24px 32px;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
  display: flex;
}
.tools__item {
  width: calc(50% - 16px);
}
.tool-card__content {
  width: calc(100% - 172px - 32px);
  padding-top: 12px;
}
.tool-card__content {
  margin-bottom: 40px;
}
.tool-card__title {
  font-weight: 700;
  font-size: 26px;
  line-height: 120%;
  margin-bottom: 12px;
}
.tool-card__img {
  order: 0;
  margin-bottom: 40px;
}
.tool-card__btns {
  flex-basis: 100%;
}
.tool-card__btns {
  flex: 0 1 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: baseline;
  margin-top: auto;
}
.tool-card__btns {
  display: flex;
  gap: 20px;
  text-align: center;
}
.button--m {
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  padding: 10px 30px 12px;
}
.button--card {
  color: #171a22;
  fill: #171a22;
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #b5bac4;
}
.tool-card__link {
  margin-right: 21px;
}
.tool-card__link {
  text-decoration: none;
  color: #575c66;
  transition: color 0.2s ease-in-out;
}
.tool-card:focus,
.tool-card:hover {
  outline: 0;
  border-color: transparent;
  box-shadow: 5px 5px 25px rgba(137, 141, 154, 0.3);
}
.tool-card:focus .button--card,
.tool-card:focus .tool-card__link,
.tool-card:hover .button--card,
.tool-card:hover .tool-card__link {
  color: #ff622d;
  border-color: currentColor;
}

</style>

@endsection