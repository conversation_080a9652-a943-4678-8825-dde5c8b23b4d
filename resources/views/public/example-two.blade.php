@vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/marketplace.js'])

<!-- Livewire / Alpine -->
@livewireStyles
@livewireScripts



<div class="flex my-24 flex-col justify-center items-center">
	<div class="space-y-20 mx-auto w-4/6">




		<x-examples.alpine.banner />
		<x-examples.alpine.copy-to-clipboard />
		<x-examples.alpine.modal />
		<x-examples.alpine.progress />
		<x-examples.alpine.quantity-input />
		<x-examples.alpine.radio-group />
		<x-examples.alpine.switch />



		<x-examples.preline.alerts />
		<x-examples.preline.cards />
		<x-examples.preline.empty-state />
		<x-examples.preline.newsletter />


		
		
		


		<x-examples.tailwindui.change-plan-card />
		<x-examples.tailwindui.data-display />
		<x-examples.tailwindui.newsletter />
		<x-examples.tailwindui.progress-navigation />
		<x-examples.tailwindui.stats-public />
		<x-examples.tailwindui.testimonials />
		


		<x-examples.etc.coming-soon />
		<x-examples.etc.details-card />
		<x-examples.etc.login-second />
		<x-examples.etc.tags-card />
		<x-examples.etc.page-info-head />
		<x-examples.etc.comments />
		<x-examples.etc.integrations />
		<x-examples.etc.card-payment />


		<x-examples.flowbite.skeleton />
		<x-examples.flowbite.checkmarks-list />
		<x-examples.flowbite.dropzone />
		<x-examples.flowbite.speed-dial />
		<x-examples.flowbite.steps-indicator />
		<x-examples.flowbite.table-product-buy />
		<x-examples.flowbite.table-simple />



		{{-- <x-examples.dialog.action /> --}}
		<x-examples.dialog.create-project />
		<x-examples.dialog.payment-successful />

		{{-- <x-tailwindui.privacy-notice /> --}}
		{{-- <x-tailwindui.bottom-notification-sticky /> --}}
		{{-- <x-examples.mobile-menu-bottom /> --}}
	</div>
</div>

