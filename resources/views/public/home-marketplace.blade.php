@extends('layouts.public')
@section('content')

@php
    $title = 'PressBear - Marketplace for Sponsorship & Link Building';
    $description = 'PressBear is a marketplace connecting brands with over 20k publishers for effortless sponsorships and link-building. Streamline PR, boost rankings, and grow your online presence with quality-driven, strategic partnerships.';
@endphp


<!---------------------->
<!-- Top Hero Content -->
<!---------------------->
<section class="w-full px-6 pb-6 antialiased bg-white  md:-mt-6" 
         style="background-image: url({{Storage::url('graphics/bg.svg')}}); background-size: cover;">

    <div class="mx-auto md:max-w-7xl">

        <div class="py-12 md:py-20 lg:py-[6rem] mx-auto text-left sm:px-4 max-w-full md:max-w-none md:text-center">

            <h1 style="line-height: 1.13;" class="title-h  font-bold  text-center text-[2.6rem] md:text-7xl lg:text-[5.7rem]">
                <span class="text-transparent bg-clip-text bg-gradient-to-br from-slate-700 to-slate-800 ">
                    Marketplace for
                </span>

                <div id="changing-word" class="moving-element">Sponsorships</div>

            </h1>

            <div class="content-h flex flex-wrap items-center justify-center space-x-3  md:space-x-5 my-8">
                <div class="text-xs mt-2 sm:mt-0 sm:text-sm text-center p-2 sm:py-1.5 sm:px-5 rounded-xl bg-emerald-100 text-emerald-900 hover:bg-emerald-200 font-medium transition-all">Link Building</div>
                <div class="text-xs mt-2 sm:mt-0 sm:text-sm text-center p-2 sm:py-1.5 sm:px-5 rounded-xl bg-orange-100 text-orange-900 hover:bg-orange-200 font-medium transition-all">Sponsorships</div>
                <div class="text-xs mt-2 sm:mt-0 sm:text-sm text-center p-2 sm:py-1.5 sm:px-5 rounded-xl bg-purple-100 text-purple-900 hover:bg-purple-200 font-medium transition-all">Brand Awareness</div>
                <div class="hidden md:block text-xs mt-2 sm:mt-0 sm:text-sm text-center p-2 sm:py-1.5 sm:px-5 rounded-xl bg-sky-100 text-sky-900 hover:bg-sky-200 font-medium transition-all">Press Releases</div>
            </div>

            <div class="content-h mx-auto my-2 text-slate-500 px-1 max-w-full  md:max-w-xl text-center md:text-[1.30rem] font-medium">
                {{-- Connecting Brands with 20k+ Publishers to help build strong digital footprint via sponsorships, press releases and link building.  --}}

                Publish your content on over 20,000 high quality websites.
                Eliminate the tedious research, outreach, and negotiations. Discover sites that match your traffic and ranking criteria, with all the insights at your fingertips.

                {{-- Connect with 20,000+ publishers to effortlessly publish your content on relevant websites. Skip the manual research, outreach, and negotiations. Find sites that match your traffic and ranking criteria, and review all the information before deciding. Make content distribution seamless and efficient.            --}}

                {{-- Collaborate with over 20k publishers to get your content featured on relevant websites. Eliminate the tedious research, outreach, and negotiations. Discover sites that match your traffic and ranking criteria, with all the insights at your fingertips. --}}
            </div>

            <div class="content-h flex flex-col items-center justify-center my-8 space-y-4 text-center sm:flex-row sm:space-y-0 sm:space-x-4">
                <span class="inline-flex w-2/3 md:w-auto ">
                    <a href="{{ route('register') }}" class="inline-flex items-center justify-center w-full px-6 py-2 text-base font-medium leading-6 text-white bg-gradient-to-br from-slate-500 to-slate-700 border border-transparent rounded-full xl:px-10 md:w-auto hover:from-slate-600 hover:to-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-900 hover:shadow-lg">
                        Get Started
                    </a>
                </span>
                <span class="hidden md:inline-flex w-full md:w-auto ">
                    <a href="#screenshot" class="inline-flex items-center justify-center w-full px-6 py-2 text-base font-medium leading-6 text-slate-800 bg-slate-100 border rounded-full xl:px-8 md:w-auto hover:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-200">
                        Learn More
                    </a>
                </span>
            </div>



            <div id="screenshot" class="mt-20 md:mt-[5rem] md:mb-16">
              <div class="mx-auto max-w-7xl">
                <div class="mt-12 flow-root image-container">
                  <div class="tilted-image -m-2 rounded-xl bg-slate-600/5 p-2 ring-1 ring-inset ring-slate-700/10 lg:-m-4 lg:rounded-2xl lg:p-4">
                    <img src="{{Storage::url('resources/homepage/hero-sep.png')}}" alt="App screenshot" width="2432" height="1442" class="rounded-md shadow-2xl ring-1 ring-slate-700/10">
                  </div>
                </div>
              </div>
            </div>

        </div>
    </div>
</section>
<!---------------------->
 <!-- End Hero Section -->
<!---------------------->



<!---------------------->
{{-- Feature Tiles --}}
<!---------------------->
<section id="features-list-tiles" class="md:-mt-20 px-4">
    <div class="interests__cards grid grid-cols-1 lg:grid-cols-3 max-w-7xl">

        <div class="flex mx-auto interests__card feature-card  bg-white border-2 border-slate-200 rounded-xl "> 
            <h3 class="feature_title feature-card__title title-h font-medium  text-xl text-slate-800">Link Building</h3>
            <p class="feature-card__text content-h leading-7 text-slate-800">
              Access a network of over 20,000 premium websites to enhance your organic search rankings and grow your online visibility.
            </p>   
        </div>

        <div class="flex mx-auto interests__card feature-card  bg-white border-2 border-slate-200 rounded-xl "> 
            <h3 class="feature_title feature-card__title title-h font-medium text-xl text-slate-800">Sponsorships</h3>
            <p class="feature-card__text content-h leading-7 text-slate-800">
              Expand your brand's online presence by partnering with top-tier, relevant websites for impactful exposure and increased visibility.
            </p>   
        </div>

        <div class="flex mx-auto interests__card feature-card  bg-white border-2 border-slate-200 rounded-xl "> 
            <h3 class="feature_title feature-card__title title-h font-medium text-xl text-slate-800">Press Releases</h3>
            <p class="feature-card__text content-h leading-7 text-slate-800">
                Effortlessly publish press releases and news updates on targeted websites for maximum reach and visibility.
            </p>
        </div>

    </div>
</section>


    <div class="max-w-full overflow-hidden mx-6 my-12 lg:my-24 xl:my-32 text-center lg:mx-auto text-slate-300">
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    </div>

    <div class="mx-6 xl:mx-auto lg:max-w-6xl  my-12 space-y-6">

        <div class="space-y-1 title-h text-4xl lg:text-5xl text-slate-800">
            <div class="">Boost Rankings</div>
            <div class="">and Maximize Exposure</div>
        </div>
        
        
        <div class="max-w-4xl leading-7 text-slate-800">
            We simplify the process of getting your brand featured on leading websites by connecting you with top tier publishers. Our team crafts tailored content that aligns with your niche and meets your unique goals, ensuring it's published on the platforms you choose. Backed by data-driven strategies, exclusive media partnerships, and a commitment to quality, we help strengthen your online presence and enhance your organic SEO performance.
        </div>
    </div>


<!---------------------->
{{-- INFO SECTIONS --}}
<!---------------------->
<section id="flow-define" class="flex md:py-6 flex-col items-center mx-auto space-y-10 lg:space-y-20">


    {{-- 1. CONNECT PUBLISHERS --}}
    <div id="connect-publishers" class="max-w-6xl mx-4 xl:mx-auto bg-white shadow-lg  border-2 border-slate-300 rounded-xl hover:shadow-xl transition  hover:shadow-slate-100">

        <div class="flex-col md:grid md:grid-cols-5 p-6 pb-0 md:px-10 md:pt-8">
            <div class="left-section col-span-3 md:mr-6">
                <div class="title-h section-header font-medium text-xl md:text-2xl text-slate-700 flex items-center">
                    <x-icons.lucide.sparkles class="w-7 h-7 mr-3 text-slate-700" />
                    Data Driven Publishing
                </div>
                <div class="content-h section-body py-6 leading-7 text-slate-800 max-w-xl">
                    Easily search, filter, and sort through websites to find the right publications. Gain access to comprehensive insights, including traffic statistics, engagement levels, analytics, and SEO scores. Make informed decisions to maximize your impact and achieve your sponsorship goals.

     {{--                <ul class="tick-list relative ml-7 py-4 space-y-3">
                        <li>
                            <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                            <strong>Find The Right Publishers</strong> that perfectly meet your criteria.
                        </li>
                        <li>
                            <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                            <strong>Filter & Sort</strong> in a streamlined way.
                        </li>
                        <li>
                            <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                            <strong>All the Data</strong> to help you make the right decisions.
                        </li>
                    </ul> --}}
                </div>    
            </div>

            {{-- Mockup Filter --}}
            <div class="hidden lg:flex right-section col-span-2 items-center justify-center">
                <div class="filters flex flex-wrap content-h">

                    <div class="filter cursor-default text-xs md:text-sm w-auto m-3 bg-slate-50
                                 border-slate-300 border flex items-center px-3
                                py-1 rounded-xl font-medium shadow-sm hover:bg-slate-100">
                        <span class="text-slate-600/90 mr-1">Domain Rank:</span>
                        <span class="text-slate-600 tracking-wide">10-100</span>
                        <span class="ml-1">
                            <x-icons.etc.down-icon />
                        </span>
                    </div>

                    <div class="filter cursor-default text-xs md:text-sm w-auto m-3 bg-slate-50
                                 border-slate-300 border flex items-center px-3
                                 py-1 rounded-xl font-medium shadow-sm hover:bg-slate-100">
                        <span class="text-slate-600/90 mr-1 ">Org Traffic:</span>
                        <span class="text-slate-600 tracking-wide">1k-100k</span>
                        <span class="ml-1">
                            <x-icons.etc.down-icon />
                        </span>
                    </div>

                    <div class="filter cursor-default text-xs md:text-sm w-auto m-3 bg-slate-50
                                 border-slate-300 border flex items-center px-3
                                 py-1 rounded-xl font-medium shadow-sm hover:bg-slate-100">
                        <span class="text-slate-600/90 mr-1">Category:</span>
                        <span class="text-slate-600 tracking-wide">Tech</span>
                        <span class="ml-1">
                            <x-icons.etc.down-icon />
                        </span>
                    </div>

                    <div class="filter cursor-default text-xs md:text-sm w-auto m-3 bg-slate-50
                                 border-slate-300 border flex items-center px-3
                                 py-1 rounded-xl font-medium shadow-sm hover:bg-slate-100">
                        <span class="text-slate-600/90 mr-1">Country:</span>
                        <span class="text-slate-600 tracking-wide">United States</span>
                        <span class="ml-1">
                            <x-icons.etc.down-icon />
                        </span>
                    </div>

                    <div class="filter cursor-default text-xs md:text-sm w-auto m-3 bg-slate-50
                                 border-slate-300 border flex items-center px-3
                                 py-1 rounded-xl font-medium shadow-sm hover:bg-slate-100">
                        <span class="text-slate-600/90 mr-1">Price:</span>
                        <span class="text-slate-600 tracking-wide">$200 - $500</span>
                        <span class="ml-1">
                            <x-icons.etc.down-icon />
                        </span>
                    </div>

                    <div class="filter cursor-default text-xs md:text-sm w-auto m-3 bg-slate-50
                                 border-slate-300 border flex items-center px-3
                                 py-1 rounded-xl font-medium shadow-sm hover:bg-slate-100">
                        <span class="text-slate-600/90 mr-1">Language:</span>
                        <span class="text-slate-600 tracking-wide">English</span>
                        <span class="ml-1">
                            <x-icons.etc.down-icon />
                        </span>
                    </div>
                </div>
            </div>
        </div>

        {{-- Website with Stats --}}
        <div class="bottom-section w-full p-4 md:px-6 md:pb-4 pt-0">
            <img class="rounded-lg"  src="{{Storage::url('resources/homepage/website-stats.png')}}">
        </div>

    </div>







    {{-- 2. FIND SITES --}}
    <div id="find-sites" class="bg-white shadow-lg mx-4 xl:mx-auto max-w-6xl border-2 border-slate-300 
                                rounded-xl hover:shadow-xl transition  hover:shadow-slate-100">

      <div class="flex flex-col md:grid md:grid-cols-2 content-center">

        <div class="left-section col-span-1 p-6 md:px-10 md:py-8">
            <div class="title-h -ml-1 section-header font-medium text-xl md:text-2xl  text-slate-700 flex items-center">
                <x-icons.lucide.route class="w-7 h-7 mr-2 text-slate-600" />
                Find The Right Publishers
            </div>
            <div class="content-h section-body pt-6 leading-7 text-slate-700 max-w-xl">
                Discover websites that are not only relevant to your niche but also open to publishing your content. Say goodbye to the hassle of navigating requirements—we’ve got it covered.
                <ul class="tick-list relative ml-7 mt-1 py-4 space-y-3">
                    <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong class="text-slate-600">Our pre-negotiated rates</strong> with publishers allows you to gain greater exposure at a lower cost.
                    </li>
                    <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong class="text-slate-600">Get featured</strong> in trusted media outlets and establish yourself as an authority in your industry.
                    </li>
                   {{--  <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong>Stress Free Publishing</strong> with the help of our team that care of every little detail.
                    </li> --}}
                </ul>
            </div>    
        </div>

        <div class="right-section col-span-1 p-2 md:p-0 md:mr-2 -mt-10 md:mt-0">


               <div class="flex title-h mt-2 -mb-5 text-slate-800/70 font-medium items-center  content-center">
                <span style="transform: rotate(2deg);" class="text-sm ml-auto">Choose Niche</span>
                
                <div style="position: relative; transform: rotate(93deg); margin-top: 37px; margin-left: -1px;" class="mr-auto z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-redo"><path d="M21 7v6h-6"/><path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"/></svg>
                </div>  
            </div>

            <div class="cart-image">
                <img class="h-auto  w-auto" src="{{Storage::url('resources/homepage/cart.png')}}">
            </div>
        </div>

      </div>
    </div>




     <div id="find-sites" class="bg-white shadow-lg mx-4 xl:mx-auto max-w-6xl  border-2 border-slate-300 
                                rounded-xl hover:shadow-xl transition  hover:shadow-slate-100">

      <div class="flex flex-col md:grid md:grid-cols-2 content-center">

        <div class="left-section col-span-1 p-6 md:px-10 md:py-8">
            <div class="title-h -ml-1 section-header font-medium text-xl md:text-2xl text-slate-800 flex items-center">
                <x-icons.lucide.rss class="w-6 h-6 mt-1 mr-2 text-slate-600" />
                From Concept to Publishing
            </div>
            <div class="content-h section-body pt-6 leading-7 text-slate-700 max-w-xl">
                Select the sites that meet your needs, place your order, and we'll take care of everything from content creation to publishing.
                <ul class="tick-list relative ml-7 mt-1 py-4 space-y-2">
                    <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong class="text-slate-600">Tell us what you need</strong> and we’ll create the perfect content tailored to your exact specifications.
                    </li>
                    <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong class="text-slate-600">Content perfectly crafted</strong> for search visibility and organic growth.
                    </li>
                    <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong class="text-slate-600">Guranteed publishing</strong> or we’ll refund your payment.
                    </li>
                   {{--  <li>
                        <x-icons.lucide.check class="absolute -left-7 mt-1.5 text-emerald-700 w-5 h-5 mr-1" />
                        <strong>Stress Free Publishing</strong> with the help of our team that care of every little detail.
                    </li> --}}
                </ul>
            </div>    
        </div>

                {{-- Website with Stats --}}
        <div class="right-section col-span-1 -mt-8 md:mt-0 p-2 md:p-0">

            <div class="flex  title-h mt-2 -mb-5 text-slate-800/70 font-medium items-center  content-center">
                <span style="transform: rotate(2deg);" class="text-sm ml-auto">Share Your Requirements</span>
                
                <div style="position: relative; transform: rotate(93deg); margin-top: 37px; margin-left: -1px;" class="mr-auto z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-redo"><path d="M21 7v6h-6"/><path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7"/></svg>
                </div>  
            </div>

            <div class="">
                <img class="rounded-lg" src="{{Storage::url('resources/homepage/req-3.png')}}">
              {{-- <div class="relative" aria-hidden="true">
                <div class="absolute -inset-x-4 bottom-0 bg-gradient-to-t from-white pt-[10%]"></div>
              </div> --}}
            </div>
        </div>


      </div>







    </div>

  

</section>
<!---------------------->
<!-- END INFO SECTION -->
<!---------------------->








<!-------------------------->
<!-- INTEGRATIONS SECTION -->
<!-------------------------->
<section class="py-20 md:py-28 bg-white">
    <div class="max-w-7xl px-10 mx-auto text-center">
        <p class="title-h text-orange-600 font-medium uppercase text-xs md:text-sm lg:text-base">
            Platform Data Integrations
        </p>
        <h2 class="title-h font-bold text-2xl lg:text-3xl mt-3">
            <span class="text-transparent bg-clip-text bg-gradient-to-br from-slate-700 to-slate-800">
                Data From Your Favorite SEO Tools
            </span>
        </h2>
        <p class="content-h mt-3 mb-5 text-slate-600 text-base lg:text-1xl">
            Integrations with your favorite SEO & analytic tools<br class="lg:hidden hidden sm:block"> to help you make the right decisions.
        </p>


        <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 sm:my-10 content-h">

            {{-- AHREF --}}
            <div class="rounded-xl py-6 flex flex-col items-center justify-center shadow 
                        border border-slate-200 hover:shadow-lg">
                <div class="h-16 content-center">
                    <img    height="10px" 
                        width="60px"
                        class=""  
                        src="{{Storage::url('resources/logos-external/ahref.svg')}}">
                </div>
                <p class="font-bold mt-1 text-slate-700">Ahref</p>
                <p class="mt-1 text-sm text-slate-600">SEO Tool</p>
            </div>

            {{-- SEMRUSH --}}
            <div class="rounded-xl py-6 flex flex-col items-center justify-center shadow 
                        border border-slate-200 hover:shadow-lg">
                <div class="h-16 content-center">
                    <img    height="40px" 
                        width="140px"
                        class="" 
                        src="{{Storage::url('resources/logos-external/semrush.png')}}">
                </div>
                <p class="font-bold mt-1 text-slate-700">Semrush</p>
                <p class="mt-1 text-sm text-slate-600">SEO Tool</p>
            </div>

            {{-- SIMILARWEB --}}
            <div class="rounded-xl py-6 flex flex-col items-center justify-center shadow
                        border border-slate-200 align-middle  hover:shadow-lg">

                <div class="h-16 content-center">
                    <img    height="40px" 
                        width="60px"
                        class="" 
                        src="{{Storage::url('resources/logos-external/similarweb.png')}}">
                </div>
                <p class="font-bold mt-1 text-slate-700">SimilarWeb</p>
                <p class="mt-1 text-sm text-slate-600">Market Data</p>
            </div>

            {{-- SERPSTATS --}}
            <div class="rounded-xl py-6 flex flex-col items-center justify-center shadow
                        border border-slate-200 hover:shadow-lg">
                <div class="h-16 content-center">
                    <img    height="40px" 
                        width="100px"
                        class="" 
                        src="{{Storage::url('resources/logos-external/serpstats.svg')}}">
                </div>
                <p class="font-bold mt-1 text-slate-700">SerpStat</p>
                <p class="mt-1 text-sm text-slate-600">SEO Tool</p>
            </div>
            
        </div>
        {{-- <a href="#_" class="px-8 py-4 sm:w-auto w-full text-center text-base font-medium inline-block rounded text-white hover:bg-blue-600 bg-blue-500">View All Integrations</a> --}}
    </div>
</section>
<!----------------------------->
<!-- END: INTEGRATION SECTION-->
<!----------------------------->






<!----------------------------->
<!--      TESTIMONIALS       -->
<!----------------------------->
<section class="bg-slate-50 py-10 px-6 md:px-0 sm:py-20 h-content">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto grid max-w-2xl grid-cols-1 lg:mx-0 lg:max-w-none lg:grid-cols-2">
      <div class="flex flex-col pb-10 sm:pb-16 lg:pb-0 lg:pr-8 xl:pr-20">
        <figure class=" flex flex-auto flex-col justify-between">
          <blockquote class="lg:text-lg leading-8 text-slate-800">
            <p>“Fantastic platform. They did a phenomenal job from keyword research to article creation to finding quality digital PR. Great driver of traffic for us.”</p>
          </blockquote>
          <figcaption class="mt-10 flex items-center gap-x-6">
            <img    class="h-14 w-14 rounded-full bg-slate-50" 
                    src="{{Storage::url('resources/avatars/testm-ben.jpg')}}" alt="avatar">
            <div class="text-base">
              <div class="font-semibold text-slate-800">Judith Black</div>
              <div class="mt-1 text-slate-500">CEO of Tech Startup</div>
            </div>
          </figcaption>
        </figure>
      </div>
      <div class="flex flex-col border-t border-slate-900/10 pt-10 sm:pt-16 lg:border-l lg:border-t-0 lg:pl-8 lg:pt-0 xl:pl-20">
        <figure class=" flex flex-auto flex-col justify-between">
          <blockquote class="lg:text-lg leading-8 text-slate-800">
            <p>“Signed up and love the concept, made me a big believer in organic search. Looking forward to seeing more articles in the fintech and private debt space!”</p>
          </blockquote>
          <figcaption class="mt-10 flex items-center gap-x-6">
            <img    class="h-14 w-14 rounded-full bg-slate-50" 
                    src="{{Storage::url('resources/avatars/testm-nathan.png')}}" alt="avatar">
            <div class="text-base">
              <div class="font-semibold text-slate-800">Joseph Rodriguez</div>
              <div class="mt-1 text-slate-500">CEO of Healthcare Products</div>
            </div>
          </figcaption>
        </figure>
      </div>
    </div>
  </div>
</section>
<!----------------------------->
<!----- END: TESTIMONIALS ----->
<!----------------------------->


<!----------------------------->
<!-------- LOGO CLOUD --------->
<!----------------------------->
{{-- 
<div class="py-8">
    <x-public.logo-cloud/>
</div>
--}}
<!----------------------------->
<!------ END: LOGO CLOUD ------>
<!----------------------------->


<!----------------------------->
<!----------- FAQS ------------>
<!----------------------------->
<section class="mt-10 md:mt-16">
    <div class="max-w-[85rem] px-10 md:px-6 py-8 md:py-16 sm:px-6 lg:px-8 lg:py-14 mx-auto">
      <!-- Grid -->
      <div class="grid md:grid-cols-5 gap-10">
        <div class="md:col-span-2">
          <div class="max-w-xs">
            <h2 class="title-h text-2xl font-bold md:text-4xl md:leading-tight dark:text-white">
                Frequently<br>asked questions
            </h2>
            <p class="content-h mt-4 hidden md:block text-slate-600 dark:text-neutral-400">
                Answers to the most frequently asked questions.
            </p>
          </div>
        </div>
        <!-- End Col -->

        <div class="md:col-span-3 content-h" x-data="{activeAccordion: 1}">
          <!-- Accordion -->
          <div class="hs-accordion-group divide-y divide-slate-200 dark:divide-neutral-700">

            <div x-data="{id: 1}" class="hs-accordion pb-3 active" id="hs-basic-with-title-and-arrow-stretched-heading-one">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-expanded="true" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-one">
                What is PressBear?
                 <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                <p class="text-slate-600 dark:text-neutral-400">
                 PressBear is a digital marketing platform that connects brands with leading media outlets for sponsorships and link-building opportunities. Our platform empowers you to enhance your brand's online presence and boost your SEO.
                </p>
              </div>
            </div>

            <div x-data="{id: 2}" class="hs-accordion pt-6 pb-3">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400">
                How to get started?                
                <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                <p class="text-slate-600 dark:text-neutral-400">
                 Sign up, filter, and discover websites that match your criteria, then place your order. You can either provide your own content or simply let us know what you need, and our team will handle the rest.
                </p>
              </div>
            </div>

            <div x-data="{id: 3}" class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-three">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-three">
                Why use PressBear?
                <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
                
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                <p class="text-slate-600 dark:text-neutral-400">
                  We streamline the entire process for you. No more manual outreach or managing bulky Google sheets—find the right websites with just a few clicks, and we’ll handle the entire publishing process. With our platform, you benefit from better-negotiated rates with publishers and enjoy secure, reliable placements.
                </p>
              </div>
            </div>

            <div x-data="{id: 4}" class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-four">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-four">
                Can you write content for me?
                <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                <p class="text-slate-600 dark:text-neutral-400">
                  Yes, we offer complimentary content writing with every publication. Simply share your topic, target link, anchor text, and goals, and we'll handle the rest. Alternatively, you're welcome to provide your own content.
                </p>
              </div>
            </div>

            <div x-data="{id: 5}" class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-five">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-five">
                Is publication guaranteed?
                 <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                <p class="text-slate-600 dark:text-neutral-400">
                  We do our utmost to ensure every article gets published, but if any issues arise, we will either refund 100% of your payment or provide alternative options.
                </p>
              </div>
            </div>

            <div x-data="{id: 6}" class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-six">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-six">
                How much it cost to publish on a website?
                 <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region">
                <p class="text-slate-600 dark:text-neutral-400">
                    Each website has its own pricing based on popularity and metrics. You can choose the site that best fits your requirements and budget.
                </p>
              </div>
            </div>

             <div x-data="{id: 7}" class="hs-accordion pt-6 pb-3" id="hs-basic-with-title-and-arrow-stretched-heading-six">
              <button @click="activeAccordion = id" class="hs-accordion-toggle group pb-3 inline-flex items-center justify-between gap-x-3 w-full md:text-lg font-semibold text-start text-slate-800 rounded-lg transition hover:text-slate-500 focus:outline-none focus:text-slate-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-expanded="false" aria-controls="hs-basic-with-title-and-arrow-stretched-collapse-six">
                What industries do you support?
                 <x-icons.animated.toggle-down class="w-6 h-6 block shrink-0 size-5 text-slate-600 
                                                group-hover:text-slate-500 dark:text-neutral-400" />
              </button>
              <div x-cloak x-show="activeAccordion == id" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300" role="region">
                <p class="text-slate-600 dark:text-neutral-400">
                    We support a wide range of industries, but each publisher has specific requirements and restrictions. Additionally, pricing may vary for sensitive niches such as betting, adult, dating, and others.
                </p>
              </div>
            </div>
          </div>
          <!-- End Accordion -->
        </div>
        <!-- End Col -->
      </div>
      <!-- End Grid -->
    </div>
</section>
<!----------------------------->
<!-------- END: FAQS ---------->
<!----------------------------->




{{-- <section class="bg-orange-100/30 py-24 ">
  <div class="container">
    <div class="lg:px-20">
      <div class="text-center flex flex-col items-center">
        <h2 class="mb-6 text-3xl title-h font-bold">Customer support is always here to help you</h2>
        <p class="text-xl mx-auto max-w-screen-md leading-relaxed">We work around the clock to assist you. Drop us a message any time, and one of us will be happy to get back to you quickly!</p>
        <div class="grid grid-cols-2 sm:grid-cols-3 max-w-screen-lg sm:divide-x-2 divide-black divide-opacity-10 mx-auto mt-10">
          <div class="flex flex-col p-4">
            <span class="text-2xl lg:text-xl font-bold">24/7</span>
            <span class="opacity-50 leading-none text-sm">always available</span>
          </div>
          <div class="flex flex-col p-4">
            <span class="text-2xl lg:text-xl font-bold">97%</span>
            <span class="opacity-50 leading-none text-sm">satisfaction rate</span>
          </div>
          <div class="col-span-2 sm:col-span-1 flex flex-col p-4">
            <span class="text-2xl lg:text-xl font-bold">3 hour</span>
            <span class="opacity-50 leading-none text-sm">avg. response time</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
 --}}

<!----------------------------->
<!---- ACTION: GROW BRAND ----->
<!----------------------------->
<section class="overflow-hidden">
  <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20">
    <div class="relative mx-auto max-w-4xl grid space-y-5 sm:space-y-10">

        <!-- Title -->
        <div class="text-center title-h">
            <p class="text-xs font-semibold text-slate-500 tracking-wide uppercase mb-3 dark:text-slate-200">
              Grow Your Online Prescence
            </p>
            <h1 class="text-3xl text-slate-700 font-bold sm:text-5xl  lg:leading-tight dark:text-slate-200">
              Grow Your Brand <span class="text-orange-500">Organically</span>
        </h1>
        </div>
        <!-- End Title -->

        <!-- Avatar Group -->
        <div class="sm:flex sm:justify-center sm:items-center text-center sm:text-left">
            <div class="flex-shrink-0 pb-5 sm:flex sm:pb-0 sm:pr-5">
              <!-- Avatar Group -->
              <div class="flex justify-center -space-x-3">
                <img class="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-slate-800" src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=facearea&amp;facepad=2&amp;w=300&amp;h=300&amp;q=80" alt="Image Description">
                <img class="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-slate-800" src="https://images.unsplash.com/photo-1531927557220-a9e23c1e4794?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=facearea&amp;facepad=2&amp;w=300&amp;h=300&amp;q=80" alt="Image Description">
                <img class="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-slate-800" src="https://images.unsplash.com/photo-1541101767792-f9b2b1c4f127?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;&amp;auto=format&amp;fit=facearea&amp;facepad=3&amp;w=300&amp;h=300&amp;q=80" alt="Image Description">
                <img class="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-slate-800" src="https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=facearea&amp;facepad=2&amp;w=300&amp;h=300&amp;q=80" alt="Image Description">
                <span class="inline-flex items-center justify-center h-8 w-8 rounded-full ring-2 ring-white bg-slate-800 dark:bg-slate-900 dark:ring-slate-800">
                  <span class="text-xs font-medium leading-none text-white uppercase">1k+</span>
                </span>
              </div>
              <!-- End Avatar Group -->
            </div>

            <div class="border-t sm:border-t-0 sm:border-l border-slate-200 w-32 h-px sm:w-auto sm:h-full mx-auto sm:mx-0"></div>

            <div class="pt-5 sm:pt-0 sm:pl-5 content-h">
              <div class="text-lg font-semibold text-slate-800 dark:text-slate-200">Trusted Platform</div>
              <div class="text-sm text-slate-500">Trusted by over 1k Customers</div>
            </div>
        </div>
        <!-- End Avatar Group -->


        <div class="pt-2 flex w-full mx-auto items-center">
            <a class="mx-auto py-3 px-4 inline-flex justify-center items-center gap-2 rounded-full border border-transparent font-semibold bg-gradient-to-br from-slate-600 to-slate-800 hover:from-slate-700 hover:to-slate-800 text-white hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-all w-2/3 md:w-1/3 text-base sm:p-4 dark:focus:ring-offset-slate-800" href="{{ route('register') }}">
              Get Started
            </a>
        </div>

        <!-- Form -->
        {{-- <form>
        <div class="mx-auto max-w-2xl sm:flex sm:space-x-3 p-3 bg-white border rounded-lg shadow-lg shadow-slate-100 dark:bg-slate-900 dark:border-slate-700 dark:shadow-slate-900/[.2]">
          <div class="pb-2 sm:pb-0 sm:flex-[1_0_0%]">
            <label for="hs-hero-name-1" class="block text-sm font-medium dark:text-white"><span class="sr-only">Your name</span></label>
            <input type="text" id="hs-hero-name-1" class="py-3 px-4 block w-full border-transparent rounded-md text-sm focus:border-blue-500 focus:ring-blue-500 sm:p-4 dark:bg-slate-900 dark:border-transparent dark:text-slate-400" placeholder="Your name">
          </div>
          <div class="pt-2 sm:pt-0 sm:pl-3 border-t border-slate-200 sm:border-t-0 sm:border-l sm:flex-[1_0_0%] dark:border-slate-700">
            <label for="hs-hero-email-1" class="block text-sm font-medium dark:text-white"><span class="sr-only">Your email address</span></label>
            <input type="email" id="hs-hero-email-1" class="py-3 px-4 block w-full border-transparent rounded-md text-sm focus:border-blue-500 focus:ring-blue-500 sm:p-4 dark:bg-slate-900 dark:border-transparent dark:text-slate-400" placeholder="Your email">
          </div>
          <div class="pt-2 sm:pt-0 grid sm:block sm:flex-[0_0_auto]">
            <a class="py-3 px-4 inline-flex justify-center items-center gap-2 rounded-md border border-transparent font-semibold bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all text-sm sm:p-4 dark:focus:ring-offset-slate-800" href="#">
              Get started
            </a>
          </div>
        </div>
        </form> --}}
        <!-- End Form -->

      <!-- SVG Element -->
      <div class="hidden absolute top-2/4 left-0 transform -translate-y-2/4 -translate-x-40 md:block lg:-translate-x-80" aria-hidden="true">
        <svg class="w-52 h-auto" width="717" height="653" viewBox="0 0 717 653" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path class="fill-slate-800" d="M170.176 228.357C177.176 230.924 184.932 227.329 187.498 220.329C190.064 213.329 186.47 205.574 179.47 203.007L170.176 228.357ZM98.6819 71.4156L85.9724 66.8638L85.8472 67.2136L85.7413 67.5698L98.6819 71.4156ZM336.169 77.9736L328.106 88.801L328.288 88.9365L328.475 89.0659L336.169 77.9736ZM616.192 128.685C620.658 122.715 619.439 114.254 613.469 109.788L516.183 37.0035C510.213 32.5371 501.753 33.756 497.286 39.726C492.82 45.696 494.039 54.1563 500.009 58.6227L586.485 123.32L521.788 209.797C517.322 215.767 518.541 224.227 524.511 228.694C530.481 233.16 538.941 231.941 543.407 225.971L616.192 128.685ZM174.823 215.682C179.47 203.007 179.475 203.009 179.48 203.011C179.482 203.012 179.486 203.013 179.489 203.014C179.493 203.016 179.496 203.017 179.498 203.018C179.501 203.019 179.498 203.018 179.488 203.014C179.469 203.007 179.425 202.99 179.357 202.964C179.222 202.912 178.991 202.822 178.673 202.694C178.035 202.437 177.047 202.026 175.768 201.456C173.206 200.314 169.498 198.543 165.106 196.099C156.27 191.182 144.942 183.693 134.609 173.352C114.397 153.124 97.7311 122.004 111.623 75.2614L85.7413 67.5698C68.4512 125.748 89.856 166.762 115.51 192.436C128.11 205.047 141.663 213.953 151.976 219.692C157.158 222.575 161.591 224.698 164.777 226.118C166.371 226.828 167.659 227.365 168.578 227.736C169.038 227.921 169.406 228.065 169.675 228.168C169.809 228.22 169.919 228.261 170.002 228.293C170.044 228.309 170.08 228.322 170.109 228.333C170.123 228.338 170.136 228.343 170.147 228.347C170.153 228.349 170.16 228.352 170.163 228.353C170.17 228.355 170.176 228.357 174.823 215.682ZM111.391 75.9674C118.596 55.8511 137.372 33.9214 170.517 28.6833C204.135 23.3705 255.531 34.7533 328.106 88.801L344.233 67.1462C268.876 11.0269 210.14 -4.91361 166.303 2.01428C121.993 9.01681 95.9904 38.8917 85.9724 66.8638L111.391 75.9674ZM328.475 89.0659C398.364 137.549 474.018 153.163 607.307 133.96L603.457 107.236C474.34 125.837 406.316 110.204 343.864 66.8813L328.475 89.0659Z" fill="currentColor" class="fill-slate-800 dark:fill-white"></path>
          <path d="M17.863 238.22C10.4785 237.191 3.6581 242.344 2.62917 249.728C1.60024 257.113 6.75246 263.933 14.137 264.962L17.863 238.22ZM117.548 265.74L119.421 252.371L119.411 252.37L117.548 265.74ZM120.011 466.653L132.605 471.516L132.747 471.147L132.868 470.771L120.011 466.653ZM285.991 553.767C291.813 549.109 292.756 540.613 288.098 534.792L212.193 439.92C207.536 434.098 199.04 433.154 193.218 437.812C187.396 442.47 186.453 450.965 191.111 456.787L258.582 541.118L174.251 608.589C168.429 613.247 167.486 621.742 172.143 627.564C176.801 633.386 185.297 634.329 191.119 629.672L285.991 553.767ZM14.137 264.962L115.685 279.111L119.411 252.37L17.863 238.22L14.137 264.962ZM115.675 279.11C124.838 280.393 137.255 284.582 145.467 291.97C149.386 295.495 152.093 299.505 153.39 304.121C154.673 308.691 154.864 314.873 152.117 323.271L177.779 331.665C181.924 318.993 182.328 307.301 179.383 296.818C176.451 286.381 170.485 278.159 163.524 271.897C149.977 259.71 131.801 254.105 119.421 252.371L115.675 279.11ZM152.117 323.271C138.318 365.454 116.39 433.697 107.154 462.535L132.868 470.771C142.103 441.936 164.009 373.762 177.779 331.665L152.117 323.271ZM107.417 461.79C103.048 473.105 100.107 491.199 107.229 508.197C114.878 526.454 132.585 539.935 162.404 543.488L165.599 516.678C143.043 513.99 135.175 505.027 132.132 497.764C128.562 489.244 129.814 478.743 132.605 471.516L107.417 461.79ZM162.404 543.488C214.816 549.734 260.003 554.859 276.067 556.643L279.047 529.808C263.054 528.032 217.939 522.915 165.599 516.678L162.404 543.488Z" fill="currentColor" 
          class="fill-orange-500"></path>
          <path d="M229.298 165.61C225.217 159.371 216.85 157.621 210.61 161.702C204.371 165.783 202.621 174.15 206.702 180.39L229.298 165.61ZM703.921 410.871C711.364 410.433 717.042 404.045 716.605 396.602L709.47 275.311C709.032 267.868 702.643 262.189 695.2 262.627C687.757 263.065 682.079 269.454 682.516 276.897L688.858 384.71L581.045 391.052C573.602 391.49 567.923 397.879 568.361 405.322C568.799 412.765 575.187 418.444 582.63 418.006L703.921 410.871ZM206.702 180.39C239.898 231.14 343.567 329.577 496.595 322.758L495.394 295.785C354.802 302.049 259.09 211.158 229.298 165.61L206.702 180.39ZM496.595 322.758C567.523 319.598 610.272 335.61 637.959 353.957C651.944 363.225 662.493 373.355 671.17 382.695C675.584 387.447 679.351 391.81 683.115 396.047C686.719 400.103 690.432 404.172 694.159 407.484L712.097 387.304C709.691 385.166 706.92 382.189 703.298 378.113C699.837 374.217 695.636 369.362 690.951 364.319C681.43 354.07 669.255 342.306 652.874 331.451C619.829 309.553 571.276 292.404 495.394 295.785L496.595 322.758Z" fill="currentColor" class="fill-emerald-600"></path>
        </svg>
      </div>
      <!-- End SVG Element -->

      <!-- SVG Element -->
      <div class="hidden absolute top-2/4 right-0 transform -translate-y-2/4 translate-x-40 md:block lg:translate-x-80" aria-hidden="true">
        <svg class="w-72 h-auto" width="1115" height="636" viewBox="0 0 1115 636" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.990203 279.321C-1.11035 287.334 3.68307 295.534 11.6966 297.634L142.285 331.865C150.298 333.965 158.497 329.172 160.598 321.158C162.699 313.145 157.905 304.946 149.892 302.845L33.8132 272.418L64.2403 156.339C66.3409 148.326 61.5475 140.127 53.5339 138.026C45.5204 135.926 37.3213 140.719 35.2207 148.733L0.990203 279.321ZM424.31 252.289C431.581 256.26 440.694 253.585 444.664 246.314C448.635 239.044 445.961 229.931 438.69 225.96L424.31 252.289ZM23.0706 296.074C72.7581 267.025 123.056 230.059 187.043 212.864C249.583 196.057 325.63 198.393 424.31 252.289L438.69 225.96C333.77 168.656 249.817 164.929 179.257 183.892C110.144 202.465 54.2419 243.099 7.92943 270.175L23.0706 296.074Z" fill="currentColor" class="fill-orange-500"></path>
          <path d="M451.609 382.417C446.219 388.708 446.95 398.178 453.241 403.567L555.763 491.398C562.054 496.788 571.524 496.057 576.913 489.766C582.303 483.474 581.572 474.005 575.281 468.615L484.15 390.544L562.222 299.413C567.612 293.122 566.881 283.652 560.59 278.263C554.299 272.873 544.829 273.604 539.44 279.895L451.609 382.417ZM837.202 559.655C841.706 566.608 850.994 568.593 857.947 564.09C864.9 559.586 866.885 550.298 862.381 543.345L837.202 559.655ZM464.154 407.131C508.387 403.718 570.802 395.25 638.136 410.928C704.591 426.401 776.318 465.66 837.202 559.655L862.381 543.345C797.144 442.631 718.724 398.89 644.939 381.709C572.033 364.734 504.114 373.958 461.846 377.22L464.154 407.131Z" fill="currentColor" class="fill-emerald-600"></path>
          <path d="M447.448 0.194357C439.203 -0.605554 431.87 5.43034 431.07 13.6759L418.035 148.045C417.235 156.291 423.271 163.623 431.516 164.423C439.762 165.223 447.095 159.187 447.895 150.942L459.482 31.5025L578.921 43.0895C587.166 43.8894 594.499 37.8535 595.299 29.6079C596.099 21.3624 590.063 14.0296 581.818 13.2297L447.448 0.194357ZM1086.03 431.727C1089.68 439.166 1098.66 442.239 1106.1 438.593C1113.54 434.946 1116.62 425.96 1112.97 418.521L1086.03 431.727ZM434.419 24.6572C449.463 42.934 474.586 81.0463 521.375 116.908C568.556 153.07 637.546 187.063 742.018 200.993L745.982 171.256C646.454 157.985 582.444 125.917 539.625 93.0974C496.414 59.978 474.537 26.1903 457.581 5.59138L434.419 24.6572ZM742.018 200.993C939.862 227.372 1054.15 366.703 1086.03 431.727L1112.97 418.521C1077.85 346.879 956.138 199.277 745.982 171.256L742.018 200.993Z" fill="currentColor" class="fill-slate-800 dark:fill-white"></path>
        </svg>
      </div>
      <!-- End SVG Element -->
    </div>
  </div>
</section>



{{-- Changing Words --}}
<script>
    const words = [
        { text: "Sponsorships", className: "text-design" },
        { text: "Link building", className: "text-design" },
        { text: "Press releases", className: "text-design" },
    ];
    let index = 0;

    function swapWord() {
        const changingWord = document.getElementById("changing-word");
        changingWord.style.opacity = 0;

        setTimeout(() => {
            index = (index + 1) % words.length;
            changingWord.textContent = words[index].text;
            changingWord.className = `moving-element ${words[index].className}`;
            changingWord.style.opacity = 1;
        }, 1000); // Match this with the CSS transition duration
    }

    setInterval(swapWord, 3500); // Change every 3 seconds
</script>



{{-- Image hero prespective change --}}
<script>
    const image = document.querySelector('.tilted-image');
    const imageContainer = document.querySelector('.image-container');

    // Function to handle scroll and adjust the tilt angle dynamically
    function handleScroll() {
        const rect = imageContainer.getBoundingClientRect(); // Get the position of the image container

        const imageHeight = rect.height;
        const windowHeight = window.innerHeight;
        
        // Calculate the distance from the center of the viewport
        const distanceFromCenter = (rect.top + imageHeight / 2) - (windowHeight / 2);

        // Normalize the distance to a range between -1 and 1
        const normalizedDistance = distanceFromCenter / (windowHeight / 2);

        // Set the rotation angle based on normalized distance (from -30deg to 0deg)
        const rotationAngle = Math.max(Math.min(normalizedDistance * 10, 10), 0);

        // Apply the rotation to the image
        image.style.transform = `rotateX(${rotationAngle}deg)`;
    }

    // Listen for scroll events
    window.addEventListener('scroll', handleScroll);

    // Run the function initially to set the tilt on page load
    handleScroll();
</script>



{{-- Image hero prespective change --}}
<style type="text/css">
       .image-container {
            perspective: 1000px; /* Depth perspective for 3D effect */
        }

        .tilted-image {
            will-change: transform;
            transition: transform 0.1s ease; /* Smooth transition for rotation */
            transform-origin: center center; 
            transform: rotateX(10deg) ; /* Initial backward tilt */
                transform-style: preserve-3d;
        }

        .in-view {
            transform: rotateX(0deg); /* Reset to normal position */
        }
</style>




<style type="text/css">
    
    #changing-word {
        transition: opacity 1s ease-in-out;
    }

    .feature-card {
        padding: 24px 28px;
        box-sizing: border-box;
/*        border-radius: 10px;*/
        position: relative;
/*        border: 1px solid #d1d4db;*/
        display: flex;
        flex-direction: column;
        transition: all .2s ease-in-out;
        height: 100%;
    }

    .feature-card__title {
        align-self: flex-start;
        margin-bottom: 12px;
        line-height: 1.3;
        transition: color .2s ease-in-out;
    }


    .feature-card__footer {
        margin-top: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .feature-card:hover, .feature-card:focus {
        outline: none;
        box-shadow: 5px 5px 25px rgba(137,141,154,.3);
    }

/*    .feature-card:hover .feature-card__title, .feature-card:hover 
    .feature-card__footer-link, .feature-card:focus .feature-card__title, 
    .feature-card:focus .feature-card__footer-link {
        color: #ff622d;
    }*/

/*    .feature-card__count {
        display: block;
        padding: 5px 10px 7px;
        border-radius: 6px;
        align-self: flex-start;
        font-size: 14px;
        line-height: 17px;
    }*/


    .interests__cards {
        gap: 32px;
        grid-template-rows: auto;
        gap: 24px;
        margin: 0px auto 60px auto;
/*        max-width: 1200px;*/
    }

</style>


@endsection