@extends('layouts.public')

@section('content')




<table class="mt-16 mx-auto border-2 p-6">
  <tr class="row-table">

      <td class="buy-quantity sticky left-0 bg-white">
        <div class="px-6 py-4">
          <x-examples.alpine.quantity-input />
        </div>
      </td>

      <td class="price ">
        <div class="w-auto px-6 py-4">
          <span class="text-sm text-gray-800 font-medium dark:text-gray-800">
            ${{rand(100,500);}}
          </span>
        </div>
      </td>

      <td class="domain font-medium">
        <div class="px-6 py-4">
          <div class="flex items-center">
              <img width="16px" height="16px" src="https://www.google.com/s2/favicons?sz=64&domain_url=Guideline.com"  alt="" class="w-5 h-5 border-2 rounded-full mr-2">
              <div class="">
                  <span class="text-sm text-gray-800 dark:text-gray-400">
                      Guideline.com
                  </span>
              </div>
              <div class="ml-2 external-link-icon">
              <a target="_blank" href="#">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link">
                  <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/><polyline points="15 3 21 3 21 9"/><line x1="10" x2="21" y1="14" y2="3"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </td>

      <td class="quality-score">
        <div class="px-6 py-4">
          <div class="flex items-center text-sm">
            <div class="inline-block w-3 h-3 mr-2 bg-green-500 rounded-full"></div>
             <span class="text-gray-800">24</span>
          </div>
        </div>
      </td>

      <td class="country">
        <div class="px-6 py-4">
          <span>
            <img class="w-5 h-5 inline mr-1 rounded-lg" src="{{asset('graphics/country-flags/in.svg');}}">
          </span>
          <span class="text-sm text-gray-800 dark:text-gray-400">India</span>
        </div>
      </td>

      <td class="traffic px-6 py-4">
          <span class="text-sm text-gray-800 dark:text-gray-400">45m</span>
      </td>

      <td class=" ">
        <div class="px-6 py-4">
          <span class="text-sm text-gray-800 dark:text-gray-400">
            French
          </span>
        </div>
      </td>

      <td class="stars-rating">
        <div class="px-6 py-4 flex gap-x-1">
          <svg class="w-3 h-3 text-gray-800 dark:text-gray-800" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
          </svg>
          <svg class="w-3 h-3 text-gray-800 dark:text-gray-800" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
          </svg>
          <svg class="w-3 h-3 text-gray-800 dark:text-gray-800" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
          </svg>
          <svg class="w-3 h-3 text-gray-800 dark:text-gray-800" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
          </svg>
          <svg class="w-3 h-3 text-gray-300 dark:text-gray-800" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
          </svg>
        </div>
      </td>

      <td class="value">
        <div class="px-6 py-3">
          <div class="flex items-center gap-x-3">
            <span class="text-xs text-gray-800">3/5</span>
            <div class="flex w-full h-1.5 bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700">
              <div class="flex flex-col justify-center overflow-hidden bg-gray-800 dark:bg-gray-200" role="progressbar" style="width: 78%" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
          </div>
        </div>
      </td>

      <td class="status">
        <div class="px-6 py-3">
          <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <x-icons.etc.check />
            Active
          </span>
        </div>
      </td>

      <td class="dr flex p-4 align-middle pr-0">
          <div class="inline-flex items-center justify-center rounded-full font-medium cursor-default bg-gray-100 px-2.5 py-0.5 text-gray-800 text-xs">94</div>
      </td>

      <td class="p-4 align-middle pr-0">
          <div class="inline-flex items-center justify-center rounded-full font-medium cursor-default bg-gray-100 px-2.5 py-0.5 text-gray-800 text-xs">84</div>
      </td>



      <td class="p-4 align-middle pr-0">
          <div class="inline-flex items-center border border-orange-800 rounded-full px-2.5 py-[2px] text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-orange-800">NoFollow</div>
      </td>


      <td class="category">
        <a target="_blank" class="" href="#">
          <div class="px-4 py-4">
            <span class="whitespace-nowrap inline-flex items-center gap-1.5 px-3 py-2 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
              Lifestyle
            </span>
          </div>
        </a>
      </td>

      <td class="topics">
        <a target="_blank" class="block relative z-10" href="#">
          <div class="px-6 py-4">
            <div class="block text-sm text-gray-800 decoration-2 hover:underline dark:text-gray-800">#news</div>
          </div>
        </a>
      </td>

      <td class="action-options-website">
        <div class="px-6 py-4">

          <div x-data="{display: false}" class="hs-dropdown relative inline-block [--placement:bottom-right]">

            <button x-on:click="display =! display" id="hs-table-dropdown-1" type="button" 
            class="hs-dropdown-toggle py-1.5 px-2 inline-flex justify-center items-center gap-2 text-gray-800 align-middle focus:ring-2 focus:ring-offset-2 focus:ring-gray-200 transition-all text-sm dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-400 focus:bg-gray-100 hover:bg-gray-100 rounded-md">
              <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
              </svg>
            </button>

            <div  @click.away="display=false"
                  x-transition:enter="ease-out duration-200"
                  x-transition:enter-start="-translate-y-2"
                  x-transition:enter-end="translate-y-0" 

                  x-show="display" 
                  x-cloack

                  class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100  mt-2 divide-y divide-gray-200 min-w-[12rem] z-40 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-gray-700 dark:bg-gray-800 dark:border dark:border-gray-700 absolute" 

                  aria-labelledby="hs-table-dropdown-1">
              

              <div class="py-2 first:pt-0 last:pb-0">
                <span class="block py-2 px-3 text-xs font-medium uppercase text-gray-400 dark:text-gray-800">
                  Actions
                </span>
                <a target="_blank" class="flex items-center gap-x-3 py-2 px-3 rounded-md font-medium text-sm text-gray-800 hover:bg-gray-100 hover:text-gray-800 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">
                  {{-- icon --}}
                 <x-icons.iconly.cart />
                  Add To Cart
                </a>
                <a target="_blank" class="flex items-center gap-x-3 py-2 px-3 rounded-md font-medium text-sm text-gray-800 hover:bg-gray-100 hover:text-gray-800 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">

                  <x-icons.iconly.star />

                  Add To Favorites
                </a>
              </div>
              <div class="py-2 first:pt-0 last:pb-0">
                <a target="_blank" class="flex items-center gap-x-3 py-2 px-3 rounded-md font-medium text-sm text-red-600 hover:bg-gray-100 hover:text-red-700 focus:ring-2 focus:ring-gray-500 dark:text-red-500 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">
                  <x-icons.iconly.flag />
                  Report Site
                </a>
              </div>
            </div>
          </div>
        </div>
      </td>

  </tr>
</table>


              


<style>

  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --card: 0 0% 100%;
    --card-border: 214.3 31.8% 91.4%;
    --card-border-tint: 112 205 159;
    --card-foreground: 222.2 47.4% 11.2%;
    --field-card: 95 74% 90%;
    --field-card-border: 95.08 71.08% 67.45%;
    --field-card-foreground: 222.2 47.4% 11.2%;
    --widget: 0 0% 97%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --primary: 95.08 71.08% 67.45%;
    --primary-foreground: 95.08 71.08% 10%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 95.08 71.08% 67.45%;
    --radius: 0.5rem;
    --warning: 54 96% 45%;
}

.bg-primary {
    background-color: hsl(var(--primary));
}

.text-documenso-800 {
    --tw-text-opacity: 1;
    color: rgb(77 150 25 / var(--tw-text-opacity));
}


.ring-offset-background {
    --tw-ring-offset-color: hsl(var(--background));
}


</style>

<main class="mt-8 pb-8 md:mt-12 md:pb-12">
  <div class="mx-auto w-full max-w-screen-xl px-4 md:px-8">
    <div class="relative">
      <div style="transform: none;">
        <div class="bg-background text-foreground gradient-border-mask dark:gradient-border-mask focus-visible:ring-ring ring-offset-background group group relative flex h-[min(400px,50vh)] flex-1 cursor-pointer flex-col items-center justify-center rounded-lg border-2 shadow-[0_0_0_4px_theme(colors.gray.100/70%),0_0_0_1px_theme(colors.gray.100/70%),0_0_0_0.5px_theme(colors.primary.DEFAULT/70%)] backdrop-blur-[2px] before:pointer-events-none before:absolute before:-inset-[2px] before:rounded-lg before:p-[2px] before:[background:linear-gradient(var(--card-gradient-degrees),theme(colors.documenso.DEFAULT/70%)_5%,theme(colors.border/80%)_30%)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 dark:shadow-[0]" role="presentation" aria-disabled="false" tabindex="0" style="--card-gradient-degrees: 120deg;">
          <div class="text-muted-foreground/40 flex flex-col items-center justify-center p-6">
            <div class="flex">
              <div class="border-muted-foreground/20 group-hover:border-documenso/80 dark:bg-muted/80 a z-10 flex aspect-[3/4] w-24 origin-top-right -rotate-[22deg] flex-col gap-y-1 rounded-lg border bg-white/80 px-2 py-4 backdrop-blur-sm" style="transform: translateX(40px) translateY(-10px) rotate(-14deg) translateZ(0px);">
                <div class="bg-muted-foreground/20 group-hover:bg-documenso h-2 w-full rounded-[2px]"></div>
                <div class="bg-muted-foreground/20 group-hover:bg-documenso h-2 w-5/6 rounded-[2px]"></div>
                <div class="bg-muted-foreground/20 group-hover:bg-documenso h-2 w-full rounded-[2px]"></div>
              </div>
              <div class="border-muted-foreground/20 group-hover:border-documenso/80 dark:bg-muted/80 z-20 flex aspect-[3/4] w-24 flex-col items-center justify-center gap-y-1 rounded-lg border bg-white/80 px-2 py-4 backdrop-blur-sm" style="transform: none;">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2px" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground/20 group-hover:text-documenso h-12 w-12">
                  <path d="M5 12h14"></path>
                  <path d="M12 5v14"></path>
                </svg>
              </div>
              <div class="border-muted-foreground/20 group-hover:border-documenso/80 dark:bg-muted/80 z-10 flex aspect-[3/4] w-24 origin-top-left rotate-[22deg] flex-col gap-y-1 rounded-lg border bg-white/80 px-2 py-4 backdrop-blur-sm" style="transform: translateX(-40px) translateY(-10px) rotate(14deg) translateZ(0px);">
                <div class="bg-muted-foreground/20 group-hover:bg-documenso h-2 w-full rounded-[2px]"></div>
                <div class="bg-muted-foreground/20 group-hover:bg-documenso h-2 w-5/6 rounded-[2px]"></div>
                <div class="bg-muted-foreground/20 group-hover:bg-documenso h-2 w-full rounded-[2px]"></div>
              </div>
            </div>
            <input accept="application/pdf,.pdf" tabindex="-1" type="file" style="display: none;" />
            <p class="text-foreground mt-8 font-medium">Add a document</p>
            <p class="text-muted-foreground/80 mt-1 text-center text-sm">Drag & drop your PDF here.</p>
          </div>
        </div>
      </div>
      <div class="absolute -bottom-6 right-0"><p class="text-muted-foreground/60 text-xs">4 of 5 documents remaining this month.</p></div>
    </div>
    <div class="mt-12 flex flex-wrap items-center justify-between gap-x-4 gap-y-8">
      <div class="flex flex-row items-center"><h1 class="text-4xl font-semibold">Documents</h1></div>
      <div class="-m-1 flex flex-wrap gap-x-4 gap-y-6 overflow-hidden p-1">
        <div dir="ltr" data-orientation="horizontal" class="overflow-x-auto">
          <div role="tablist" aria-orientation="horizontal" class="bg-muted text-muted-foreground inline-flex h-10 items-center justify-center rounded-md p-1" tabindex="0" data-orientation="horizontal" style="outline: none;">
            <a type="button" role="tab" aria-selected="false" aria-controls="radix-:rb5:-content-INBOX" data-state="inactive" id="radix-:rb5:-trigger-INBOX" class="ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground hover:text-foreground inline-flex min-w-[60px] items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="" href="/documents?status=INBOX"
              ><span class="flex items-center"
                ><svg width="24" height="24" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-muted-foreground mr-2 inline-block h-4 w-4"><path d="M1.5 11H14.5M1.5 14C1.5 14 8.72 2 4.86938 2H4.875C2.01 2 1.97437 14.0694 8 6.51188V6.5C8 6.5 9 11.3631 11.5 7.52375V7.5C11.5 7.5 11.5 9 14.5 9" stroke="currentColor" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"></path></svg>Inbox</span
              ><span class="ml-1 inline-block opacity-50">0</span></a
            ><a type="button" role="tab" aria-selected="false" aria-controls="radix-:rb5:-content-PENDING" data-state="inactive" id="radix-:rb5:-trigger-PENDING" class="ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground hover:text-foreground inline-flex min-w-[60px] items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="" href="/documents?status=PENDING"
              ><span class="flex items-center"
                ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 inline-block h-4 w-4 text-blue-600 dark:text-blue-300">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline></svg
                >Pending</span
              ><span class="ml-1 inline-block opacity-50">0</span></a
            ><a type="button" role="tab" aria-selected="false" aria-controls="radix-:rb5:-content-COMPLETED" data-state="inactive" id="radix-:rb5:-trigger-COMPLETED" class="ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground hover:text-foreground inline-flex min-w-[60px] items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="" href="/documents?status=COMPLETED"
              ><span class="flex items-center"
                ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 inline-block h-4 w-4 text-green-500 dark:text-green-300">
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                  <path d="m9 12 2 2 4-4"></path></svg
                >Completed</span
              ><span class="ml-1 inline-block opacity-50">1</span></a
            ><a type="button" role="tab" aria-selected="false" aria-controls="radix-:rb5:-content-DRAFT" data-state="inactive" id="radix-:rb5:-trigger-DRAFT" class="ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground hover:text-foreground inline-flex min-w-[60px] items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="" href="/documents?status=DRAFT"
              ><span class="flex items-center"
                ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 inline-block h-4 w-4 text-yellow-500 dark:text-yellow-200">
                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline></svg
                >Draft</span
              ><span class="ml-1 inline-block opacity-50">0</span></a
            ><a type="button" role="tab" aria-selected="true" aria-controls="radix-:rb5:-content-ALL" data-state="active" id="radix-:rb5:-trigger-ALL" class="ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground hover:text-foreground inline-flex min-w-[60px] items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="" href="/documents?status=ALL"><span class="flex items-center">All</span></a>
          </div>
        </div>
        <div class="flex w-48 flex-wrap items-center justify-between gap-x-2 gap-y-4">
          <button type="button" role="combobox" aria-controls="radix-:rbb:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="border-input ring-offset-background placeholder:text-muted-foreground focus:ring-ring text-muted-foreground flex h-10 w-full max-w-[200px] items-center justify-between rounded-md border bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
            <span style="pointer-events: none;">All Time</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg>
          </button>
        </div>
      </div>
    </div>
    <div class="mt-8">
      <div class="relative">
        <div class="rounded-md border">
          <div class="w-full overflow-auto">
            <table class="w-full caption-bottom text-sm">
              <thead class="[&_tr]:border-b">
                <tr class="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors">
                  <th class="text-muted-foreground [&:has([role=checkbox])]:pr-0 h-12 px-4 text-left align-middle font-medium">Created</th>
                  <th class="text-muted-foreground [&:has([role=checkbox])]:pr-0 h-12 px-4 text-left align-middle font-medium">Title</th>
                  <th class="text-muted-foreground [&:has([role=checkbox])]:pr-0 h-12 px-4 text-left align-middle font-medium">Recipient</th>
                  <th class="text-muted-foreground [&:has([role=checkbox])]:pr-0 h-12 px-4 text-left align-middle font-medium">Status</th>
                  <th class="text-muted-foreground [&:has([role=checkbox])]:pr-0 h-12 px-4 text-left align-middle font-medium">Actions</th>
                </tr>
              </thead>
              <tbody class="[&_tr:last-child]:border-0">
                <tr class="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors" data-state="false">
                  <td class="[&:has([role=checkbox])]:pr-0 truncate p-4 align-middle" style="width: 150px;"><span>17/03/2024</span></td>
                  <td class="[&:has([role=checkbox])]:pr-0 truncate p-4 align-middle" style="width: 150px;"><a title="Your Electronic Ticket-EMD Receipt.pdf" class="block max-w-[10rem] truncate font-medium hover:underline md:max-w-[20rem]" href="/documents/5265">Your Electronic Ticket-EMD Receipt.pdf</a></td>
                  <td class="[&:has([role=checkbox])]:pr-0 truncate p-4 align-middle" style="width: 150px;">
                    <button type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rha:" data-state="closed" class="flex cursor-pointer">
                      <span class="dark:border-border relative z-50 flex h-10 w-10 shrink-0 overflow-hidden rounded-full border-2 border-solid border-white"><span class="bg-documenso-200 text-documenso-800 flex h-full w-full items-center justify-center rounded-full">HA</span></span>
                    </button>
                  </td>
                  <td class="[&:has([role=checkbox])]:pr-0 truncate p-4 align-middle" style="width: 140px;">
                    <span class="flex items-center"
                      ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 inline-block h-4 w-4 text-green-500 dark:text-green-300">
                        <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                        <path d="m9 12 2 2 4-4"></path></svg
                      >Completed</span
                    >
                  </td>
                  <td class="[&:has([role=checkbox])]:pr-0 truncate p-4 align-middle" style="width: 150px;">
                    <div class="flex items-center gap-x-4">
                      <button class="focus-visible:ring-ring ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-10 w-32 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="-ml-1 mr-2 inline h-4 w-4">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" x2="12" y1="15" y2="3"></line></svg
                        >Download</button
                      ><button type="button" id="radix-:rhb:" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground h-5 w-5">
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="19" cy="12" r="1"></circle>
                          <circle cx="5" cy="12" r="1"></circle>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="mt-8 w-full">
          <div class="flex flex-wrap items-center justify-between gap-x-4 gap-y-4 px-2">
            <div class="text-muted-foreground flex-1 text-sm"><span>Showing 1 result.</span></div>
            <div class="flex items-center gap-x-2">
              <p class="whitespace-nowrap text-sm font-medium">Rows per page</p>
              <button type="button" role="combobox" aria-controls="radix-:rbi:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="border-input ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-8 w-[70px] items-center justify-between rounded-md border bg-transparent px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                <span style="pointer-events: none;">20</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg>
              </button>
            </div>
            <div class="flex flex-wrap items-center gap-x-6 gap-y-4 lg:gap-x-8">
              <div class="flex items-center text-sm font-medium md:justify-center">Page 1 of 1</div>
              <div class="flex items-center gap-x-2">
                <button class="focus-visible:ring-ring ring-offset-background border-input hover:bg-accent hover:text-accent-foreground hidden h-8 w-8 items-center justify-center rounded-md border p-0 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 lg:flex" disabled="">
                  <span class="sr-only">Go to first page</span
                  ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <path d="m11 17-5-5 5-5"></path>
                    <path d="m18 17-5-5 5-5"></path>
                  </svg></button
                ><button class="focus-visible:ring-ring ring-offset-background border-input hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border p-0 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" disabled="">
                  <span class="sr-only">Go to previous page</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><path d="m15 18-6-6 6-6"></path></svg></button
                ><button class="focus-visible:ring-ring ring-offset-background border-input hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border p-0 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" disabled="">
                  <span class="sr-only">Go to next page</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><path d="m9 18 6-6-6-6"></path></svg></button
                ><button class="focus-visible:ring-ring ring-offset-background border-input hover:bg-accent hover:text-accent-foreground hidden h-8 w-8 items-center justify-center rounded-md border p-0 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 lg:flex" disabled="">
                  <span class="sr-only">Go to last page</span
                  ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <path d="m6 17 5-5-5-5"></path>
                    <path d="m13 17 5-5-5-5"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>


<section>
    <div class="w-4/6 m-auto  grid my-8 border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 md:mb-12 md:grid-cols-2">

        <figure class="flex flex-col items-center justify-center p-8 text-center bg-white border-b border-gray-200 rounded-t-lg md:rounded-t-none md:rounded-tl-lg md:border-r dark:bg-gray-800 dark:border-gray-700">
            <blockquote class=" mx-auto mb-4 text-gray-500 lg:mb-8 dark:text-gray-400">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Very easy to use platform</h3>
                <p class="my-4">We’ve had many collaborations with this platform and they have an excellent service. Sebastian Hansen is very helpful and he responds fast to our inquiries. Highly recommended!</p>
            </blockquote>
            <figcaption class="flex items-center justify-center space-x-3">
                <img class="rounded-full w-9 h-9" src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/karen-nelson.png" alt="profile picture">
                <div class="space-y-0.5 font-medium dark:text-white text-left">
                    <div>Bonnie Green</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">SEO at DotDash Media</div>
                </div>
            </figcaption>    
        </figure>
        <figure class="flex flex-col items-center justify-center p-8 text-center bg-white border-b border-gray-200 rounded-tr-lg dark:bg-gray-800 dark:border-gray-700">
            <blockquote class="max-w-2xl mx-auto mb-4 text-gray-500 lg:mb-8 dark:text-gray-400">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Solid foundation for any project</h3>
                <p class="my-4">Super cool platform with lots of interesting sites to get links from, easy to use, trustworthy and transparent. Fast and accurate response if you have any questions.</p>
            </blockquote>
            <figcaption class="flex items-center justify-center space-x-3">
                <img class="rounded-full w-9 h-9" src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/roberta-casas.png" alt="profile picture">
                <div class="space-y-0.5 font-medium dark:text-white text-left">
                    <div>Roberta Casas</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Lead SEO at Canva</div>
                </div>
            </figcaption>    
        </figure>
        <figure class="flex flex-col items-center justify-center p-8 text-center bg-white border-b border-gray-200 rounded-bl-lg md:border-b-0 md:border-r dark:bg-gray-800 dark:border-gray-700">
            <blockquote class="max-w-2xl mx-auto mb-4 text-gray-500 lg:mb-8 dark:text-gray-400">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Platform Review</h3>
                <p class="my-4">I have been working with them for a while, and have always been happy with the experience. The service, the personal treatment and the quality of their work was always top notch.</p>
            </blockquote>
            <figcaption class="flex items-center justify-center space-x-3">
                <img class="rounded-full w-9 h-9" src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/jese-leos.png" alt="profile picture">
                <div class="space-y-0.5 font-medium dark:text-white text-left">
                    <div>Jese Leos</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">SEO Lead at Web SEO Company UK</div>
                </div>
            </figcaption>    
        </figure>
        <figure class="flex flex-col items-center justify-center p-8 text-center bg-white border-gray-200 rounded-b-lg md:rounded-br-lg dark:bg-gray-800 dark:border-gray-700">
            <blockquote class="max-w-2xl mx-auto mb-4 text-gray-500 lg:mb-8 dark:text-gray-400">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Great Company</h3>
                <p class="my-4">A great company with great employees. They really take care of their customers and make sure that they get what they want. Anyone that work with online marketing should work with them.</p>
            </blockquote>
            <figcaption class="flex items-center justify-center space-x-3">
                <img class="rounded-full w-9 h-9" src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/joseph-mcfall.png" alt="profile picture">
                <div class="space-y-0.5 font-medium dark:text-white text-left">
                    <div>Joseph McFall</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">CEO SmartSites SEO</div>
                </div>
            </figcaption>    
        </figure>
    </div>
</section>


{{-- Information --}}
<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6">
      <div class="max-w-screen-md mb-8 lg:mb-16">
          <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white">Designed for business teams like yours</h2>
          <p class="text-gray-500 sm:text-xl dark:text-gray-400">Here at Flowbite we focus on markets where technology, innovation, and capital can unlock long-term value and drive economic growth.</p>
      </div>
      <div class="space-y-8 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-12 md:space-y-0">
          <div>
              <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-primary-100 lg:h-12 lg:w-12 dark:bg-primary-900">
                  <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>
              </div>
              <h3 class="mb-2 text-xl font-bold dark:text-white">Marketing</h3>
              <p class="text-gray-500 dark:text-gray-400">Plan it, create it, launch it. Collaborate seamlessly with all  the organization and hit your marketing goals every month with our marketing plan.</p>
          </div>
          <div>
              <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-primary-100 lg:h-12 lg:w-12 dark:bg-primary-900">
                  <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path></svg>
              </div>
              <h3 class="mb-2 text-xl font-bold dark:text-white">Legal</h3>
              <p class="text-gray-500 dark:text-gray-400">Protect your organization, devices and stay compliant with our structured workflows and custom permissions made for you.</p>
          </div>
          <div>
              <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-primary-100 lg:h-12 lg:w-12 dark:bg-primary-900">
                  <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path><path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path></svg>                    
              </div>
              <h3 class="mb-2 text-xl font-bold dark:text-white">Business Automation</h3>
              <p class="text-gray-500 dark:text-gray-400">Auto-assign tasks, send Slack messages, and much more. Now power up with hundreds of new templates to help you get started.</p>
          </div>
          <div>
              <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-primary-100 lg:h-12 lg:w-12 dark:bg-primary-900">
                  <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path></svg>
              </div>
              <h3 class="mb-2 text-xl font-bold dark:text-white">Finance</h3>
              <p class="text-gray-500 dark:text-gray-400">Audit-proof software built for critical financial operations like month-end close and quarterly budgeting.</p>
          </div>
          <div>
              <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-primary-100 lg:h-12 lg:w-12 dark:bg-primary-900">
                  <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path></svg>
              </div>
              <h3 class="mb-2 text-xl font-bold dark:text-white">Enterprise Design</h3>
              <p class="text-gray-500 dark:text-gray-400">Craft beautiful, delightful experiences for both marketing and product with real cross-company collaboration.</p>
          </div>
          <div>
              <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-primary-100 lg:h-12 lg:w-12 dark:bg-primary-900">
                  <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path></svg>
              </div>
              <h3 class="mb-2 text-xl font-bold dark:text-white">Operations</h3>
              <p class="text-gray-500 dark:text-gray-400">Keep your company’s lights on with customizable, iterative, and structured workflows built for all efficient teams and individual.</p>
          </div>
      </div>
  </div>
</section>




{{-- Stats Counter --}}

<div class="w-9/12 m-auto p-4 bg-white rounded-lg px-8 dark:bg-gray-800" id="stats" role="tabpanel" 
        aria-labelledby="stats-tab">
    <dl class="grid grid-cols-2 gap-8 mx-auto text-gray-900 sm:grid-cols-3 xl:grid-cols-4 
        dark:text-white sm:p-8">
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">50,000+</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium dark:text-gray-400">Media Sites</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">40k+</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium dark:text-gray-400">Publishers</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">100K +</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium dark:text-gray-400">Orders Delivered</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">98%</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium  dark:text-gray-400">Delivery Rate</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">5 Days</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium  dark:text-gray-400">Avg Turn Around Time</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">2B+</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium  dark:text-gray-400">Monthly Visitors Collectively</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">30+</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium  dark:text-gray-400">Niches</dd>
        </div>
        <div class="flex flex-col items-center justify-center pb-10">
            <dt class="mb-2 text-4xl tracking-wide font-extrabold text-gray-700">30k+</dt>
            <dd class="text-gray-600 text-muted-foreground text-base font-medium  dark:text-gray-400">Organizations</dd>
        </div>
    </dl>
</div>



<!-- Features -->
<div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
  <div class="relative p-6 md:p-16">
    <!-- Grid -->
    <div class="relative z-10 lg:grid lg:grid-cols-12 lg:gap-16 lg:items-center">
      <div class="mb-10 lg:mb-0 lg:col-span-6 lg:col-start-8 lg:order-2">
        <h2 class="text-2xl text-gray-800 font-bold sm:text-3xl dark:text-neutral-200">
          Find The Websites That Meet Your Critera
        </h2>

        <!-- Tab Navs -->
        <nav class="grid gap-4 mt-5 md:mt-10" aria-label="Tabs" role="tablist" aria-orientation="vertical">
          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-md hs-tab-active:hover:border-transparent text-start hover:bg-gray-200 focus:outline-none focus:bg-gray-200 p-4 md:p-5 rounded-xl dark:hs-tab-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 active" id="tabs-with-card-item-1" aria-selected="true" data-hs-tab="#tabs-with-card-1" aria-controls="tabs-with-card-1" role="tab">
            <span class="flex gap-x-6">
              <svg class="shrink-0 mt-2 size-6 md:size-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 5.5A3.5 3.5 0 0 1 8.5 2H12v7H8.5A3.5 3.5 0 0 1 5 5.5z"/><path d="M12 2h3.5a3.5 3.5 0 1 1 0 7H12V2z"/><path d="M12 12.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 1 1-7 0z"/><path d="M5 19.5A3.5 3.5 0 0 1 8.5 16H12v3.5a3.5 3.5 0 1 1-7 0z"/><path d="M5 12.5A3.5 3.5 0 0 1 8.5 9H12v7H8.5A3.5 3.5 0 0 1 5 12.5z"/></svg>
              <span class="grow">
                <span class="block text-lg font-semibold hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200">Advanced Filtering</span>
                <span class="block mt-1 text-gray-800 dark:hs-tab-active:text-gray-200 dark:text-neutral-200">
                Filter and Sort Websites That Meets Your Requirements.
                </span>
              </span>
            </span>
          </button>

          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-md hs-tab-active:hover:border-transparent text-start hover:bg-gray-200 focus:outline-none focus:bg-gray-200 p-4 md:p-5 rounded-xl dark:hs-tab-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" id="tabs-with-card-item-2" aria-selected="false" data-hs-tab="#tabs-with-card-2" aria-controls="tabs-with-card-2" role="tab">
            <span class="flex gap-x-6">
              <svg class="shrink-0 mt-2 size-6 md:size-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 14 4-4"/><path d="M3.34 19a10 10 0 1 1 17.32 0"/></svg>
              <span class="grow">
                <span class="block text-lg font-semibold hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200">One Click Buy</span>
                <span class="block mt-1 text-gray-800 dark:hs-tab-active:text-gray-200 dark:text-neutral-200">
                    Add sites to your cart and get the article published without manual input.
                </span>
              </span>
            </span>
          </button>

          <button type="button" class="hs-tab-active:bg-white hs-tab-active:shadow-md hs-tab-active:hover:border-transparent text-start hover:bg-gray-200 focus:outline-none focus:bg-gray-200 p-4 md:p-5 rounded-xl dark:hs-tab-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" id="tabs-with-card-item-3" aria-selected="false" data-hs-tab="#tabs-with-card-3" aria-controls="tabs-with-card-3" role="tab">
            <span class="flex gap-x-6">
              <svg class="shrink-0 mt-2 size-6 md:size-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M19 17v4"/><path d="M3 5h4"/><path d="M17 19h4"/></svg>
              <span class="grow">
                <span class="block text-lg font-semibold hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200">Powerful features</span>
                <span class="block mt-1 text-gray-800 dark:hs-tab-active:text-gray-200 dark:text-neutral-200">Reduce time and effort on building modern look design with Preline only.</span>
              </span>
            </span>
          </button>
        </nav>
        <!-- End Tab Navs -->
      </div>
      <!-- End Col -->

      <div class="lg:col-span-6">
        <div class="relative">
          <!-- Tab Content -->
          <div>
            <div id="tabs-with-card-1" role="tabpanel" aria-labelledby="tabs-with-card-item-1">
              <img class="shadow-xl shadow-gray-200 rounded-xl dark:shadow-gray-900/20" src="https://images.unsplash.com/photo-1605629921711-2f6b00c6bbf4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=560&h=720&q=80" alt="Features Image">
            </div>

            <div id="tabs-with-card-2" class="hidden" role="tabpanel" aria-labelledby="tabs-with-card-item-2">
              <img class="shadow-xl shadow-gray-200 rounded-xl dark:shadow-gray-900/20" src="https://images.unsplash.com/photo-1665686306574-1ace09918530?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=560&h=720&q=80" alt="Features Image">
            </div>

            <div id="tabs-with-card-3" class="hidden" role="tabpanel" aria-labelledby="tabs-with-card-item-3">
              <img class="shadow-xl shadow-gray-200 rounded-xl dark:shadow-gray-900/20" src="https://images.unsplash.com/photo-1598929213452-52d72f63e307?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=560&h=720&q=80" alt="Features Image">
            </div>
          </div>
          <!-- End Tab Content -->

          <!-- SVG Element -->
          <div class="hidden absolute top-0 end-0 translate-x-20 md:block lg:translate-x-20">
            <svg class="w-16 h-auto text-orange-500" width="121" height="135" viewBox="0 0 121 135" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 16.4754C11.7688 27.4499 21.2452 57.3224 5 89.0164" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
              <path d="M33.6761 112.104C44.6984 98.1239 74.2618 57.6776 83.4821 5" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
              <path d="M50.5525 130C68.2064 127.495 110.731 117.541 116 78.0874" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
            </svg>
          </div>
          <!-- End SVG Element -->
        </div>
      </div>
      <!-- End Col -->
    </div>
    <!-- End Grid -->

    <!-- Background Color -->
    <div class="absolute inset-0 grid grid-cols-12 size-full">
      <div class="col-span-full lg:col-span-7 lg:col-start-6 bg-gray-100 w-full h-5/6 rounded-xl sm:h-3/4 lg:h-full dark:bg-neutral-800"></div>
    </div>
    <!-- End Background Color -->
  </div>
</div>
<!-- End Features -->






 <!-- Features Top -->
<div class="px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto border-2 rounded-lg max-w-7xl ">

  <!-- Tab Nav -->
  <nav class="max-w-6xl mx-auto flex flex-col sm:flex-row gap-y-px sm:gap-y-0 sm:gap-x-4" aria-label="Tabs" role="tablist" aria-orientation="horizontal">

    {{-- Button One --}}
    <button type="button" class="bg-gray-100 border-2 border-gray-600 hover:border-transparent w-full flex flex-col text-start hover:bg-gray-100 focus:outline-none focus:bg-gray-100 p-3 md:p-5 rounded-xl dark:hs-tab-active:bg-neutral-800 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800 active" id="tabs-with-card-item-1" aria-selected="true" data-hs-tab="#tabs-with-card-1" aria-controls="tabs-with-card-1" role="tab">
      <svg class="shrink-0 hidden sm:block size-7 text-gray-600 dark:hs-tab-active:text-blue-500 dark:text-white" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 5.5A3.5 3.5 0 0 1 8.5 2H12v7H8.5A3.5 3.5 0 0 1 5 5.5z"/><path d="M12 2h3.5a3.5 3.5 0 1 1 0 7H12V2z"/><path d="M12 12.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 1 1-7 0z"/><path d="M5 19.5A3.5 3.5 0 0 1 8.5 16H12v3.5a3.5 3.5 0 1 1-7 0z"/><path d="M5 12.5A3.5 3.5 0 0 1 8.5 9H12v7H8.5A3.5 3.5 0 0 1 5 12.5z"/></svg>
      <span class="mt-5">
        <span class="text-gray-600 block font-semibold">Thousands of Publishers</span>
        <span class="hidden lg:block mt-2 text-gray-700 dark:text-neutral-200">Easily find publishers in any niche.</span>
      </span>
    </button>

    <button type="button" class="bg-gray-100 hover:border-transparent w-full flex flex-col text-start hover:bg-gray-100 focus:outline-none focus:bg-gray-100 p-3 md:p-5 rounded-xl dark:hs-tab-active:bg-neutral-800 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" id="tabs-with-card-item-2" aria-selected="false" data-hs-tab="#tabs-with-card-2" aria-controls="tabs-with-card-2" role="tab">
      <svg class="shrink-0 hidden sm:block size-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-white" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 14 4-4"/><path d="M3.34 19a10 10 0 1 1 17.32 0"/></svg>
      <span class="mt-5">
        <span class="hs-tab-active:text-blue-600 block font-semibold text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200">Automation on a whole new level</span>
        <span class="hidden lg:block mt-2 text-gray-800 dark:text-neutral-200">Use automation to scale campaigns profitably and save time doing it.</span>
      </span>
    </button>

    <button type="button" class="bg-gray-100 hover:border-transparent w-full flex flex-col text-start hover:bg-gray-100 focus:outline-none focus:bg-gray-100 p-3 md:p-5 rounded-xl dark:hs-tab-active:bg-neutral-800 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" id="tabs-with-card-item-3" aria-selected="false" data-hs-tab="#tabs-with-card-3" aria-controls="tabs-with-card-3" role="tab">
      <svg class="shrink-0 hidden sm:block size-7 hs-tab-active:text-blue-600 text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-white" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M19 17v4"/><path d="M3 5h4"/><path d="M17 19h4"/></svg>
      <span class="mt-5">
        <span class="hs-tab-active:text-blue-600 block font-semibold text-gray-800 dark:hs-tab-active:text-blue-500 dark:text-neutral-200">Solving problems for every team</span>
        <span class="hidden lg:block mt-2 text-gray-800 dark:text-neutral-200">One tool for your company to share knowledge and ship projects.</span>
      </span>
    </button>
  </nav>
  <!-- End Tab Nav -->



  <!-- Tab Content -->
  <div class="mt-12 md:mt-16">

    <div id="tabs-with-card-1" role="tabpanel" aria-labelledby="tabs-with-card-item-1">
      <!-- Devices -->
      <div class="max-w-[1140px] lg:pb-32 relative">
        <!-- Mobile Device -->
        <figure class="hidden absolute bottom-0 start-0 z-[2] max-w-full w-60 h-auto mb-20 ms-20 lg:block">
          <div class="p-1.5 bg-gray-100 rounded-3xl shadow-[0_2.75rem_5.5rem_-3.5rem_rgb(45_55_75_/_20%),_0_2rem_4rem_-2rem_rgb(45_55_75_/_30%),_inset_0_-0.1875rem_0.3125rem_0_rgb(45_55_75_/_20%)] dark:shadow-[0_2.75rem_5.5rem_-3.5rem_rgb(0_0_0_/_20%),_0_2rem_4rem_-2rem_rgb(0_0_0_/_30%),_inset_0_-0.1875rem_0.3125rem_0_rgb(0_0_0_/_20%)] dark:bg-neutral-700">
            <img class="max-w-full rounded-[1.25rem] h-auto" src="https://links.test/storage/resources/homepage/websites.png" alt="Features Image">
          </div>
        </figure>
        <!-- End Mobile Device -->

        <!-- Browser Device -->
        <figure class="ms-auto me-20 relative z-[1] max-w-full w-[50rem] h-auto shadow-[0_2.75rem_3.5rem_-2rem_rgb(45_55_75_/_20%),_0_0_5rem_-2rem_rgb(45_55_75_/_15%)] dark:shadow-[0_2.75rem_3.5rem_-2rem_rgb(0_0_0_/_20%),_0_0_5rem_-2rem_rgb(0_0_0_/_15%)] rounded-b-lg">
          <div class="relative flex items-center max-w-[50rem] bg-white border-b border-gray-100 rounded-t-lg py-2 px-24 dark:bg-neutral-800 dark:border-neutral-700">
            <div class="flex gap-x-1 absolute top-2/4 start-4 -translate-y-1">
              <span class="size-2 bg-gray-200 rounded-full dark:bg-neutral-700"></span>
              <span class="size-2 bg-gray-200 rounded-full dark:bg-neutral-700"></span>
              <span class="size-2 bg-gray-200 rounded-full dark:bg-neutral-700"></span>
            </div>
            <div class="flex justify-center items-center size-full bg-gray-200 text-[.25rem] text-gray-800 rounded-sm sm:text-[.5rem] dark:bg-neutral-700 dark:text-neutral-200">www.preline.co</div>
          </div>

          <div class="bg-gray-800 rounded-b-lg">
            <img class="max-w-full h-auto rounded-b-lg" src="https://links.test/storage/resources/screenshot.png" alt="Features Image">
          </div>
        </figure>
        <!-- End Browser Device -->
      </div>
      <!-- End Devices -->
    </div>

    <div id="tabs-with-card-2" class="hidden" role="tabpanel" aria-labelledby="tabs-with-card-item-2">
      <!-- Devices -->
      <div class="max-w-[1140px] lg:pb-32 relative">
        <!-- Mobile Device -->
        <figure class="hidden absolute bottom-0 start-0 z-[2] max-w-full w-60 h-auto mb-20 ms-20 lg:block">
          <div class="p-1.5 bg-gray-700 shadow-[0_2.75rem_5.5rem_-3.5rem_rgb(0_0_0_/_20%),_0_2rem_4rem_-2rem_rgb(0_0_0_/_30%),_inset_0_-0.1875rem_0.3125rem_0_rgb(0_0_0_/_20%)] rounded-3xl">
            <img class="max-w-full rounded-[1.25rem] h-auto" src="https://preline.co/assets/img/mockups/img11.jpg" alt="Features Image">
          </div>
        </figure>
        <!-- End Mobile Device -->

        <!-- Browser Device -->
        <figure class="ms-auto me-20 relative z-[1] max-w-full w-[50rem] h-auto shadow-shadow-[0_2.75rem_3.5rem_-2rem_rgb(0_0_0_/_20%),_0_0_5rem_-2rem_rgb(0_0_0_/_15%)] rounded-b-lg">
          <div class="relative flex items-center max-w-[50rem] bg-gray-800 border-b border-gray-700 rounded-t-lg py-2 px-24">
            <div class="flex gap-x-1 absolute top-2/4 start-4 -translate-y-1">
              <span class="size-2 bg-gray-700 rounded-full"></span>
              <span class="size-2 bg-gray-700 rounded-full"></span>
              <span class="size-2 bg-gray-700 rounded-full"></span>
            </div>
            <div class="flex justify-center items-center size-full bg-gray-700 text-[.25rem] sm:text-[.5rem] text-gray-200 rounded-sm">www.preline.co</div>
          </div>

          <div class="bg-gray-800 rounded-b-lg">
            <img class="max-w-full h-auto rounded-b-lg" src="https://preline.co/assets/img/mockups/img10.jpg" alt="Features Image">
          </div>
        </figure>
        <!-- End Browser Device -->
      </div>
      <!-- End Devices -->
    </div>




    <div id="tabs-with-card-3" class="hidden" role="tabpanel" aria-labelledby="tabs-with-card-item-3">
      <!-- Devices -->
      <div class="max-w-[1140px] lg:pb-32 relative">
        <!-- Mobile Device -->
        <figure class="hidden absolute bottom-0 start-0 z-[2] max-w-full w-60 h-auto mb-20 ms-20 lg:block">
          <div class="p-1.5 bg-gray-100 rounded-3xl shadow-[0_2.75rem_5.5rem_-3.5rem_rgb(45_55_75_/_20%),_0_2rem_4rem_-2rem_rgb(45_55_75_/_30%),_inset_0_-0.1875rem_0.3125rem_0_rgb(45_55_75_/_20%)] dark:shadow-[0_2.75rem_5.5rem_-3.5rem_rgb(0_0_0_/_20%),_0_2rem_4rem_-2rem_rgb(0_0_0_/_30%),_inset_0_-0.1875rem_0.3125rem_0_rgb(0_0_0_/_20%)] dark:bg-neutral-700">
            <img class="max-w-full rounded-[1.25rem] h-auto" src="https://preline.co/assets/img/mockups/img13.jpg" alt="Features Image">
          </div>
        </figure>
        <!-- End Mobile Device -->

        <!-- Browser Device -->
        <figure class="ms-auto me-20 relative z-[1] max-w-full w-[50rem] h-auto shadow-[0_2.75rem_3.5rem_-2rem_rgb(45_55_75_/_20%),_0_0_5rem_-2rem_rgb(45_55_75_/_15%)] dark:shadow-[0_2.75rem_3.5rem_-2rem_rgb(0_0_0_/_20%),_0_0_5rem_-2rem_rgb(0_0_0_/_15%)] rounded-b-lg">
          <div class="relative flex items-center max-w-[50rem] bg-white border-b border-gray-100 rounded-t-lg py-2 px-24 dark:bg-neutral-800 dark:border-neutral-700">
            <div class="flex gap-x-1 absolute top-2/4 start-4 -translate-y-1">
              <span class="size-2 bg-gray-200 rounded-full dark:bg-neutral-700"></span>
              <span class="size-2 bg-gray-200 rounded-full dark:bg-neutral-700"></span>
              <span class="size-2 bg-gray-200 rounded-full dark:bg-neutral-700"></span>
            </div>
            <div class="flex justify-center items-center size-full bg-gray-200 text-[.25rem] text-gray-800 rounded-sm sm:text-[.5rem] dark:bg-neutral-700 dark:text-neutral-200">www.preline.co</div>
          </div>

          <div class="bg-gray-800 rounded-b-lg">
            <img class="max-w-full h-auto rounded-b-lg" src="../assets/img/mockups/img12.jpg" alt="Features Image">
          </div>
        </figure>
        <!-- End Browser Device -->
      </div>
      <!-- End Devices -->
    </div>
  </div>
  <!-- End Tab Content -->
</div>
<!-- End Features -->



<!-- Hero -->
<div class="relative overflow-hidden">
  <div class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-24">
    <div class="text-center">
      <h1 class="text-4xl sm:text-6xl font-bold text-gray-800 dark:text-neutral-200">
        Insights
      </h1>

      <p class="mt-3 text-gray-600 dark:text-neutral-400">
        Stay in the know with insights from industry experts.
      </p>

      <div class="mt-7 sm:mt-12 mx-auto max-w-xl relative">
        <!-- Form -->
        <form>
          <div class="relative z-10 flex gap-x-3 p-3 bg-white border rounded-lg shadow-lg shadow-gray-100 dark:bg-neutral-900 dark:border-neutral-700 dark:shadow-gray-900/20">
            <div class="w-full">
              <label for="hs-search-article-1" class="block text-sm text-gray-700 font-medium dark:text-white"><span class="sr-only">Search article</span></label>
              <input type="email" name="hs-search-article-1" id="hs-search-article-1" class="py-2.5 px-4 block w-full border-transparent rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-transparent dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Search article">
            </div>
            <div>
              <a class="size-[46px] inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none" href="#">
                <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
              </a>
            </div>
          </div>
        </form>
        <!-- End Form -->

        <!-- SVG Element -->
        <div class="hidden md:block absolute top-0 end-0 -translate-y-12 translate-x-20">
          <svg class="w-16 h-auto text-orange-500" width="121" height="135" viewBox="0 0 121 135" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 16.4754C11.7688 27.4499 21.2452 57.3224 5 89.0164" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
            <path d="M33.6761 112.104C44.6984 98.1239 74.2618 57.6776 83.4821 5" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
            <path d="M50.5525 130C68.2064 127.495 110.731 117.541 116 78.0874" stroke="currentColor" stroke-width="10" stroke-linecap="round"/>
          </svg>
        </div>
        <!-- End SVG Element -->

        <!-- SVG Element -->
        <div class="hidden md:block absolute bottom-0 start-0 translate-y-10 -translate-x-32">
          <svg class="w-40 h-auto text-cyan-500" width="347" height="188" viewBox="0 0 347 188" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 82.4591C54.7956 92.8751 30.9771 162.782 68.2065 181.385C112.642 203.59 127.943 78.57 122.161 25.5053C120.504 2.2376 93.4028 -8.11128 89.7468 25.5053C85.8633 61.2125 130.186 199.678 180.982 146.248L214.898 107.02C224.322 95.4118 242.9 79.2851 258.6 107.02C274.299 134.754 299.315 125.589 309.861 117.539L343 93.4426" stroke="currentColor" stroke-width="7" stroke-linecap="round"/>
          </svg>
        </div>
        <!-- End SVG Element -->
      </div>

      <div class="mt-10 sm:mt-20">
        <a class="m-1 py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg>
          Business
        </a>
        <a class="m-1 py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
          Strategy
        </a>
        <a class="m-1 py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>
          Health
        </a>
        <a class="m-1 py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5"/><path d="M9 18h6"/><path d="M10 22h4"/></svg>
          Creative
        </a>
        <a class="m-1 py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/><path d="M10 6h4"/><path d="M10 10h4"/><path d="M10 14h4"/><path d="M10 18h4"/></svg>
          Environment
        </a>
        <a class="m-1 py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z"/></svg>
          Adventure
        </a>
      </div>
    </div>
  </div>
</div>
<!-- End Hero -->


{{-- Hero Content --}}
<section class="px-2 py-32 bg-white md:px-0">
  <div class="container items-center max-w-6xl px-8 mx-auto xl:px-5">
    <div class="flex flex-wrap items-center sm:-mx-3">
      <div class="w-full md:w-1/2 md:px-3">
        <div class="w-full pb-6 space-y-6 sm:max-w-md lg:max-w-lg md:space-y-4 lg:space-y-8 xl:space-y-9 sm:pr-5 lg:pr-0 md:pb-0">
          <h1 class="text-4xl leading-10 font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-4xl lg:text-5xl xl:text-6xl">
            <span class="block xl:inline">Buy Powerful Backlinks</span>
             <span class="text-transparent bg-clip-text bg-gradient-to-br from-yellow-300 to-red-500">With Your Own Requirements</span>
          </h1>
          <p class="mx-auto text-base text-gray-500 sm:max-w-md lg:text-xl md:max-w-3xl">Get links from +50000 media all around the world.
The only link building platform you’ll need!</p>
          <div class="relative flex flex-col sm:flex-row sm:space-x-4">
            <a href="#_" class="flex items-center w-full px-6 py-3 mb-3 text-lg text-white bg-indigo-600 rounded-md sm:mb-0 hover:bg-indigo-700 sm:w-auto" data-primary="indigo-600" data-rounded="rounded-md">
              Try It Free
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>
            </a>
            <a href="#_" class="flex items-center px-6 py-3 text-gray-500 bg-gray-100 rounded-md hover:bg-gray-200 hover:text-gray-600" data-rounded="rounded-md">
              Learn More
            </a>
          </div>
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <div class="w-full h-auto overflow-hidden rounded-md shadow-xl sm:rounded-xl" data-rounded="rounded-xl" data-rounded-max="rounded-full">
            <img src="https://cdn.devdojo.com/images/november2020/hero-image.jpeg">
          </div>
      </div>
    </div>
  </div>
</section>



<!-- Card Section -->
<div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
  <!-- Grid -->
  <div class="grid sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-6">
    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Management
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              4 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              App Development
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              26 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Arts & Entertainment
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              9 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Accounting
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              11 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              UI Designer
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              37 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Apps
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              2 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Content Writer
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              10 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex justify-between items-center gap-x-3">
          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Analytics
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              14 job positions
            </p>
          </div>
          <div>
            <svg class="shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->
  </div>
  <!-- End Grid -->
</div>
<!-- End Card Section -->


<!-- Card Section -->
<div class="max-w-5xl px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
  <!-- Grid -->
  <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex gap-x-5">
          <svg class="mt-1 shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>

          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Ask our community
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              Get help from 40k+ Preline users
            </p>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex gap-x-5">
          <svg class="mt-1 shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>

          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Get help in the app
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              Just head to «Help» in the app
            </p>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->

    <!-- Card -->
    <a class="group flex flex-col bg-white border shadow-sm rounded-xl hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-900 dark:border-neutral-800" href="#">
      <div class="p-4 md:p-5">
        <div class="flex gap-x-5">
          <svg class="mt-1 shrink-0 size-5 text-gray-800 dark:text-neutral-200" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.2 8.4c.5.38.8.97.8 1.6v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V10a2 2 0 0 1 .8-1.6l8-6a2 2 0 0 1 2.4 0l8 6Z"/><path d="m22 10-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 10"/></svg>

          <div class="grow">
            <h3 class="group-hover:text-blue-600 font-semibold text-gray-800 dark:group-hover:text-neutral-400 dark:text-neutral-200">
              Email us
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-500">
              Reach us at <span class="text-blue-600 decoration-2 group-hover:underline font-medium dark:text-blue-500"><EMAIL></span>
            </p>
          </div>
        </div>
      </div>
    </a>
    <!-- End Card -->
  </div>
  <!-- End Grid -->
</div>
<!-- End Card Section -->


 <div id="order-message-success" class="box-section-wrapper">

      <div class="py-2">
        <h1 class="text-base font-semibold text-orange-600">Thank you!</h1>
        <p class="mt-2 text-2xl py-2 text-slate-700 font-bold tracking-tight sm:text-5xl">
          It's in the process!
        </p>
        <p class="mt-2 text-slate-500 font-medium ">
          Your order #14034056 is sucessfully placed. Please provide the required information to start the publishing process.
        </p>
        <div class="mt-8 mb-1 flex justify-between items-center">
          <dl class="text-sm font-medium">
            <dt class="text-slate-700 font-medium">Order Tracking number</dt>
            <dd class="mt-2 text-slate-900 font-bold">$order->id</dd>
          </dl>
        </div>

        <div class=" pt-6 sm:flex sm:items-baseline sm:justify-between sm:space-y-0 sm:px-0">
          <div class="flex sm:items-baseline sm:space-x-4">
            <a href="#" class="hidden text-sm font-medium text-emerald-600 hover:text-indigo-500 sm:block">
              View invoice
              <span aria-hidden="true"> &rarr;</span>
            </a>
          </div>
          <p class="text-sm text-slate-600">Order placed: <time datetime="2021-03-22" class="font-medium text-slate-900">March 22, 2023</time></p>
        </div>
      </div>


      {{-- Action Buttons --}}
      <div id="actions-buttons" class="flex border-t border-zinc-200 mt-6 pt-6 space-x-3"> 
        {{-- <button type="button" class="text-white bg-orange-700 hover:bg-emerald-800 focus:ring-2 focus:ring-emerald-500 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Start My Order</button> --}}
        <a href="#order-items-table-user">
        <button type="button" class="py-2.5 px-4 font-medium text-zinc-700 focus:outline-none bg-white rounded-md border-2 border-zinc-400 hover:bg-emerald-600 hover:text-white hover:border-emerald-600 focus:z-10 focus:ring-2 focus:ring-emerald-500 dark:focus:ring-emerald-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 text-xs">Add Order Details</button>
        </a>
      </div>

    </div>

<div class="flex items-center justify-center">
    <div class="flex w-full max-w-xs flex-wrap items-center justify-center gap-x-12 gap-y-6 sm:max-w-mdlg lg:max-w-2.5xl">
        <div class="order-1">
            <div class="flex w-52 items-center justify-between gap-2 text-center">
                <svg width="30" height="60" viewBox="0 0 30 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[60px] w-[30px] shrink-0"><path d="M14.0947 9.35871C16.8587 9.08173 19.5519 5.86753 19.2482 1.59668C15.2005 2.17114 12.7117 6.59369 14.0947 9.35871Z" fill="#404147"></path><path d="M9.84495 15.0454C11.3313 13.8755 12.8841 8.96429 9.62272 5.5204C5.42024 8.68822 8.16782 14.1711 9.84495 15.0454Z" fill="#404147"></path><path d="M8.46051 23.3394C9.32182 21.6553 9.0378 16.0771 4.0246 15.0236C1.87123 19.234 6.57147 23.2461 8.46051 23.3394Z" fill="#404147"></path><path d="M8.77875 32.5161C9.07611 30.648 6.91525 24.8271 1.82228 25.3781C1.0788 30.0483 6.2582 32.8936 8.77875 32.5161Z" fill="#404147"></path><path d="M11.9011 41.4024C11.7067 39.4928 7.80018 34.1114 2.48796 34.954C3.04076 41.4024 10.0796 42.0072 11.9011 41.4024Z" fill="#404147"></path><path d="M17.5849 49.6023C16.9542 47.5302 10.9975 42.1893 5.07298 43.8383C7.33357 50.3866 15.6209 50.5148 17.5849 49.6023Z" fill="#404147"></path><path d="M24.8713 56.5979C23.8075 54.7112 17.1405 50.6749 11.7124 53.5655C14.3717 58.2824 21.3974 58.8228 24.8713 56.5979Z" fill="#404147"></path><path d="M26.5857 55.0667C28.347 54.4303 30.5452 48.9804 25.9242 46.1679C21.7057 49.784 24.2179 53.7486 26.5857 55.0667Z" fill="#404147"></path><path d="M19.5207 48.7717C21.3778 48.5311 24.5242 43.6143 20.6204 39.8695C15.7201 42.4882 18.3433 47.3157 19.5207 48.7717Z" fill="#404147"></path><path d="M13.8978 40.9918C15.6131 41.0353 18.6613 37.4608 16.1737 33.5105C12.0313 36.0546 13.0368 39.508 13.8978 40.9918Z" fill="#404147"></path><path d="M10.1811 32.3355C11.8166 32.7276 15.575 30.4957 14.2307 25.9681C9.53946 26.0767 9.8478 30.7897 10.1811 32.3355Z" fill="#404147"></path><path d="M10.12 23.6177C11.3059 24.3893 16.173 23.0644 16.1718 18.8474C12.0709 17.9523 10.1563 22.2037 10.12 23.6177Z" fill="#404147"></path><path d="M11.775 15.8755C12.9608 16.6471 18.1349 16.152 17.8265 11.7638C14.5411 11.176 11.8113 14.4615 11.775 15.8755Z" fill="#404147"></path></svg><div class="component-semi-strong-500 flex w-[130px] flex-col items-center gap-1 text-center text-neutral-8"><svg width="40" height="32" viewBox="0 0 40 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-8 w-10 fill-neutral-8"><path d="M25.5435 7.57567C23.1435 7.57567 22.1292 8.72092 20.4578 8.72092C18.7441 8.72092 17.4369 7.58401 15.3572 7.58401C13.3215 7.58401 11.1506 8.82687 9.77204 10.9441C7.83632 13.9299 8.16489 19.5531 11.3 24.3436C12.4215 26.0585 13.9191 27.9817 15.8834 28.0025H15.9191C17.6262 28.0025 18.1334 26.8847 20.4828 26.8716H20.5185C22.8328 26.8716 23.2971 27.996 24.9971 27.996H25.0328C26.9971 27.9752 28.5751 25.8442 29.6965 24.1359C30.5037 22.9073 30.8037 22.2906 31.4227 20.9007C26.8876 19.1793 26.159 12.7501 30.6441 10.2852C29.2751 8.57091 27.3513 7.57805 25.5376 7.57805L25.5435 7.57567Z"></path><path d="M25.0149 1.33398C23.5864 1.43101 21.9197 2.34054 20.9435 3.52804C20.0578 4.60423 19.3292 6.20067 19.6149 7.74889H19.7292C21.2506 7.74889 22.8078 6.83281 23.7173 5.659C24.5935 4.54173 25.2578 2.95839 25.0149 1.33398Z"></path></svg>Editors' Choice</div><svg width="30" height="60" viewBox="0 0 30 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[60px] w-[30px] shrink-0 -scale-x-100"><path d="M14.0947 9.35871C16.8587 9.08173 19.5519 5.86753 19.2482 1.59668C15.2005 2.17114 12.7117 6.59369 14.0947 9.35871Z" fill="#404147"></path><path d="M9.84495 15.0454C11.3313 13.8755 12.8841 8.96429 9.62272 5.5204C5.42024 8.68822 8.16782 14.1711 9.84495 15.0454Z" fill="#404147"></path><path d="M8.46051 23.3394C9.32182 21.6553 9.0378 16.0771 4.0246 15.0236C1.87123 19.234 6.57147 23.2461 8.46051 23.3394Z" fill="#404147"></path><path d="M8.77875 32.5161C9.07611 30.648 6.91525 24.8271 1.82228 25.3781C1.0788 30.0483 6.2582 32.8936 8.77875 32.5161Z" fill="#404147"></path><path d="M11.9011 41.4024C11.7067 39.4928 7.80018 34.1114 2.48796 34.954C3.04076 41.4024 10.0796 42.0072 11.9011 41.4024Z" fill="#404147"></path><path d="M17.5849 49.6023C16.9542 47.5302 10.9975 42.1893 5.07298 43.8383C7.33357 50.3866 15.6209 50.5148 17.5849 49.6023Z" fill="#404147"></path><path d="M24.8713 56.5979C23.8075 54.7112 17.1405 50.6749 11.7124 53.5655C14.3717 58.2824 21.3974 58.8228 24.8713 56.5979Z" fill="#404147"></path><path d="M26.5857 55.0667C28.347 54.4303 30.5452 48.9804 25.9242 46.1679C21.7057 49.784 24.2179 53.7486 26.5857 55.0667Z" fill="#404147"></path><path d="M19.5207 48.7717C21.3778 48.5311 24.5242 43.6143 20.6204 39.8695C15.7201 42.4882 18.3433 47.3157 19.5207 48.7717Z" fill="#404147"></path><path d="M13.8978 40.9918C15.6131 41.0353 18.6613 37.4608 16.1737 33.5105C12.0313 36.0546 13.0368 39.508 13.8978 40.9918Z" fill="#404147"></path><path d="M10.1811 32.3355C11.8166 32.7276 15.575 30.4957 14.2307 25.9681C9.53946 26.0767 9.8478 30.7897 10.1811 32.3355Z" fill="#404147"></path><path d="M10.12 23.6177C11.3059 24.3893 16.173 23.0644 16.1718 18.8474C12.0709 17.9523 10.1563 22.2037 10.12 23.6177Z" fill="#404147"></path><path d="M11.775 15.8755C12.9608 16.6471 18.1349 16.152 17.8265 11.7638C14.5411 11.176 11.8113 14.4615 11.775 15.8755Z" fill="#404147"></path></svg></div></div><div class="order-2 lg:order-3"><div class="flex w-52 items-center justify-between gap-2 text-center"><svg width="30" height="60" viewBox="0 0 30 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[60px] w-[30px] shrink-0"><path d="M14.0947 9.35871C16.8587 9.08173 19.5519 5.86753 19.2482 1.59668C15.2005 2.17114 12.7117 6.59369 14.0947 9.35871Z" fill="#404147"></path><path d="M9.84495 15.0454C11.3313 13.8755 12.8841 8.96429 9.62272 5.5204C5.42024 8.68822 8.16782 14.1711 9.84495 15.0454Z" fill="#404147"></path><path d="M8.46051 23.3394C9.32182 21.6553 9.0378 16.0771 4.0246 15.0236C1.87123 19.234 6.57147 23.2461 8.46051 23.3394Z" fill="#404147"></path><path d="M8.77875 32.5161C9.07611 30.648 6.91525 24.8271 1.82228 25.3781C1.0788 30.0483 6.2582 32.8936 8.77875 32.5161Z" fill="#404147"></path><path d="M11.9011 41.4024C11.7067 39.4928 7.80018 34.1114 2.48796 34.954C3.04076 41.4024 10.0796 42.0072 11.9011 41.4024Z" fill="#404147"></path><path d="M17.5849 49.6023C16.9542 47.5302 10.9975 42.1893 5.07298 43.8383C7.33357 50.3866 15.6209 50.5148 17.5849 49.6023Z" fill="#404147"></path><path d="M24.8713 56.5979C23.8075 54.7112 17.1405 50.6749 11.7124 53.5655C14.3717 58.2824 21.3974 58.8228 24.8713 56.5979Z" fill="#404147"></path><path d="M26.5857 55.0667C28.347 54.4303 30.5452 48.9804 25.9242 46.1679C21.7057 49.784 24.2179 53.7486 26.5857 55.0667Z" fill="#404147"></path><path d="M19.5207 48.7717C21.3778 48.5311 24.5242 43.6143 20.6204 39.8695C15.7201 42.4882 18.3433 47.3157 19.5207 48.7717Z" fill="#404147"></path><path d="M13.8978 40.9918C15.6131 41.0353 18.6613 37.4608 16.1737 33.5105C12.0313 36.0546 13.0368 39.508 13.8978 40.9918Z" fill="#404147"></path><path d="M10.1811 32.3355C11.8166 32.7276 15.575 30.4957 14.2307 25.9681C9.53946 26.0767 9.8478 30.7897 10.1811 32.3355Z" fill="#404147"></path><path d="M10.12 23.6177C11.3059 24.3893 16.173 23.0644 16.1718 18.8474C12.0709 17.9523 10.1563 22.2037 10.12 23.6177Z" fill="#404147"></path><path d="M11.775 15.8755C12.9608 16.6471 18.1349 16.152 17.8265 11.7638C14.5411 11.176 11.8113 14.4615 11.775 15.8755Z" fill="#404147"></path></svg><div class="component-semi-strong-500 flex w-[130px] flex-col items-center gap-1 text-center text-neutral-8"><svg width="40" height="32" viewBox="0 0 40 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-8 w-10 fill-neutral-8"><path d="M26.9038 19.4479C26.215 19.4479 25.6546 18.8508 25.6546 18.1172C25.6546 17.3836 26.215 16.7869 26.9038 16.7869C27.5927 16.7869 28.153 17.3836 28.153 18.1172C28.153 18.8508 27.5927 19.4479 26.9038 19.4479ZM13.0962 19.4479C12.4073 19.4479 11.847 18.8508 11.847 18.1172C11.847 17.3836 12.4073 16.7869 13.0962 16.7869C13.785 16.7869 14.3454 17.3836 14.3454 18.1172C14.3454 18.8508 13.785 19.4479 13.0962 19.4479ZM27.3518 11.4343L29.8485 6.82956C29.9916 6.56489 29.9065 6.22682 29.6584 6.07397C29.4102 5.92156 29.0924 6.01223 28.9488 6.27647L26.4209 10.9395C24.4878 10 22.3167 9.47671 20 9.47671C17.6833 9.47671 15.5122 10 13.5791 10.9395L11.0512 6.27647C10.9076 6.01223 10.5898 5.92156 10.3416 6.07397C10.0935 6.22682 10.008 6.56489 10.1515 6.82956L12.6482 11.4343C8.36108 13.9174 5.42895 18.5395 5 24H35C34.5707 18.5395 31.6385 13.9174 27.3518 11.4343Z"></path></svg>Editors' Choice</div><svg width="30" height="60" viewBox="0 0 30 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[60px] w-[30px] shrink-0 -scale-x-100"><path d="M14.0947 9.35871C16.8587 9.08173 19.5519 5.86753 19.2482 1.59668C15.2005 2.17114 12.7117 6.59369 14.0947 9.35871Z" fill="#404147"></path><path d="M9.84495 15.0454C11.3313 13.8755 12.8841 8.96429 9.62272 5.5204C5.42024 8.68822 8.16782 14.1711 9.84495 15.0454Z" fill="#404147"></path><path d="M8.46051 23.3394C9.32182 21.6553 9.0378 16.0771 4.0246 15.0236C1.87123 19.234 6.57147 23.2461 8.46051 23.3394Z" fill="#404147"></path><path d="M8.77875 32.5161C9.07611 30.648 6.91525 24.8271 1.82228 25.3781C1.0788 30.0483 6.2582 32.8936 8.77875 32.5161Z" fill="#404147"></path><path d="M11.9011 41.4024C11.7067 39.4928 7.80018 34.1114 2.48796 34.954C3.04076 41.4024 10.0796 42.0072 11.9011 41.4024Z" fill="#404147"></path><path d="M17.5849 49.6023C16.9542 47.5302 10.9975 42.1893 5.07298 43.8383C7.33357 50.3866 15.6209 50.5148 17.5849 49.6023Z" fill="#404147"></path><path d="M24.8713 56.5979C23.8075 54.7112 17.1405 50.6749 11.7124 53.5655C14.3717 58.2824 21.3974 58.8228 24.8713 56.5979Z" fill="#404147"></path><path d="M26.5857 55.0667C28.347 54.4303 30.5452 48.9804 25.9242 46.1679C21.7057 49.784 24.2179 53.7486 26.5857 55.0667Z" fill="#404147"></path><path d="M19.5207 48.7717C21.3778 48.5311 24.5242 43.6143 20.6204 39.8695C15.7201 42.4882 18.3433 47.3157 19.5207 48.7717Z" fill="#404147"></path><path d="M13.8978 40.9918C15.6131 41.0353 18.6613 37.4608 16.1737 33.5105C12.0313 36.0546 13.0368 39.508 13.8978 40.9918Z" fill="#404147"></path><path d="M10.1811 32.3355C11.8166 32.7276 15.575 30.4957 14.2307 25.9681C9.53946 26.0767 9.8478 30.7897 10.1811 32.3355Z" fill="#404147"></path><path d="M10.12 23.6177C11.3059 24.3893 16.173 23.0644 16.1718 18.8474C12.0709 17.9523 10.1563 22.2037 10.12 23.6177Z" fill="#404147"></path><path d="M11.775 15.8755C12.9608 16.6471 18.1349 16.152 17.8265 11.7638C14.5411 11.176 11.8113 14.4615 11.775 15.8755Z" fill="#404147"></path></svg></div></div><div class="xs:mx-auto order-3 lg:order-2"><div class="flex w-52 items-center justify-between gap-2 text-center"><svg width="30" height="60" viewBox="0 0 30 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[60px] w-[30px] shrink-0"><path d="M14.0947 9.35871C16.8587 9.08173 19.5519 5.86753 19.2482 1.59668C15.2005 2.17114 12.7117 6.59369 14.0947 9.35871Z" fill="#404147"></path><path d="M9.84495 15.0454C11.3313 13.8755 12.8841 8.96429 9.62272 5.5204C5.42024 8.68822 8.16782 14.1711 9.84495 15.0454Z" fill="#404147"></path><path d="M8.46051 23.3394C9.32182 21.6553 9.0378 16.0771 4.0246 15.0236C1.87123 19.234 6.57147 23.2461 8.46051 23.3394Z" fill="#404147"></path><path d="M8.77875 32.5161C9.07611 30.648 6.91525 24.8271 1.82228 25.3781C1.0788 30.0483 6.2582 32.8936 8.77875 32.5161Z" fill="#404147"></path><path d="M11.9011 41.4024C11.7067 39.4928 7.80018 34.1114 2.48796 34.954C3.04076 41.4024 10.0796 42.0072 11.9011 41.4024Z" fill="#404147"></path><path d="M17.5849 49.6023C16.9542 47.5302 10.9975 42.1893 5.07298 43.8383C7.33357 50.3866 15.6209 50.5148 17.5849 49.6023Z" fill="#404147"></path><path d="M24.8713 56.5979C23.8075 54.7112 17.1405 50.6749 11.7124 53.5655C14.3717 58.2824 21.3974 58.8228 24.8713 56.5979Z" fill="#404147"></path><path d="M26.5857 55.0667C28.347 54.4303 30.5452 48.9804 25.9242 46.1679C21.7057 49.784 24.2179 53.7486 26.5857 55.0667Z" fill="#404147"></path><path d="M19.5207 48.7717C21.3778 48.5311 24.5242 43.6143 20.6204 39.8695C15.7201 42.4882 18.3433 47.3157 19.5207 48.7717Z" fill="#404147"></path><path d="M13.8978 40.9918C15.6131 41.0353 18.6613 37.4608 16.1737 33.5105C12.0313 36.0546 13.0368 39.508 13.8978 40.9918Z" fill="#404147"></path><path d="M10.1811 32.3355C11.8166 32.7276 15.575 30.4957 14.2307 25.9681C9.53946 26.0767 9.8478 30.7897 10.1811 32.3355Z" fill="#404147"></path><path d="M10.12 23.6177C11.3059 24.3893 16.173 23.0644 16.1718 18.8474C12.0709 17.9523 10.1563 22.2037 10.12 23.6177Z" fill="#404147"></path><path d="M11.775 15.8755C12.9608 16.6471 18.1349 16.152 17.8265 11.7638C14.5411 11.176 11.8113 14.4615 11.775 15.8755Z" fill="#404147"></path></svg><div class="component-semi-strong-500 flex w-[130px] flex-col items-center gap-1 text-center text-neutral-8"><div class="component-semi-strong-500 max-w-[130px] text-center"><span class="[&amp;_b]:component-strong-600 [&amp;_p]:flex [&amp;_p]:flex-col"><p><b>150+ million</b> downloads</p></span></div></div><svg width="30" height="60" viewBox="0 0 30 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[60px] w-[30px] shrink-0 -scale-x-100"><path d="M14.0947 9.35871C16.8587 9.08173 19.5519 5.86753 19.2482 1.59668C15.2005 2.17114 12.7117 6.59369 14.0947 9.35871Z" fill="#404147"></path><path d="M9.84495 15.0454C11.3313 13.8755 12.8841 8.96429 9.62272 5.5204C5.42024 8.68822 8.16782 14.1711 9.84495 15.0454Z" fill="#404147"></path><path d="M8.46051 23.3394C9.32182 21.6553 9.0378 16.0771 4.0246 15.0236C1.87123 19.234 6.57147 23.2461 8.46051 23.3394Z" fill="#404147"></path><path d="M8.77875 32.5161C9.07611 30.648 6.91525 24.8271 1.82228 25.3781C1.0788 30.0483 6.2582 32.8936 8.77875 32.5161Z" fill="#404147"></path><path d="M11.9011 41.4024C11.7067 39.4928 7.80018 34.1114 2.48796 34.954C3.04076 41.4024 10.0796 42.0072 11.9011 41.4024Z" fill="#404147"></path><path d="M17.5849 49.6023C16.9542 47.5302 10.9975 42.1893 5.07298 43.8383C7.33357 50.3866 15.6209 50.5148 17.5849 49.6023Z" fill="#404147"></path><path d="M24.8713 56.5979C23.8075 54.7112 17.1405 50.6749 11.7124 53.5655C14.3717 58.2824 21.3974 58.8228 24.8713 56.5979Z" fill="#404147"></path><path d="M26.5857 55.0667C28.347 54.4303 30.5452 48.9804 25.9242 46.1679C21.7057 49.784 24.2179 53.7486 26.5857 55.0667Z" fill="#404147"></path><path d="M19.5207 48.7717C21.3778 48.5311 24.5242 43.6143 20.6204 39.8695C15.7201 42.4882 18.3433 47.3157 19.5207 48.7717Z" fill="#404147"></path><path d="M13.8978 40.9918C15.6131 41.0353 18.6613 37.4608 16.1737 33.5105C12.0313 36.0546 13.0368 39.508 13.8978 40.9918Z" fill="#404147"></path><path d="M10.1811 32.3355C11.8166 32.7276 15.575 30.4957 14.2307 25.9681C9.53946 26.0767 9.8478 30.7897 10.1811 32.3355Z" fill="#404147"></path><path d="M10.12 23.6177C11.3059 24.3893 16.173 23.0644 16.1718 18.8474C12.0709 17.9523 10.1563 22.2037 10.12 23.6177Z" fill="#404147"></path><path d="M11.775 15.8755C12.9608 16.6471 18.1349 16.152 17.8265 11.7638C14.5411 11.176 11.8113 14.4615 11.775 15.8755Z" fill="#404147"></path></svg>
            </div>
        </div>
    </div>
</div>





{{-- Resend --}}
{{-- https://resend.com/ --}}

<style>
  :root{
    --orange-primary: #ff622d;
    --orange-link: #dd3500;
    --orange-link-bg: #ffe5db;
    --green-primary: #009f81;

    --amber-a1: color(display-p3 0.757 0.514 0.024 / 0.016);
    --amber-a2: color(display-p3 0.902 0.804 0.008 / 0.079);
    --amber-a3: color(display-p3 0.965 0.859 0.004 / 0.22);
    --amber-a4: color(display-p3 0.969 0.82 0.004 / 0.35);
    --amber-a5: color(display-p3 0.933 0.796 0.004 / 0.475);
    --amber-a6: color(display-p3 0.875 0.682 0.004 / 0.495);
    --amber-a7: color(display-p3 0.804 0.573 0 / 0.557);
    --amber-a8: color(display-p3 0.788 0.502 0 / 0.699);
    --amber-a9: color(display-p3 1 0.686 0 / 0.742);
    --amber-a10: color(display-p3 0.945 0.643 0 / 0.726);
    --amber-a11: color(display-p3 0.64 0.4 0);
    --amber-a12: color(display-p3 0.294 0.208 0.145);


        --slate-a1: color(display-p3 0.024 0.024 0.349 / 0.012);
    --slate-a2: color(display-p3 0.024 0.024 0.349 / 0.024);
    --slate-a3: color(display-p3 0.004 0.004 0.204 / 0.059);
    --slate-a4: color(display-p3 0.012 0.012 0.184 / 0.091);
    --slate-a5: color(display-p3 0.004 0.039 0.2 / 0.122);
    --slate-a6: color(display-p3 0.008 0.008 0.165 / 0.15);
    --slate-a7: color(display-p3 0.008 0.027 0.184 / 0.197);
    --slate-a8: color(display-p3 0.004 0.031 0.176 / 0.275);
    --slate-a9: color(display-p3 0.004 0.02 0.106 / 0.455);
    --slate-a10: color(display-p3 0.004 0.027 0.098 / 0.499);
    --slate-a11: color(display-p3 0 0.02 0.063 / 0.62);
    --slate-a12: color(display-p3 0 0.012 0.031 / 0.887)

  }

  .text-yellow-10 {
    color: var(--amber-a10);
  }

  .text-yellow-11 {
    color: var(--amber-a11);
}

.bg-yellow-3 {
    background-color: var(--amber-a3);
}


.bg-slate-3 {
    background-color: var(--slate-a3);
}

.from-yellow-4 {
    --tw-gradient-from: var(--amber-a4) var(--tw-gradient-from-position);
    --tw-gradient-to: hsla(0, 0%, 100%, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.border-slate-6 {
    border-color: var(--slate-a6);
}
    


</style>


<section class="mt-14 w-9/12 m-auto">
  <div class="flex items-center gap-2"><h2 class="text-slate-12 text-lg font-bold">Explore more</h2></div>
  <p class="text-slate-11 mt-1 text-sm font-normal">Continue unlocking Resend's full capabilities and setup</p>
  <div class="mt-6 flex flex-col gap-8 md:grid md:grid-cols-2">
    <div class="border-slate-6 relative w-full rounded-2xl border">
      <div class="absolute right-0 top-0 h-px w-[200px]" style="background: linear-gradient(90deg, rgba(56, 189, 248, 0) 0%, rgba(56, 189, 248, 0) 0%, rgba(232, 232, 232, 0.2) 33.02%, rgba(143, 143, 143, 0.67) 64.41%, rgba(236, 72, 153, 0) 98.93%);"></div>
      <div class="border-slate-6 flex justify-between border-b p-4">
        <div>
          <div class="flex items-center gap-2">
            <h3 class="text-slate-12 text-base font-semibold">Add a domain</h3>
            <span class="bg-green-3 text-green-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded px-2 text-xs font-medium">Recommended</span>
          </div>
          <p class="text-slate-11 mt-1 text-sm font-normal">Verify and send emails from your own custom domains</p>
        </div>
        <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
          <path clip-rule="evenodd" d="M16 32C24.5007 32 31.4531 25.3707 31.9693 17H32V16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16V17H0.030742C0.546938 25.3707 7.4993 32 16 32ZM18.7113 30.7556C25.3821 29.5378 30.5125 23.9075 30.9672 17H22.9866C22.8191 23.207 21.1034 28.4565 18.7113 30.7556ZM13.2887 1.2444C6.29867 2.5205 1 8.64147 1 16H9C9 9.36077 10.7692 3.66602 13.2887 1.2444ZM10 16C10 11.6863 10.7669 7.82877 11.9664 5.08711C12.5668 3.7148 13.2559 2.66703 13.9703 1.97631C14.68 1.29024 15.3644 1 16 1C16.6356 1 17.32 1.29024 18.0297 1.97631C18.7441 2.66703 19.4332 3.7148 20.0336 5.08711C21.2331 7.82877 22 11.6863 22 16H10ZM10.0138 17C10.1222 20.9082 10.8604 24.3849 11.9664 26.9129C12.5668 28.2852 13.2559 29.333 13.9703 30.0237C14.68 30.7098 15.3644 31 16 31C16.6356 31 17.32 30.7098 18.0297 30.0237C18.7441 29.333 19.4332 28.2852 20.0336 26.9129C21.1396 24.3849 21.8778 20.9082 21.9862 17H10.0138ZM9.01345 17C9.18091 23.207 10.8966 28.4565 13.2887 30.7556C6.61788 29.5378 1.48746 23.9075 1.0328 17H9.01345ZM23 16H31C31 8.64147 25.7013 2.5205 18.7113 1.2444C21.2308 3.66602 23 9.36077 23 16Z" fill="#606060" fill-rule="evenodd"></path>
          <path clip-rule="evenodd" d="M16 32C24.5007 32 31.4531 25.3707 31.9693 17H32V16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16V17H0.030742C0.546938 25.3707 7.4993 32 16 32ZM18.7113 30.7556C25.3821 29.5378 30.5125 23.9075 30.9672 17H22.9866C22.8191 23.207 21.1034 28.4565 18.7113 30.7556ZM13.2887 1.2444C6.29867 2.5205 1 8.64147 1 16H9C9 9.36077 10.7692 3.66602 13.2887 1.2444ZM10 16C10 11.6863 10.7669 7.82877 11.9664 5.08711C12.5668 3.7148 13.2559 2.66703 13.9703 1.97631C14.68 1.29024 15.3644 1 16 1C16.6356 1 17.32 1.29024 18.0297 1.97631C18.7441 2.66703 19.4332 3.7148 20.0336 5.08711C21.2331 7.82877 22 11.6863 22 16H10ZM10.0138 17C10.1222 20.9082 10.8604 24.3849 11.9664 26.9129C12.5668 28.2852 13.2559 29.333 13.9703 30.0237C14.68 30.7098 15.3644 31 16 31C16.6356 31 17.32 30.7098 18.0297 30.0237C18.7441 29.333 19.4332 28.2852 20.0336 26.9129C21.1396 24.3849 21.8778 20.9082 21.9862 17H10.0138ZM9.01345 17C9.18091 23.207 10.8966 28.4565 13.2887 30.7556C6.61788 29.5378 1.48746 23.9075 1.0328 17H9.01345ZM23 16H31C31 8.64147 25.7013 2.5205 18.7113 1.2444C21.2308 3.66602 23 9.36077 23 16Z" fill="url(#paint0_radial_146_5114)" fill-rule="evenodd"></path>
          <defs>
            <radialGradient cx="0" cy="0" gradientTransform="translate(26 -9.22591e-06) rotate(120.098) scale(63.8119 62.57)" gradientUnits="userSpaceOnUse" id="paint0_radial_146_5114" r="1">
              <stop stop-color="white" stop-opacity="0.3"></stop>
              <stop offset="0.25" stop-color="white" stop-opacity="0"></stop>
              <stop offset="0.5" stop-color="white" stop-opacity="0"></stop>
              <stop offset="0.75" stop-color="white" stop-opacity="0"></stop>
              <stop offset="1" stop-color="white" stop-opacity="0"></stop>
            </radialGradient>
          </defs>
        </svg>
      </div>
      <div class="p-4"><a class="border-slate-6 inline-flex h-8 select-none items-center justify-center gap-1 rounded-md border bg-black pl-3 pr-3 text-sm font-semibold text-white transition duration-200 ease-in-out hover:bg-black/90 focus-visible:bg-black/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black/40 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-black dark:bg-white dark:text-black dark:hover:bg-white/90 dark:focus-visible:bg-white/90 dark:focus-visible:ring-white/40 dark:disabled:hover:bg-white" href="/domains?new=true">Add domain</a></div>
    </div>
    <div class="border-slate-6 relative w-full rounded-2xl border">
      <div class="absolute right-0 top-0 h-px w-[200px]" style="background: linear-gradient(90deg, rgba(56, 189, 248, 0) 0%, rgba(56, 189, 248, 0) 0%, rgba(232, 232, 232, 0.2) 33.02%, rgba(143, 143, 143, 0.67) 64.41%, rgba(236, 72, 153, 0) 98.93%);"></div>
      <div class="border-slate-6 flex justify-between border-b p-4">
        <div>
          <div class="flex items-center gap-2"><h3 class="text-slate-12 text-base font-semibold">Invite a team member</h3></div>
          <p class="text-slate-11 mt-1 text-sm font-normal">Add new team members and streamline collaboration</p>
        </div>
        <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
          <path clip-rule="evenodd" d="M12.4167 10.6667C12.4167 8.68765 14.021 7.08333 16 7.08333C17.979 7.08333 19.5833 8.68765 19.5833 10.6667C19.5833 12.6457 17.979 14.25 16 14.25C14.021 14.25 12.4167 12.6457 12.4167 10.6667ZM16 5.58333C13.1926 5.58333 10.9167 7.85922 10.9167 10.6667C10.9167 13.4741 13.1926 15.75 16 15.75C18.8074 15.75 21.0833 13.4741 21.0833 10.6667C21.0833 7.85922 18.8074 5.58333 16 5.58333ZM7.96648 23.3093C8.99342 21.575 11.2504 19.4167 16.3333 19.4167C16.7475 19.4167 17.0833 19.0809 17.0833 18.6667C17.0833 18.2525 16.7475 17.9167 16.3333 17.9167C10.7212 17.9167 7.97317 20.354 6.67579 22.5451C6.11277 23.4959 6.2405 24.5097 6.80505 25.2566C7.34914 25.9765 8.26624 26.4167 9.26604 26.4167H16.3333C16.7475 26.4167 17.0833 26.0809 17.0833 25.6667C17.0833 25.2525 16.7475 24.9167 16.3333 24.9167H9.26604C8.69319 24.9167 8.23719 24.6637 8.00168 24.3521C7.78661 24.0676 7.72822 23.7117 7.96648 23.3093ZM22.6667 18.9167C23.0809 18.9167 23.4167 19.2525 23.4167 19.6667V21.9167H25.6667C26.0809 21.9167 26.4167 22.2525 26.4167 22.6667C26.4167 23.0809 26.0809 23.4167 25.6667 23.4167H23.4167V25.6667C23.4167 26.0809 23.0809 26.4167 22.6667 26.4167C22.2525 26.4167 21.9167 26.0809 21.9167 25.6667V23.4167H19.6667C19.2525 23.4167 18.9167 23.0809 18.9167 22.6667C18.9167 22.2525 19.2525 21.9167 19.6667 21.9167H21.9167V19.6667C21.9167 19.2525 22.2525 18.9167 22.6667 18.9167Z" fill="#606060" fill-rule="evenodd"></path>
          <path clip-rule="evenodd" d="M12.4167 10.6667C12.4167 8.68765 14.021 7.08333 16 7.08333C17.979 7.08333 19.5833 8.68765 19.5833 10.6667C19.5833 12.6457 17.979 14.25 16 14.25C14.021 14.25 12.4167 12.6457 12.4167 10.6667ZM16 5.58333C13.1926 5.58333 10.9167 7.85922 10.9167 10.6667C10.9167 13.4741 13.1926 15.75 16 15.75C18.8074 15.75 21.0833 13.4741 21.0833 10.6667C21.0833 7.85922 18.8074 5.58333 16 5.58333ZM7.96648 23.3093C8.99342 21.575 11.2504 19.4167 16.3333 19.4167C16.7475 19.4167 17.0833 19.0809 17.0833 18.6667C17.0833 18.2525 16.7475 17.9167 16.3333 17.9167C10.7212 17.9167 7.97317 20.354 6.67579 22.5451C6.11277 23.4959 6.2405 24.5097 6.80505 25.2566C7.34914 25.9765 8.26624 26.4167 9.26604 26.4167H16.3333C16.7475 26.4167 17.0833 26.0809 17.0833 25.6667C17.0833 25.2525 16.7475 24.9167 16.3333 24.9167H9.26604C8.69319 24.9167 8.23719 24.6637 8.00168 24.3521C7.78661 24.0676 7.72822 23.7117 7.96648 23.3093ZM22.6667 18.9167C23.0809 18.9167 23.4167 19.2525 23.4167 19.6667V21.9167H25.6667C26.0809 21.9167 26.4167 22.2525 26.4167 22.6667C26.4167 23.0809 26.0809 23.4167 25.6667 23.4167H23.4167V25.6667C23.4167 26.0809 23.0809 26.4167 22.6667 26.4167C22.2525 26.4167 21.9167 26.0809 21.9167 25.6667V23.4167H19.6667C19.2525 23.4167 18.9167 23.0809 18.9167 22.6667C18.9167 22.2525 19.2525 21.9167 19.6667 21.9167H21.9167V19.6667C21.9167 19.2525 22.2525 18.9167 22.6667 18.9167Z" fill="url(#paint0_radial_0_3)" fill-rule="evenodd"></path>
          <defs>
            <radialGradient cx="0" cy="0" gradientTransform="translate(22.6471 5.58333) rotate(119.22) scale(41.1836 39.6549)" gradientUnits="userSpaceOnUse" id="paint0_radial_0_3" r="1">
              <stop stop-color="white" stop-opacity="0.3"></stop>
              <stop offset="0.25" stop-color="white" stop-opacity="0"></stop>
              <stop offset="0.5" stop-color="white" stop-opacity="0"></stop>
              <stop offset="0.75" stop-color="white" stop-opacity="0"></stop>
              <stop offset="1" stop-color="white" stop-opacity="0"></stop>
            </radialGradient>
          </defs>
        </svg>
      </div>
      <div class="p-4"><a class="border-slate-6 inline-flex h-8 select-none items-center justify-center gap-1 rounded-md border bg-black pl-3 pr-3 text-sm font-semibold text-white transition duration-200 ease-in-out hover:bg-black/90 focus-visible:bg-black/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black/40 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-black dark:bg-white dark:text-black dark:hover:bg-white/90 dark:focus-visible:bg-white/90 dark:focus-visible:ring-white/40 dark:disabled:hover:bg-white" href="/settings/team">Invite a member</a></div>
    </div>
  </div>
</section>





<div role="dialog" aria-labelledby="radix-:r5v:" data-state="open" class="my-12 mx-auto bg-root border-slate-6 __variable_469f07 data-[state=closed]:animate-fade-out data-[state=open]:animate-open-scale-in-fade z-40 w-full max-w-lg  overflow-y-auto border p-6 font-sans duration-200 sm:rounded-lg" tabindex="-1" style="pointer-events: auto;">
  <h2 class="text-slate-12 text-base font-semibold" id="radix-:r5v:">Add Domain</h2>
  <form>
    <div class="mt-6 flex flex-col space-y-2">
      <div class="flex items-center">
        <label for="domain" class="text-slate-11 flex select-none items-center text-sm text-sm">Domain</label
        ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg" data-state="closed">
          <path d="M12 13V15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
          <path d="M12 10C12.5523 10 13 9.55228 13 9C13 8.44772 12.5523 8 12 8C11.4477 8 11 8.44772 11 9C11 9.55228 11.4477 10 12 10Z" fill="currentColor"></path>
          <path d="M12 19.25C16.0041 19.25 19.25 16.0041 19.25 12C19.25 7.99594 16.0041 4.75 12 4.75C7.99594 4.75 4.75 7.99594 4.75 12C4.75 16.0041 7.99594 19.25 12 19.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
        </svg>
      </div>
      <input class="border-slate-6 bg-slate-3 text-slate-12 focus-visible:ring-slate-7 placeholder:text-slate-11 data-[state=&quot;read-only&quot;]:cursor-default data-[state=&quot;read-only&quot;]:border-slate-4 data-[state=&quot;read-only&quot;]:bg-slate-5 data-[state=&quot;read-only&quot;]:text-slate-10 relative h-8 w-full select-none appearance-none rounded-md border px-2 pl-[var(--text-field-left-slot-width)] pr-[var(--text-field-right-slot-width)] text-sm outline-none transition duration-200 ease-in-out focus-visible:ring-2" data-1p-ignore="true" type="text" required="" autocomplete="off" id="domain" placeholder="updates.example.com" value="" />
    </div>
    <div class="mt-6 flex flex-col space-y-2">
      <div class="flex items-center">
        <label for="region" class="text-slate-11 flex select-none items-center text-sm text-sm">Region</label
        ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg" data-state="closed">
          <path d="M12 13V15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
          <path d="M12 10C12.5523 10 13 9.55228 13 9C13 8.44772 12.5523 8 12 8C11.4477 8 11 8.44772 11 9C11 9.55228 11.4477 10 12 10Z" fill="currentColor"></path>
          <path d="M12 19.25C16.0041 19.25 19.25 16.0041 19.25 12C19.25 7.99594 16.0041 4.75 12 4.75C7.99594 4.75 4.75 7.99594 4.75 12C4.75 16.0041 7.99594 19.25 12 19.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
        </svg>
      </div>
      <button class="border-slate-6 bg-slate-3 focus-visible:ring-slate-6 relative inline-flex h-8 w-full cursor-pointer select-none appearance-none items-center justify-between truncate rounded-md border pl-2 text-base text-sm outline-none focus-visible:ring-2 sm:text-sm" type="button" role="combobox" aria-controls="radix-:r6c:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed">
        <span style="pointer-events: none;"
          ><div class="flex w-full flex-row">
            <div class="flex flex-row gap-2">
              <div class="flex items-center gap-2">
                <img alt="North Virginia" loading="lazy" width="24" height="18" decoding="async" data-nimg="1" class="rounded" src="https://resend.com/static/flags/us.svg" style="color: transparent;" /><span>North Virginia <span class="text-slate-11">(us-east-1)</span></span>
              </div>
            </div>
          </div></span
        ><span aria-hidden="true"
          ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M15.25 10.75L12 14.25L8.75 10.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
        ></span></button
      ><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;">
        <option value="us-east-1" selected="">North Virginia (us-east-1)</option>
        <option value="eu-west-1">Ireland (eu-west-1)Pro</option>
        <option value="sa-east-1">São Paulo (sa-east-1)Pro</option>
        <option value="ap-northeast-1">Tokyo (ap-northeast-1)Pro</option>
      </select>
    </div>
    <div class="mt-6 flex items-center gap-2"><button class="border-slate-6 inline-flex h-8 cursor-pointer select-none items-center justify-center gap-1 rounded-md border bg-black pl-3 pr-3 text-sm font-semibold text-white transition duration-200 ease-in-out hover:bg-black/90 focus-visible:bg-black/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black/40 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-black dark:bg-white dark:text-black dark:hover:bg-white/90 dark:focus-visible:bg-white/90 dark:focus-visible:ring-white/40 dark:disabled:hover:bg-white" data-state="disabled" disabled="" type="submit">Add</button><button class="text-slate-11 hover:bg-slate-5 focus-visible:ring-slate-7 focus-visible:bg-slate-6 disabled:hover:bg-slate-1 inline-flex h-8 cursor-pointer select-none items-center justify-center gap-1 rounded-md border border-transparent bg-transparent pl-3 pr-3 text-sm font-semibold transition duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" type="button">Cancel</button></div>
  </form>
  <button class="text-slate-11 hover:bg-slate-5 focus-visible:ring-slate-7 focus-visible:bg-slate-6 disabled:hover:bg-slate-1 absolute right-6 top-6 inline-flex h-6 w-6 cursor-pointer items-center justify-center rounded border border-none bg-transparent transition duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" type="button">
    <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
      <path d="M17.25 6.75L6.75 17.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
      <path d="M6.75 6.75L17.25 17.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
    </svg>
  </button>
</div>




<div class=" overflow-auto pb-10 mt-28 border-t pt-8">
  <div class="mx-auto flex max-w-5xl flex-col px-6 py-8">
    <div class="flex flex-col items-center gap-6 md:flex-row">
      <div class="relative shrink-0" style="width: 80px; height: 80px;">
        <svg class="text-yellow-10 absolute left-0 top-0" fill="none" height="80" id="status-icon" viewBox="0 0 80 80" width="80" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#a)">
            <rect fill="currentcolor" fill-opacity="0.1" height="80" rx="18" width="80"></rect>
            <rect fill="url(#status-icon-fill-dark)" height="80" rx="18" width="80"></rect>
            <mask height="80" id="d" maskUnits="userSpaceOnUse" width="80" x="0" y="0" style="mask-type: alpha;"><path d="M0 0h80v80H0z" fill="url(#c)"></path></mask>
            <g mask="url(#d)">
              <path d="M1.5 1.5h13v13h-13zM14.5 1.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M28 2h12v12H28z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M27.5 1.5h13v13h-13zM40.5 1.5h13v13h-13zM53.5 1.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M67 2h12v12H67z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M66.5 1.5h13v13h-13zM1.5 14.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M15 15h12v12H15z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M14.5 14.5h13v13h-13zM27.5 14.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M41 15h12v12H41z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M40.5 14.5h13v13h-13zM53.5 14.5h13v13h-13zM66.5 14.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M2 28h12v12H2z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M1.5 27.5h13v13h-13zM14.5 27.5h13v13h-13zM27.5 27.5h13v13h-13zM40.5 27.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M54 28h12v12H54z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M53.5 27.5h13v13h-13zM66.5 27.5h13v13h-13zM1.5 40.5h13v13h-13zM14.5 40.5h13v13h-13zM27.5 40.5h13v13h-13zM40.5 40.5h13v13h-13zM53.5 40.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M67 41h12v12H67z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M66.5 40.5h13v13h-13zM1.5 53.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M15 54h12v12H15z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M14.5 53.5h13v13h-13zM27.5 53.5h13v13h-13zM40.5 53.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M54 54h12v12H54z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M53.5 53.5h13v13h-13zM66.5 53.5h13v13h-13zM1.5 66.5h13v13h-13zM14.5 66.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
              <path d="M28 67h12v12H28z" fill="#000" fill-opacity="0.1" style="fill: rgb(0, 0, 0); fill-opacity: 0.1;"></path>
              <path d="M27.5 66.5h13v13h-13zM40.5 66.5h13v13h-13zM53.5 66.5h13v13h-13zM66.5 66.5h13v13h-13z" stroke="#000" stroke-opacity="0.03" style="stroke: rgb(0, 0, 0); stroke-opacity: 0.03;"></path>
            </g>
            <rect height="74" rx="15" stroke="#fff" stroke-width="6" width="74" x="3" y="3" style="stroke: rgb(255, 255, 255); stroke-opacity: 1;"></rect>
          </g>
          <rect height="77" rx="16.5" stroke="#D6D6D6" stroke-width="3" width="77" x="1.5" y="1.5" style="stroke: rgb(214, 214, 214); stroke-opacity: 1;"></rect>
          <defs>
            <radialGradient cx="0" cy="0" gradientTransform="matrix(0 40 -40 0 40 40)" gradientUnits="userSpaceOnUse" id="c" r="1">
              <stop style="stop-color: black; stop-opacity: 1;"></stop>
              <stop offset="1" stop-color="#fff" stop-opacity="0" style="stop-opacity: 0;"></stop>
              <stop offset="1" stop-opacity="0" style="stop-opacity: 0;"></stop>
            </radialGradient>
          </defs></svg
        ><svg class="text-yellow-10 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2" fill="currentColor" fill-opacity="0.9" filter="brightness(0.6)" height="36" viewBox="0 0 32 32" width="36">
          <path d="M16 1C7.729 1 1 7.72894 1 16C1 24.2711 7.729 31 16 31C24.2711 31 31 24.2711 31 16C31 7.72894 24.2711 1 16 1ZM28.6963 14.8733H21.988C21.8877 10.5432 21.2114 6.65073 20.1206 3.93709C24.7925 5.53739 28.2481 9.78025 28.6963 14.8733ZM17.312 3.32042C18.6756 5.50844 19.6082 9.96837 19.7332 14.8733H12.2662C12.4017 9.35378 13.5184 5.23711 14.6917 3.31999C15.122 3.27593 15.5584 3.25325 16 3.25325C16.4429 3.25325 16.8806 3.27607 17.312 3.32042ZM11.8842 3.93537C10.7909 6.65598 10.1128 10.5503 10.012 14.8733H3.30371C3.75207 9.77853 7.20987 5.53436 11.8842 3.93537ZM3.30371 17.1267H10.012C10.1128 21.4496 10.7908 25.3439 11.8841 28.0646C7.2098 26.4655 3.75207 22.2214 3.30371 17.1267ZM14.6916 28.6799C13.5183 26.7627 12.4016 22.6461 12.2662 17.1267H19.7332C19.6082 22.0316 18.6757 26.4913 17.3122 28.6795C16.8808 28.7239 16.443 28.7467 16 28.7467C15.5584 28.7467 15.1219 28.724 14.6916 28.6799ZM20.1207 28.0628C21.2115 25.3491 21.8877 21.4567 21.988 17.1267H28.6963C28.2481 22.2197 24.7926 26.4625 20.1207 28.0628Z"></path>
          <defs>
            <linearGradient gradientUnits="userSpaceOnUse" id="status-icon-fill-dark" x1="0" x2="10" y1="0" y2="45">
              <stop stop-color="white"></stop>
              <stop offset="0.2" stop-color="white"></stop>
              <stop offset="1" stop-color="white" stop-opacity="0"></stop>
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div class="w-full overflow-hidden text-center md:text-left">
        <span class="text-slate-11 text-sm font-semibold">Domain</span>
        <h1 class="text-slate-12 w-full truncate text-[28px] font-bold leading-[34px] tracking-[-0.416px] md:max-w-[800px]">mail.seoflicker.com</h1>
      </div>
      <div class="flex-end flex shrink-0 items-center gap-2">
        <button class="dark:bg-slate-3 border-slate-6 dark:text-slate-11 bg-slate-2 text-slate-12 hover:bg-slate-4 focus-visible:ring-slate-7 focus-visible:bg-slate-4 disabled:hover:bg-slate-4 inline-flex h-8 cursor-pointer select-none items-center justify-center gap-1 rounded-md border pl-2 pr-3 text-sm !font-normal font-semibold transition duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
          <span class="text-[#70757E]"
            ><div style="width: 18px; height: 18px;">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 500 500" width="500" height="500" preserveAspectRatio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
                <defs>
                  <clipPath id="__lottie_element_799"><rect width="500" height="500" x="0" y="0"></rect></clipPath>
                  <clipPath id="__lottie_element_802"><path d="M0,0 L500,0 L500,500 L0,500z"></path></clipPath>
                </defs>
                <g clip-path="url(#__lottie_element_799)">
                  <g clip-path="url(#__lottie_element_802)" transform="matrix(1,0,0,1,0,0)" opacity="1" style="display: block;">
                    <g class="primary design" transform="matrix(20.829999923706055,0,0,20.829999923706055,-4957.498046875,-4957.49267578125)" opacity="1" style="display: block;">
                      <g opacity="1" transform="matrix(1,0,0,1,256.9949951171875,250.00100708007812)"><path class="primary" fill="rgb(157,159,167)" fill-opacity="1" d=" M-1.6749999523162842,-5.730999946594238 C-1.9450000524520874,-6.051000118255615 -2.4149999618530273,-6.091000080108643 -2.734999895095825,-5.821000099182129 C-3.055000066757202,-5.551000118255615 -3.0950000286102295,-5.080999851226807 -2.825000047683716,-4.761000156402588 C-2.825000047683716,-4.761000156402588 1.2549999952316284,0.07900000363588333 1.2549999952316284,0.07900000363588333 C1.2549999952316284,0.07900000363588333 -2.825000047683716,4.758999824523926 -2.825000047683716,4.758999824523926 C-3.0950000286102295,5.068999767303467 -3.065000057220459,5.548999786376953 -2.755000114440918,5.818999767303467 C-2.5950000286102295,5.939000129699707 -2.424999952316284,5.999000072479248 -2.244999885559082,5.999000072479248 C-2.0350000858306885,5.999000072479248 -1.8250000476837158,5.908999919891357 -1.6749999523162842,5.738999843597412 C-1.6749999523162842,5.738999843597412 2.825000047683716,0.5690000057220459 2.825000047683716,0.5690000057220459 C3.065000057220459,0.289000004529953 3.075000047683716,-0.12099999934434891 2.8350000381469727,-0.41100001335144043 C2.8350000381469727,-0.41100001335144043 -1.6749999523162842,-5.730999946594238 -1.6749999523162842,-5.730999946594238z"></path></g>
                      <g opacity="1" transform="matrix(1,0,0,1,242.9969940185547,250)"><path class="primary" fill="rgb(157,159,167)" fill-opacity="1" d=" M2.743000030517578,-5.820000171661377 C2.433000087738037,-6.090000152587891 1.9630000591278076,-6.050000190734863 1.6829999685287476,-5.739999771118164 C1.6829999685287476,-5.739999771118164 -2.816999912261963,-0.5699999928474426 -2.816999912261963,-0.5699999928474426 C-3.056999921798706,-0.28999999165534973 -3.066999912261963,0.11999999731779099 -2.8269999027252197,0.4099999964237213 C-2.8269999027252197,0.4099999964237213 1.6729999780654907,5.739999771118164 1.6729999780654907,5.739999771118164 C1.8229999542236328,5.909999847412109 2.0429999828338623,6 2.253000020980835,6 C2.4230000972747803,6 2.5929999351501465,5.940000057220459 2.7330000400543213,5.820000171661377 C3.052999973297119,5.550000190734863 3.0929999351501465,5.079999923706055 2.822999954223633,4.760000228881836 C2.822999954223633,4.760000228881836 -1.2569999694824219,-0.07999999821186066 -1.2569999694824219,-0.07999999821186066 C-1.2569999694824219,-0.07999999821186066 2.822999954223633,-4.760000228881836 2.822999954223633,-4.760000228881836 C3.0929999351501465,-5.070000171661377 3.052999973297119,-5.539999961853027 2.743000030517578,-5.820000171661377z"></path></g>
                      <g opacity="1" transform="matrix(1,0,0,1,250,249.99600219726562)"><path class="primary" fill="rgb(157,159,167)" fill-opacity="1" d=" M1.7000000476837158,-6.0960001945495605 C1.2999999523162842,-6.205999851226807 0.8899999856948853,-5.97599983215332 0.7799999713897705,-5.576000213623047 C0.7799999713897705,-5.576000213623047 -2.2200000286102295,5.173999786376953 -2.2200000286102295,5.173999786376953 C-2.3299999237060547,5.573999881744385 -2.0999999046325684,5.984000205993652 -1.7000000476837158,6.093999862670898 C-1.6299999952316284,6.113999843597412 -1.5700000524520874,6.124000072479248 -1.5,6.124000072479248 C-1.1699999570846558,6.124000072479248 -0.8700000047683716,5.9039998054504395 -0.7799999713897705,5.573999881744385 C-0.7799999713897705,5.573999881744385 2.2200000286102295,-5.176000118255615 2.2200000286102295,-5.176000118255615 C2.3299999237060547,-5.565999984741211 2.0999999046325684,-5.986000061035156 1.7000000476837158,-6.0960001945495605z"></path></g>
                    </g>
                    <g class="primary design" style="display: none;">
                      <g><path class="primary"></path></g>
                      <g><path class="primary"></path></g>
                      <g><path class="primary"></path></g>
                    </g>
                    <g class="primary design" style="display: none;">
                      <g><path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" class="primary"></path></g>
                    </g>
                    <g class="primary design" style="display: none;">
                      <g><path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" class="primary"></path></g>
                    </g>
                    <g class="primary design" style="display: none;">
                      <g><path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" class="primary"></path></g>
                    </g>
                  </g>
                </g>
              </svg></div></span
          >API
        </button>
        <div class="flex items-center gap-2">
          <svg class="text-[#70757E]" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg" data-state="closed">
            <path d="M17.3051 6.6975C15.8869 5.28 14.0035 4.5 12 4.5C9.9965 4.5 8.11306 5.28 6.69485 6.6975C3.76838 9.6225 3.76838 14.3775 6.69485 17.3025C8.15808 18.765 10.079 19.5 12 19.5C13.921 19.5 15.8419 18.765 17.3051 17.3025C20.2316 14.3775 20.2316 9.6225 17.3051 6.6975ZM16.5098 16.5075C14.026 18.99 9.97399 18.9975 7.49024 16.5075C4.999 14.025 4.999 9.975 7.49024 7.4925C8.69084 6.285 10.2966 5.625 12 5.625C13.7033 5.625 15.3016 6.285 16.5098 7.4925C18.9935 9.975 18.9935 14.025 16.5098 16.5075Z" fill="currentColor"></path>
            <path d="M15.5868 15.585C15.4743 15.6975 15.3317 15.75 15.1891 15.75C15.0466 15.75 14.904 15.6975 14.7914 15.585L11.6023 12.3975C11.5498 12.345 11.5123 12.285 11.4823 12.2175C11.4523 12.15 11.4373 12.075 11.4373 12V8.84253C11.4373 8.53503 11.6924 8.28003 12 8.28003C12.3152 8.28003 12.5628 8.53503 12.5628 8.84253V11.7675L15.5868 14.79C15.8044 15.015 15.8044 15.3675 15.5868 15.585Z" fill="currentColor"></path></svg
          ><button class="bg-slate-6 relative h-[34px] cursor-wait overflow-hidden rounded-md px-12 will-change-transform focus-visible:outline-none"><span class="bg-root text-slate-11 absolute inset-px z-10 grid place-items-center rounded-md text-sm">Verifying...</span><span aria-hidden="true" class="before:animate-disco before:bg-gradient-conic before:to-slate-4 absolute inset-0 z-0 scale-x-[2.0] blur before:absolute before:inset-0 before:top-1/2 before:aspect-square before:from-white"></span></button>
        </div>
        <button class="dark:bg-slate-3 border-slate-6 dark:text-slate-11 bg-slate-2 text-slate-12 hover:bg-slate-4 focus-visible:ring-slate-7 focus-visible:bg-slate-4 disabled:hover:bg-slate-4 inline-flex h-8 w-8 cursor-pointer items-center justify-center rounded-md border transition duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed" aria-label="More actions" type="button" id="radix-:r1j:" aria-haspopup="menu" aria-expanded="false">
          <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
            <path d="M13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z" fill="currentColor"></path>
            <path d="M9 12C9 12.5523 8.55228 13 8 13C7.44772 13 7 12.5523 7 12C7 11.4477 7.44772 11 8 11C8.55228 11 9 11.4477 9 12Z" fill="currentColor"></path>
            <path d="M17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12Z" fill="currentColor"></path>
          </svg>
        </button>
      </div>
    </div>
    <div class="flex-1"></div>
    <div class="grid grid-cols-2 items-center gap-12 pb-4 pt-10 md:flex md:gap-16">
      <div class="flex flex-col gap-2">
        <label class="text-slate-11 select-none text-sm text-xs uppercase">Created</label>
        <div class="flex items-center gap-2">
          <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
            <path d="M17.3051 6.6975C15.8869 5.28 14.0035 4.5 12 4.5C9.9965 4.5 8.11306 5.28 6.69485 6.6975C3.76838 9.6225 3.76838 14.3775 6.69485 17.3025C8.15808 18.765 10.079 19.5 12 19.5C13.921 19.5 15.8419 18.765 17.3051 17.3025C20.2316 14.3775 20.2316 9.6225 17.3051 6.6975ZM16.5098 16.5075C14.026 18.99 9.97399 18.9975 7.49024 16.5075C4.999 14.025 4.999 9.975 7.49024 7.4925C8.69084 6.285 10.2966 5.625 12 5.625C13.7033 5.625 15.3016 6.285 16.5098 7.4925C18.9935 9.975 18.9935 14.025 16.5098 16.5075Z" fill="currentColor"></path>
            <path d="M15.5868 15.585C15.4743 15.6975 15.3317 15.75 15.1891 15.75C15.0466 15.75 14.904 15.6975 14.7914 15.585L11.6023 12.3975C11.5498 12.345 11.5123 12.285 11.4823 12.2175C11.4523 12.15 11.4373 12.075 11.4373 12V8.84253C11.4373 8.53503 11.6924 8.28003 12 8.28003C12.3152 8.28003 12.5628 8.53503 12.5628 8.84253V11.7675L15.5868 14.79C15.8044 15.015 15.8044 15.3675 15.5868 15.585Z" fill="currentColor"></path></svg
          ><span class="text-slate-12 text-sm font-normal"
            ><button data-state="closed" class="cursor-auto"><time class="text-current" datetime="2024-03-25T03:36:25.966237+00:00">1 minute ago</time></button></span
          >
        </div>
      </div>
      <div class="flex flex-col gap-2">
        <label class="text-slate-11 select-none text-sm text-xs uppercase">Status</label>
        <div class="flex items-center gap-2"><span class="bg-yellow-3 text-yellow-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded px-2 text-xs font-medium capitalize">pending</span></div>
      </div>
      <div class="flex flex-col gap-2">
        <label class="text-slate-11 select-none text-sm text-xs uppercase">Region</label>
        <div class="flex items-center gap-2">
          <img alt="us-east-1" loading="lazy" width="24" height="18" decoding="async" data-nimg="1" class="rounded" src="https://resend.com/static/flags/us.svg" style="color: transparent;" /><span class="text-slate-12 text-sm font-normal">North Virginia <span class="text-slate-11">(us-east-1)</span></span>
        </div>
      </div>
      <div class="flex flex-col gap-2">
        <label class="text-slate-11 select-none text-sm text-xs uppercase">DNS Provider</label
        ><a target="_blank" href="https://dash.cloudflare.com"
          ><div class="flex items-center gap-2">
            <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
              <rect fill="white" height="24" rx="12" width="24"></rect>
              <path d="M15.6549 15.8589L15.7572 15.4676C15.8791 15.0023 15.8337 14.572 15.6293 14.256C15.4411 13.9648 15.1276 13.7934 14.7468 13.7734L7.53508 13.6725C7.51269 13.672 7.49073 13.6657 7.47098 13.6541C7.45117 13.6424 7.43421 13.6258 7.42128 13.6056C7.4087 13.5847 7.40064 13.5608 7.39779 13.5359C7.39501 13.5109 7.39751 13.4856 7.40509 13.4618C7.41746 13.4228 7.43991 13.3885 7.46966 13.3634C7.49949 13.3383 7.53521 13.3233 7.57261 13.3204L14.8512 13.2183C15.7146 13.1745 16.6494 12.3998 16.9767 11.4551L17.3917 10.2558C17.4088 10.2051 17.4127 10.1502 17.403 10.0972C16.9317 7.75297 15.0397 6 12.7775 6C10.6932 6 8.92347 7.48756 8.28867 9.55518C7.8597 9.19915 7.32487 9.03548 6.79142 9.09694C5.79147 9.20674 4.98748 10.097 4.88849 11.2028C4.86312 11.479 4.88168 11.7579 4.94348 12.0269C3.30998 12.0796 2 13.5594 2 15.3785C2.00021 15.5409 2.01105 15.703 2.03246 15.8637C2.03719 15.9008 2.0538 15.9348 2.07931 15.9595C2.10483 15.9842 2.13757 15.9979 2.17142 15.9981L15.4856 15.9999C15.4869 16 15.4881 16 15.4894 15.9999C15.527 15.9992 15.5635 15.9851 15.5933 15.9597C15.6231 15.9342 15.6447 15.8989 15.6549 15.8589Z" fill="#F6821F"></path>
              <path d="M18.0571 10.3428C17.9903 10.3428 17.9237 10.3446 17.8574 10.3483C17.8467 10.3492 17.8362 10.3517 17.8262 10.3559C17.8088 10.3625 17.7931 10.3736 17.7804 10.3883C17.7678 10.4031 17.7585 10.421 17.7535 10.4405L17.47 11.5236C17.3481 11.989 17.3934 12.4188 17.598 12.7349C17.7861 13.0264 18.0996 13.1975 18.4803 13.2174L20.0178 13.3195C20.0394 13.3203 20.0605 13.3266 20.0796 13.3381C20.0985 13.3495 20.1149 13.3656 20.1273 13.3853C20.14 13.4063 20.1482 13.4303 20.151 13.4555C20.1538 13.4806 20.1512 13.5061 20.1435 13.5299C20.1312 13.5689 20.1087 13.6031 20.079 13.6282C20.0493 13.6533 20.0136 13.6683 19.9763 13.6714L18.3789 13.7734C17.5116 13.8176 16.5769 14.5919 16.2499 15.5366L16.1345 15.8702C16.1297 15.8841 16.1279 15.8991 16.1294 15.9139C16.1309 15.9288 16.1354 15.9431 16.1429 15.9555C16.1503 15.968 16.1603 15.9784 16.172 15.9858C16.1838 15.9931 16.197 15.9973 16.2104 15.9979C16.2119 15.9979 16.2132 15.9979 16.2147 15.9979H21.7114C21.7433 15.9982 21.7745 15.987 21.8001 15.9658C21.8257 15.9447 21.8444 15.9149 21.8532 15.8809C21.9506 15.4967 21.9998 15.0995 21.9995 14.7005C21.9989 12.294 20.234 10.3428 18.0571 10.3428Z" fill="#FBAD41"></path>
            </svg>
            <div class="group flex items-center gap-1">
              <span class="text-slate-12 text-sm font-normal">Cloudflare</span>
              <svg class="text-slate-8 transition duration-150 group-hover:text-black dark:group-hover:text-white" fill="none" height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.25 4.75H6.75C5.64543 4.75 4.75 5.64543 4.75 6.75V17.25C4.75 18.3546 5.64543 19.25 6.75 19.25H17.25C18.3546 19.25 19.25 18.3546 19.25 17.25V14.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                <path d="M19.25 9.25V4.75H14.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                <path d="M19 5L11.75 12.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
              </svg>
            </div></div
        ></a>
      </div>
    </div>
  </div>
  <div class="mx-auto max-w-5xl px-6">
    <div class="border-slate-6 relative rounded-lg border p-6">
      <div class="bg-slate-5 absolute left-0 top-[22px] h-8 w-1 rounded-br-md rounded-tr-md"></div>
      <div class="mb-8 flex items-center justify-between"><h3 class="text-slate-12 text-xl font-bold tracking-[-0.16px]">DNS Records</h3></div>
      <span class="to-root bg-gradient-fade from-yellow-4 border-yellow-5 flex gap-2 rounded-lg border bg-clip-padding p-4"
        ><svg class="text-yellow-11 flex-shrink-0" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.996 13.8597C11.6681 13.8597 11.3962 13.5908 11.3962 13.2665V10.1032C11.3962 9.77899 11.6681 9.51011 11.996 9.51011C12.3239 9.51011 12.5958 9.77899 12.5958 10.1032V13.2665C12.5958 13.5908 12.3239 13.8597 11.996 13.8597Z" fill="currentColor"></path>
          <path d="M11.996 16.6275C11.892 16.6275 11.7881 16.6038 11.6921 16.5642C11.5881 16.5247 11.5081 16.4693 11.4282 16.3982C11.2762 16.2479 11.1962 16.0502 11.1962 15.8367C11.1962 15.6311 11.2842 15.4254 11.4282 15.2752C11.7241 14.9826 12.2599 14.9826 12.5639 15.2752C12.7078 15.4254 12.7958 15.6311 12.7958 15.8367C12.7958 16.0502 12.7158 16.2479 12.5639 16.3982C12.4119 16.5484 12.2119 16.6275 11.996 16.6275Z" fill="currentColor"></path>
          <path clip-rule="evenodd" d="M4.08609 18.7153C4.19007 18.8893 4.39001 19 4.59796 19H19.394C19.602 19 19.8019 18.8893 19.9139 18.7153C20.0259 18.5334 20.0259 18.312 19.9299 18.1301L12.5319 4.29063C12.3239 3.90312 11.6761 3.90312 11.4681 4.29063L4.0701 18.1301C3.97412 18.3199 3.97412 18.5413 4.08609 18.7153ZM11.996 5.83274L18.3943 17.8138H5.59769L11.996 5.83274Z" fill="currentColor" fill-rule="evenodd"></path>
        </svg>
        <p class="text-slate-12 text-sm font-normal">The DNS records verification is in progress. It may take a few minutes or hours, depending on domain propagation time. You will receive an email notification once this operation is completed.</p></span
      >
      <h2 class="text-slate-12 mt-8 flex flex-auto gap-3 text-base font-bold">
        <a class="group flex items-center justify-center gap-1" target="_blank" href="https://resend.com/docs/dashboard/domains/introduction#what-are-spf-records"
          >DKIM and SPF<svg class="text-slate-8 transition duration-150 group-hover:text-black dark:group-hover:text-white" fill="none" height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.25 4.75H6.75C5.64543 4.75 4.75 5.64543 4.75 6.75V17.25C4.75 18.3546 5.64543 19.25 6.75 19.25H17.25C18.3546 19.25 19.25 18.3546 19.25 17.25V14.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
            <path d="M19.25 9.25V4.75H14.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
            <path d="M19 5L11.75 12.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg></a
        ><span class="border-slate-4 bg-slate-3 text-slate-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded border bg-transparent px-2 text-xs font-medium">Required</span>
      </h2>
      <table class="mt-4 min-w-full border-separate border-spacing-0 border-none text-left">
        <thead class="bg-slate-3 h-8 rounded-md">
          <tr class="">
            <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[70px]">Type</th>
            <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[270px]">Name</th>
            <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[270px]">Server / Content / Target</th>
            <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[100px]">TTL</th>
            <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[100px]">Priority</th>
            <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r">Status</th>
          </tr>
        </thead>
        <tbody class="">
          <tr class="">
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">MX</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">send.mail</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">feedback-smtp.us-east-1.amazonses.com</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">Auto</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">10</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <span class="flex items-center gap-2"
                ><button data-state="closed"><span class="bg-yellow-3 text-yellow-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded px-2 text-xs font-medium capitalize">pending</span></button></span
              >
            </td>
          </tr>
          <tr class="">
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">TXT</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">send.mail</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">v=spf1 include:amazonses.com ~all</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">Auto</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm"></td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <span class="flex items-center gap-2"
                ><button data-state="closed"><span class="bg-yellow-3 text-yellow-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded px-2 text-xs font-medium capitalize">pending</span></button></span
              >
            </td>
          </tr>
          <tr class="">
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">TXT</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">resend._domainkey.mail</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">p=MIGfMA0GCSqGSI</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">Auto</td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm"></td>
            <td class="border-slate-6 h-10 truncate border-b px-3 py-2 text-sm">
              <span class="flex items-center gap-2"
                ><button data-state="closed"><span class="bg-yellow-3 text-yellow-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded px-2 text-xs font-medium capitalize">pending</span></button></span
              >
            </td>
          </tr>
        </tbody>
      </table>
      <h2 class="text-slate-12 mt-8 flex flex-auto gap-3 text-base font-bold">
        <a class="group flex items-center justify-center gap-1" target="_blank" href="https://resend.com/docs/dashboard/domains/dmarc"
          >DMARC<svg class="text-slate-8 transition duration-150 group-hover:text-black dark:group-hover:text-white" fill="none" height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.25 4.75H6.75C5.64543 4.75 4.75 5.64543 4.75 6.75V17.25C4.75 18.3546 5.64543 19.25 6.75 19.25H17.25C18.3546 19.25 19.25 18.3546 19.25 17.25V14.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
            <path d="M19.25 9.25V4.75H14.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
            <path d="M19 5L11.75 12.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg></a
        ><span class="border-slate-4 bg-slate-3 text-slate-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded border bg-transparent px-2 text-xs font-medium">Optional</span>
      </h2>
      <table class="mt-4 min-w-full border-separate border-spacing-0 border-none text-left">
        <thead class="bg-slate-3 h-8 rounded-md">
          <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[70px]">Type</th>
          <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[270px]">Name</th>
          <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[270px]">Server / Content / Target</th>
          <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r md:w-[100px]">TTL</th>
          <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r"></th>
          <th class="border-slate-6 text-slate-11 h-8 border-b border-t px-3 text-xs font-semibold first:rounded-l-md first:border-l last:rounded-r-md last:border-r"></th>
        </thead>
        <tbody class="">
          <tr class="">
            <td class="border-slate-6 h-10 truncate !border-0 border-b px-3 py-2 text-sm">TXT</td>
            <td class="border-slate-6 h-10 truncate !border-0 border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">_dmarc</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate !border-0 border-b px-3 py-2 text-sm">
              <button class="focus-visible:ring-slate-7 disabled:hover:bg-slate-1 group -ml-2 inline-flex inline-flex h-6 cursor-pointer cursor-pointer select-none items-center items-center justify-center gap-0.5 gap-1 rounded border border-none pl-2 pr-2 text-xs font-semibold transition duration-200 ease-in-out focus:outline-none focus-visible:ring-2 disabled:cursor-not-allowed disabled:opacity-70" data-state="closed">
                <span class="text-slate-12 block max-w-[200px] truncate text-sm font-normal">v=DMARC1; p=none;</span
                ><span class="opacity-0 transition-opacity duration-200 ease-in-out group-hover:opacity-100"
                  ><svg class="text-slate-11" fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 6.75H7.75C6.64543 6.75 5.75 7.64543 5.75 8.75V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75C18.25 7.64543 17.3546 6.75 16.25 6.75H15" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M14 8.25H10C9.44772 8.25 9 7.80228 9 7.25V5.75C9 5.19772 9.44772 4.75 10 4.75H14C14.5523 4.75 15 5.19772 15 5.75V7.25C15 7.80228 14.5523 8.25 14 8.25Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 12.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                    <path d="M9.75 15.25H14.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></svg
                ></span>
              </button>
            </td>
            <td class="border-slate-6 h-10 truncate !border-0 border-b px-3 py-2 text-sm">Auto</td>
            <td class="border-slate-6 h-10 truncate !border-0 border-b px-3 py-2 text-sm"></td>
            <td class="border-slate-6 h-10 truncate !border-0 border-b px-3 py-2 text-sm"></td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="border-slate-6 relative mt-10 rounded-lg border p-6">
      <div class="bg-slate-5 absolute left-0 top-[22px] h-8 w-1 rounded-br-md rounded-tr-md"></div>
      <div class="mb-8 flex items-center justify-between"><h3 class="text-slate-12 text-xl font-bold tracking-[-0.16px]">Configuration</h3></div>
      <div class="flex w-full flex-col gap-8">
        <div>
          <h2 class="text-slate-12 mb-2 flex flex-auto text-base font-bold">Click Tracking</h2>
          <span class="text-slate-11 text-sm font-normal">To track clicks, Resend modifies each link in the body of the HTML email. When recipients open a link, they are sent to a Resend server, and are immediately redirected to the URL destination.</span>
          <div class="mt-3">
            <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="off" class="border-slate-6 bg-slate-5 focus-visible:ring-slate-7 data-[state=checked]:border-green-6 data-[state=checked]:bg-green-7 dark:data-[state=checked]:bg-green-10 relative h-6 w-10 cursor-default rounded-full border outline-none focus-visible:ring-4" id="click-tracking"><span data-state="unchecked" class="bg-slate-11 data-[state=checked]:bg-slate-12 block h-5 w-5 translate-x-0.5 rounded-full transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-4"></span></button>
          </div>
        </div>
        <div>
          <h2 class="text-slate-12 mb-2 flex flex-auto gap-3 text-base font-bold">Open Tracking<span class="bg-slate-3 text-slate-11 inline-flex h-6 select-none items-center whitespace-nowrap rounded px-2 text-xs font-medium">Not Recommended</span></h2>
          <span class="text-slate-11 text-sm font-normal">A 1x1 pixel transparent GIF image is inserted in each email and includes a unique reference. Open tracking can produce inaccurate results and decrease deliverability. <a class="text-blue-11 hover:underline" href="https://resend.com/docs/knowledge-base/why-are-my-open-rates-not-accurate">Learn more and consider if open tracking is right for you</a>.</span>
          <div class="mt-3">
            <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="off" class="border-slate-6 bg-slate-5 focus-visible:ring-slate-7 data-[state=checked]:border-green-6 data-[state=checked]:bg-green-7 dark:data-[state=checked]:bg-green-10 relative h-6 w-10 cursor-default rounded-full border outline-none focus-visible:ring-4" id="open-tracking"><span data-state="unchecked" class="bg-slate-11 data-[state=checked]:bg-slate-12 block h-5 w-5 translate-x-0.5 rounded-full transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-4"></span></button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



{{-- Table Two --}}
<section class="text-muted-foreground  w-9/12 m-auto">
    <div class="mx-auto">
        <div class="border relative overflow-hidden bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">

            <div class="bg-white dark:bg-gray-800 relative shadow-md sm:rounded-lg overflow-hidden">


                {{-- Table Header Start --}}
                <div class="header-table">

                    {{-- Head Bar --}}
                    <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">

                        {{-- Search --}}
                        <div class="w-full md:w-1/2">
                            <form class="flex items-center">
                                <label for="simple-search" class="sr-only">Search</label>
                                <div class="relative w-full">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <input type="text" id="simple-search" class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search" required="">
                                </div>
                            </form>
                        </div>


                        <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">

                            {{-- primary button --}}
                          {{--   <button type="button" class="flex items-center justify-center text-white bg-primary hover:bg-primary focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary dark:hover:bg-primary focus:outline-none dark:focus:ring-primary">
                                <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                                </svg>
                                Add product
                            </button> --}}

                            <div class="flex items-center space-x-3 w-full md:w-auto">

                                <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-600 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" type="button">
                                    <svg class="-ml-1 mr-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                        <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                    </svg>
                                    Actions
                                </button>


                                {{-- Actions Dropdown --}}
                                <div id="actionsDropdown" class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                    <ul class="py-1 text-sm text-gray-600 dark:text-gray-200" aria-labelledby="actionsDropdownButton">
                                        <li>
                                            <a href="#" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Mass Edit</a>
                                        </li>
                                    </ul>
                                    <div class="py-1">
                                        <a href="#" class="block py-2 px-4 text-sm text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Delete all</a>
                                    </div>
                                </div>


                                {{-- Filters --}}
                                <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-600 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" type="button">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                                    </svg>
                                    Filter
                                    <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                        <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                                    </svg>
                                </button>

                                {{-- Filters DropDown --}}
                                <div id="filterDropdown" class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                                    <h6 class="mb-3 text-sm font-medium text-gray-600 dark:text-white">Choose brand</h6>
                                    <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                                        <li class="flex items-center">
                                            <input id="apple" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="apple" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">Dofollow Only</label>
                                        </li>
                                        <li class="flex items-center">
                                            <input id="fitbit" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                            <label for="fitbit" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">Nofollow</label>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Secondary Bar --}}
                    <div class="flex flex-row justify-between px-4 py-3 space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0 lg:space-x-4">

                          <div class="flex ">
                              <h5 class="mr-4">
                                  <span class="text-gray-500">All Backlinks:</span>
                                  <span class="dark:text-white">44,443</span>
                              </h5>
                              <h5>
                                  <span class="text-gray-500">Total Estimated Worth:</span>
                                  <span class="dark:text-white">$88.4k</span>
                              </h5>
                          </div>

                          <div class="flex flex-col flex-shrink-0 space-y-3 md:flex-row md:items-center lg:justify-end md:space-y-0 md:space-x-3">


                              {{-- <button type="button" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                                  <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                      <path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                                  </svg>
                                  Add new product
                              </button> --}}


                              <button type="button" class="flex items-center justify-center flex-shrink-0 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                                  <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" fill="none" viewbox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                  </svg>
                                  Update Links 1/25
                              </button>
                              <button type="button" class="flex items-center justify-center flex-shrink-0 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                                  <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                                  </svg>
                                  Export
                              </button>
                          </div>
                    </div>

                </div>
                {{-- Table Header End --}}




          <div class="overflow-x-auto">
              <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                  <thead class="text-xs text-gray-600 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                      <tr>
                         
                          <th scope="col" class="px-4 py-3">Website</th>
                          <th scope="col" class="px-4 py-3">Category</th>
                          <th scope="col" class="px-4 py-3">Quality</th>
                          <th scope="col" class="px-4 py-3">Linkjuice Score</th>
                          <th scope="col" class="px-4 py-3">Trust Score</th>
                          <th scope="col" class="px-4 py-3">Rating</th>
                          <th scope="col" class="px-4 py-3">Spam Score</th>
                          <th scope="col" class="px-4 py-3">Site Worth</th>
                          <th scope="col" class="px-4 py-3">Last Update</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=wikiwalls.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              wikiwalls.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Desktop PC</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-red-700 rounded-full"></div>
                                  95
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.47</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.47</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  1.6/10
                              </div>
                          </td>
                          <td class="px-4 py-2">$300</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">Just now</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=apple.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              Apple.com&#34;
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Desktop PC</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-red-700 rounded-full"></div>
                                  108
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.15</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.32</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                 
                                  6M
                              </div>
                          </td>
                          <td class="px-4 py-2">$785K</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=youtube.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              Youtube.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Phone</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-green-400 rounded-full"></div>
                                  24
                              </div>

                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.00</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.95</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">4.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  1.2M
                              </div>
                          </td>
                          <td class="px-4 py-2">$3.2M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">Just now</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=beebom.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              beeboom.com
                            </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Tablet</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-red-500 rounded-full"></div>
                                  287
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.47</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.00</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">4.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  298K
                              </div>
                          </td>
                          <td class="px-4 py-2">$425K</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">Just now</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=www.androidauthority.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              androidauthorithy.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Console</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-yellow-300 rounded-full"></div>
                                  450
                              </div>

                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.61</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.30</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  99
                              </div>
                          </td>
                          <td class="px-4 py-2">$345K</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=Propakistani.pk" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              Propakistani.pk
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Console</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-green-400 rounded-full"></div>
                                  2435
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.41</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.11</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">4.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  2.1M
                              </div>
                          </td>
                          <td class="px-4 py-2">$4.2M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=androidpolice.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              androidpolice.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Gaming/Console</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-orange-500 rounded-full"></div>
                                  235
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">7.09</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">3.32</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  989K
                              </div>
                          </td>
                          <td class="px-4 py-2">$2.27M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=watches.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              watches.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Watch</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-yellow-300 rounded-full"></div>
                                  433
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">4.96</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.74</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  102
                              </div>
                          </td>
                          <td class="px-4 py-2">$45K</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">2 weeks ago</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=nikon.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              nikon.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Photo/Video</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-orange-400 rounded-full"></div>
                                  351
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.20</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.74</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">3.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  1.2M
                              </div>
                          </td>
                          <td class="px-4 py-2">$1.52M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">3 weeks ago</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=samsung.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              samsung.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">TV/Monitor</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-green-500 rounded-full"></div>
                                  1242
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">4.12</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.30</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">4.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  211K
                              </div>
                          </td>
                          <td class="px-4 py-2">$1.2M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">Just now</td>
                      </tr>
                        <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=www.androidauthority.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              androidauthorithy.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Console</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-yellow-300 rounded-full"></div>
                                  450
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.61</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.30</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  99
                              </div>
                          </td>
                          <td class="px-4 py-2">$345K</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=Propakistani.pk" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              Propakistani.pk
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Console</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-green-400 rounded-full"></div>
                                  2435
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">1.41</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.11</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-gray-300 dark:text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">4.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  2.1M
                              </div>
                          </td>
                          <td class="px-4 py-2">$4.2M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=androidpolice.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              androidpolice.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Gaming/Console</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-orange-500 rounded-full"></div>
                                  235
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">7.09</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">3.32</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  989K
                              </div>
                          </td>
                          <td class="px-4 py-2">$2.27M</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">This week</td>
                      </tr>
                      <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                          
                          <th scope="row" class="flex items-center px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <img src="https://www.google.com/s2/favicons?sz=64&domain_url=watches.com" alt="iMac Front Image" class="w-auto h-5 rounded-full mr-3">
                              watches.com
                          </th>
                          <td class="px-4 py-2">
                              <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-primary-900 dark:text-primary-300">Watch</span>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <div class="inline-block w-4 h-4 mr-2 bg-yellow-300 rounded-full"></div>
                                  433
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">4.96</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">0.74</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <svg aria-hidden="true" class="w-5 h-5 text-yellow-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                  </svg>
                                  <span class="ml-1 text-gray-500 dark:text-gray-400">5.0</span>
                              </div>
                          </td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">
                              <div class="flex items-center">
                                  
                                  102
                              </div>
                          </td>
                          <td class="px-4 py-2">$45K</td>
                          <td class="px-4 py-2 font-medium text-gray-600 whitespace-nowrap dark:text-white">2 weeks ago</td>
                      </tr>
                     
                     
                  </tbody>
              </table>
          </div>
          <nav class="flex flex-col items-start justify-between p-4 space-y-3 md:flex-row md:items-center md:space-y-0" aria-label="Table navigation">
              <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                  Showing
                  <span class="font-semibold text-gray-600 dark:text-white">1-10</span>
                  of
                  <span class="font-semibold text-gray-600 dark:text-white">1000</span>
              </span>
              <ul class="inline-flex items-stretch -space-x-px">
                  <li>
                      <a href="#" class="flex items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                          <span class="sr-only">Previous</span>
                          <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                      </a>
                  </li>
                  <li>
                      <a href="#" class="flex items-center justify-center px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">1</a>
                  </li>
                  <li>
                      <a href="#" class="flex items-center justify-center px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">2</a>
                  </li>
                  <li>
                      <a href="#" aria-current="page" class="z-10 flex items-center justify-center px-3 py-2 text-sm leading-tight border text-primary-600 bg-primary-50 border-primary-300 hover:bg-primary-100 hover:text-primary-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">3</a>
                  </li>
                  <li>
                      <a href="#" class="flex items-center justify-center px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">...</a>
                  </li>
                  <li>
                      <a href="#" class="flex items-center justify-center px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">100</a>
                  </li>
                  <li>
                      <a href="#" class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                          <span class="sr-only">Next</span>
                          <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                          </svg>
                      </a>
                  </li>
              </ul>
          </nav>
      </div>
  </div>
</section>
    




<!-- Table Section Preline -->
<div class="w-full mb-4  w-9/12 m-auto">
  <!-- Card -->
  <div class="flex flex-col  w-9/12 m-auto my-12">
    <div class="-m-1.5 overflow-x-auto">
      <div class="p-1.5 min-w-full inline-block align-middle">
        <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden dark:bg-slate-900 dark:border-gray-700">


            <!-- Header -->
            
            
            <div class="flex flex-col mb-2 md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">

                {{-- Search --}}
                <div class="w-full md:w-1/2">
                    <form class="flex items-center">
                        <label for="simple-search" class="sr-only">Search</label>
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input type="text" id="simple-search" class="bg-gray-50 border border-gray-300 text-gray-600 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search" required="">
                        </div>
                    </form>
                </div>


                <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">

                    <button type="button" class="flex items-center justify-center flex-shrink-0 px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-lg focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                      <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" fill="none" viewbox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                      </svg>
                      Update Links 1/25
                        </button>

                      <button id="hs-as-table-table-export-dropdown" type="button" class="py-2 px-3 inline-flex justify-center items-center gap-2 rounded-md border font-medium bg-white text-gray-700 shadow-sm align-middle hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-600 transition-all text-sm dark:bg-slate-900 dark:hover:bg-slate-800 dark:border-gray-700 dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-800">
                      <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                      </svg>
                      Export
                    </button>

                  <div class="flex items-center space-x-3 w-full md:w-auto">

                      <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-600 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" type="button">
                          <svg class="-ml-1 mr-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                              <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                          </svg>
                          Actions
                      </button>

                      <div id="actionsDropdown" class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                          <ul class="py-1 text-sm text-gray-600 dark:text-gray-200" aria-labelledby="actionsDropdownButton">
                              <li>
                                  <a href="#" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Mass Edit</a>
                              </li>
                          </ul>
                          <div class="py-1">
                              <a href="#" class="block py-2 px-4 text-sm text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Delete all</a>
                          </div>
                      </div>

                      <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-600 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" type="button">
                          <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                          </svg>
                          Filter
                          <span class="mx-2 inline-flex items-center gap-1.5 py-0.5 px-1.5 rounded-full text-xs font-medium border border-gray-300 text-gray-800 dark:border-gray-700 dark:text-gray-300">
                                  2
                                </span>
                          <svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                              <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                          </svg>
                      </button>


                      {{-- Hidden-Data --}}
                      <div id="filterDropdown" class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                          <h6 class="mb-3 text-sm font-medium text-gray-600 dark:text-white">Choose brand</h6>
                          <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                              <li class="flex items-center">
                                  <input id="apple" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                  <label for="apple" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">Apple (56)</label>
                              </li>
                              <li class="flex items-center">
                                  <input id="fitbit" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                  <label for="fitbit" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">Microsoft (16)</label>
                              </li>
                              <li class="flex items-center">
                                  <input id="razor" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                  <label for="razor" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">Razor (49)</label>
                              </li>
                              <li class="flex items-center">
                                  <input id="nikon" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                  <label for="nikon" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">Nikon (12)</label>
                              </li>
                              <li class="flex items-center">
                                  <input id="benq" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                  <label for="benq" class="ml-2 text-sm font-medium text-gray-600 dark:text-gray-100">BenQ (74)</label>
                              </li>
                          </ul>
                      </div>
                  </div>
                </div>
            </div>
           
          <!-- End Header -->



          <!-- Table -->
          <table class="min-w-full divide-y  divide-gray-200 dark:divide-gray-700">
            <thead class=" bg-gray-50 dark:bg-slate-900">

                {{-- Heading --}}
              <tr class="py-4">
                <th scope="col" class="pl-6 py-3 text-left">
                  <label for="hs-at-with-checkboxes-main" class="flex">
                    <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-main">
                    <span class="sr-only">Checkbox</span>
                  </label>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Website
                    </span>
                     <div class="flex justify-center items-center w-5 h-5 border border-gray-200 group-hover:bg-gray-200 text-gray-400 rounded dark:border-gray-700 dark:group-hover:bg-gray-700 dark:text-gray-400">
                      <svg class="w-2.5 h-2.5" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7.55921 0.287451C7.86808 -0.0958171 8.40096 -0.0958167 8.70982 0.287451L12.9295 5.52367C13.3857 6.08979 13.031 7 12.3542 7H3.91488C3.23806 7 2.88336 6.08979 3.33957 5.52367L7.55921 0.287451Z" fill="currentColor"/>
                        <path d="M8.70983 15.7125C8.40096 16.0958 7.86808 16.0958 7.55921 15.7125L3.33957 10.4763C2.88336 9.9102 3.23806 9 3.91488 9H12.3542C13.031 9 13.3857 9.9102 12.9295 10.4763L8.70983 15.7125Z" fill="currentColor"/>
                      </svg>
                    </div>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Link Indexed
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Organic Traffic
                    </span>
                  </div>
                </th>


                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Anchor
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Rating
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Value
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Active
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      DR
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Relation
                    </span>
                  </div>
                </th>

                 <th scope="col" class="px-6 py-3 text-left">
                  <div class="flex items-center gap-x-2">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                      Action
                    </span>
                  </div>
                </th>

              </tr>
            </thead>


            {{-- Table Body --}}
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">

              <tr class="py-4">

                <td class="h-px w-px whitespace-nowrap">
                  <div class="pl-6 py-2">
                    <label for="hs-at-with-checkboxes-1" class="flex">
                      <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-1" checked>
                      <span class="sr-only">Checkbox</span>
                    </label>
                  </div>
                </td>

                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="flex items-center gap-x-2">
                      <svg class="inline-block h-5 w-5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.0355 1.75926C10.6408 1.75926 5.30597 1.49951 0.0111241 1C-0.288584 7.23393 5.50578 13.1282 12.7987 14.5668L13.9975 14.7266C14.3372 12.4289 15.9956 3.7773 16.595 1.73928C16.4152 1.75926 16.2353 1.75926 16.0355 1.75926Z" fill="#A49DFF"/>
                        <path d="M16.615 1.75926C16.615 1.75926 25.2266 7.9932 28.5234 16.3451C30.0419 11.3499 31.1608 6.15498 32 1C26.8051 1.49951 21.71 1.75926 16.615 1.75926Z" fill="#28289A"/>
                        <path d="M13.9975 14.7466L13.8177 15.9455C13.8177 15.9455 12.2592 28.4133 23.1886 31.9699C25.2266 26.8748 27.0049 21.6599 28.5234 16.3251C21.9698 15.8456 13.9975 14.7466 13.9975 14.7466Z" fill="#5ADCEE"/>
                        <path d="M16.6149 1.75927C16.0155 3.79729 14.3571 12.4089 14.0175 14.7466C14.0175 14.7466 21.9897 15.8456 28.5233 16.3251C25.1866 7.9932 16.6149 1.75927 16.6149 1.75927Z" fill="#7878FF"/>
                      </svg>
                      <div class="grow ml-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Guideline.com</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">No</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">45m</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2 flex gap-x-1">
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-300 dark:text-gray-700" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-3">
                    <div class="flex items-center gap-x-3">
                      <span class="text-xs text-gray-500">3/5</span>
                      <div class="flex w-full h-1.5 bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700">
                        <div class="flex flex-col justify-center overflow-hidden bg-gray-800 dark:bg-gray-200" role="progressbar" style="width: 78%" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                    </div>
                  </div>
                </td>
                 <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                      </svg>
                      Active
                    </span>
                  </div>
                </td>
                <td class="p-4 align-middle pr-0">
                                    <div class="inline-flex items-center justify-center rounded-full font-medium cursor-default bg-gray-100 px-2.5 py-0.5 text-gray-800 text-xs">94</div>
                                </td>

                                <td class="p-4 align-middle pr-0">
                                    <div class="inline-flex items-center border rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground">NoFollow</div>
                                </td>

                                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="hs-dropdown relative inline-block [--placement:bottom-right]">
                      <button id="hs-table-dropdown-1" type="button" class="hs-dropdown-toggle py-1.5 px-2 inline-flex justify-center items-center gap-2 rounded-md text-gray-700 align-middle focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-600 transition-all text-sm dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-800">
                        <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
                        </svg>
                      </button>
                      <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden mt-2 divide-y divide-gray-200 min-w-[10rem] z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-gray-700 dark:bg-gray-800 dark:border dark:border-gray-700" aria-labelledby="hs-table-dropdown-1">
                        <div class="py-2 first:pt-0 last:pb-0">
                          <span class="block py-2 px-3 text-xs font-medium uppercase text-gray-400 dark:text-gray-600">
                            Actions
                          </span>
                          <a class="flex items-center gap-x-3 py-2 px-3 rounded-md text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">
                            Rename team
                          </a>
                          <a class="flex items-center gap-x-3 py-2 px-3 rounded-md text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">
                            Add to favorites
                          </a>
                          <a class="flex items-center gap-x-3 py-2 px-3 rounded-md text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">
                            Archive team
                          </a>
                        </div>
                        <div class="py-2 first:pt-0 last:pb-0">
                          <a class="flex items-center gap-x-3 py-2 px-3 rounded-md text-sm text-red-600 hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 dark:text-red-500 dark:hover:bg-gray-700 dark:hover:text-gray-300" href="#">
                            Delete
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

              </tr>



              <tr class="py-4">
                <td class="h-px w-px whitespace-nowrap">
                  <div class="pl-6 py-2">
                    <label for="hs-at-with-checkboxes-2" class="flex">
                      <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-2" checked>
                      <span class="sr-only">Checkbox</span>
                    </label>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="flex items-center gap-x-2">
                      <svg class="inline-block h-5 w-5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_4132_5800)">
                        <path d="M16 32.0022C24.8366 32.0022 32 24.8388 32 16.0022C32 7.16569 24.8366 0.00225067 16 0.00225067C7.16344 0.00225067 0 7.16569 0 16.0022C0 24.8388 7.16344 32.0022 16 32.0022Z" fill="#1977F3"/>
                        <path d="M22.2281 20.6283L22.9369 16.0023H18.4998V13.0007C18.4998 11.7362 19.1185 10.5009 21.1076 10.5009H23.1259V6.56335C23.1259 6.56335 21.2943 6.2506 19.5438 6.2506C15.8897 6.2506 13.5002 8.46463 13.5002 12.4764V16.0023H9.43665V20.6283H13.5002V31.8087C14.3147 31.937 15.1495 32.0023 16 32.0023C16.8505 32.0023 17.6853 31.9347 18.4998 31.8087V20.6283H22.2281Z" fill="white"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_4132_5800">
                        <rect width="32" height="32" fill="white"/>
                        </clipPath>
                        </defs>
                      </svg>
                      <div class="grow ml-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Facebook.com</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Yes</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">5m</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2 flex gap-x-1">
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  </div>
                </td>
                 <td class="h-px w-px whitespace-nowrap">
                  <a class="block relative z-10" href="#">
                    <div class="px-6 py-2">
                      <div class="block text-sm text-gray-600 decoration-2 hover:underline dark:text-gray-500">#digitalmarketing</div>
                    </div>
                  </a>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <a class="block relative z-10" href="#">
                    <div class="px-6 py-2">
                      <span class="inline-flex items-center gap-1.5 py-1 px-2 rounded-md text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                        Marketing team
                      </span>
                    </div>
                  </a>
                </td>
              </tr>

              <tr class="py-4">
                <td class="h-px w-px whitespace-nowrap">
                  <div class="pl-6 py-2">
                    <label for="hs-at-with-checkboxes-3" class="flex">
                      <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-3">
                      <span class="sr-only">Checkbox</span>
                    </label>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="flex items-center gap-x-2">
                      <svg class="inline-block h-5 w-5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_6173_178913)">
                        <path d="M16 32C7.16 32 0 24.84 0 16C0 7.16 7.16 0 16 0C24.84 0 32 7.16 32 16C32 24.84 24.84 32 16 32Z" fill="#FFE01B"/>
                        <path d="M11.72 19.28C11.74 19.3 11.74 19.34 11.72 19.38C11.64 19.52 11.48 19.6 11.32 19.58C11.02 19.54 10.8 19.3 10.82 19C10.82 18.8 10.86 18.62 10.92 18.42C11.02 18.18 10.92 17.92 10.72 17.78C10.6 17.7 10.44 17.68 10.3 17.7C10.16 17.72 10.04 17.82 9.96001 17.94C9.90001 18.04 9.86001 18.14 9.84001 18.24C9.84001 18.26 9.82001 18.28 9.82001 18.28C9.78001 18.4 9.70001 18.44 9.64001 18.44C9.62001 18.44 9.58001 18.42 9.56001 18.36C9.50001 18.02 9.62001 17.68 9.84001 17.42C10.04 17.2 10.32 17.1 10.62 17.14C10.92 17.18 11.2 17.38 11.32 17.66C11.46 18 11.42 18.38 11.24 18.7C11.22 18.72 11.22 18.76 11.2 18.78C11.14 18.9 11.12 19.06 11.2 19.18C11.26 19.26 11.34 19.3 11.44 19.3C11.48 19.3 11.52 19.3 11.56 19.28C11.64 19.24 11.7 19.24 11.72 19.28ZM24.94 19.6C24.92 20.22 24.78 20.82 24.56 21.4C23.44 24.1 20.76 25.6 17.56 25.5C14.58 25.42 12.04 23.84 10.94 21.26C10.24 21.24 9.56001 20.96 9.06001 20.5C8.52001 20.04 8.18001 19.4 8.10001 18.7C8.04001 18.22 8.10001 17.74 8.28001 17.28L7.66001 16.76C4.78001 14.36 13.72 4.4 16.56 6.9C16.58 6.92 17.54 7.86 17.54 7.86C17.54 7.86 18.06 7.64 18.08 7.64C20.58 6.6 22.62 7.1 22.62 8.76C22.62 9.62 22.08 10.62 21.2 11.54C21.56 11.9 21.8 12.34 21.92 12.82C22.02 13.16 22.06 13.5 22.08 13.86C22.1 14.22 22.12 15.04 22.12 15.04C22.14 15.04 22.4 15.12 22.48 15.14C23 15.26 23.46 15.48 23.86 15.82C24.08 16.02 24.2 16.3 24.26 16.58C24.32 16.96 24.22 17.34 24 17.64C24.06 17.78 24.1 17.9 24.16 18.04C24.24 18.28 24.28 18.48 24.3 18.5C24.52 18.54 24.94 18.86 24.94 19.6ZM12.34 18.12C12.14 16.86 11.3 16.42 10.72 16.38C10.58 16.38 10.44 16.38 10.28 16.42C9.26001 16.62 8.66001 17.5 8.78001 18.64C8.96001 19.7 9.82001 20.5 10.88 20.56C10.98 20.56 11.08 20.56 11.18 20.54C12.24 20.38 12.5 19.24 12.34 18.12ZM14.1 10.12C14.98 9.4 15.9 8.76 16.88 8.2C16.88 8.2 16.1 7.3 15.86 7.22C14.42 6.82 11.3 8.98 9.30001 11.84C8.50001 13 7.34001 15.04 7.90001 16.08C8.10001 16.32 8.32001 16.52 8.56001 16.72C8.92001 16.2 9.48001 15.84 10.12 15.72C10.9 13.54 12.28 11.6 14.1 10.12ZM17.22 20.1C17.3 20.44 17.56 20.72 17.9 20.8C18.08 20.86 18.24 20.92 18.44 20.94C20.72 21.34 22.86 20.02 23.34 19.7C23.38 19.68 23.4 19.7 23.38 19.74C23.36 19.76 23.34 19.78 23.34 19.8C22.76 20.56 21.18 21.44 19.12 21.44C18.22 21.44 17.32 21.12 17 20.64C16.48 19.88 16.98 18.78 17.82 18.9C17.82 18.9 18.12 18.94 18.2 18.94C19.52 19.06 20.86 18.86 22.08 18.32C23.24 17.78 23.68 17.18 23.62 16.7C23.6 16.56 23.52 16.42 23.42 16.3C23.1 16.04 22.72 15.86 22.32 15.78C22.14 15.72 22.02 15.7 21.88 15.66C21.64 15.58 21.52 15.52 21.5 15.06C21.48 14.86 21.46 14.18 21.44 13.88C21.42 13.38 21.36 12.7 20.94 12.42C20.84 12.34 20.7 12.3 20.58 12.3C20.5 12.3 20.44 12.3 20.36 12.32C20.14 12.36 19.96 12.48 19.8 12.64C19.4 13 18.88 13.18 18.34 13.14C18.04 13.12 17.74 13.08 17.38 13.06C17.32 13.06 17.24 13.06 17.18 13.04C16.22 13.06 15.44 13.78 15.32 14.74C15.12 16.16 16.14 16.88 16.44 17.32C16.48 17.38 16.52 17.44 16.52 17.52C16.52 17.6 16.48 17.68 16.42 17.72C15.6 18.64 15.3 19.92 15.62 21.12C15.66 21.26 15.7 21.4 15.76 21.54C16.5 23.28 18.82 24.1 21.08 23.36C21.38 23.26 21.66 23.14 21.94 23C22.44 22.76 22.88 22.42 23.26 22.02C23.84 21.44 24.22 20.68 24.36 19.86C24.42 19.4 24.32 19.24 24.2 19.16C24.1 19.1 24 19.08 23.88 19.1C23.82 18.74 23.72 18.4 23.58 18.08C22.94 18.56 22.2 18.94 21.42 19.16C20.48 19.42 19.52 19.52 18.54 19.48C17.92 19.42 17.5 19.24 17.34 19.76C18.28 20.08 19.28 20.18 20.28 20.06C20.3 20.06 20.34 20.08 20.34 20.1C20.34 20.12 20.32 20.14 20.3 20.16C20.22 20.14 19.06 20.68 17.22 20.1ZM13.84 11.88C14.6 11.34 15.48 10.96 16.38 10.76C17.42 10.52 18.48 10.52 19.52 10.76C19.56 10.76 19.58 10.7 19.54 10.68C19 10.4 18.42 10.24 17.8 10.22C17.78 10.22 17.76 10.2 17.76 10.18V10.16C17.86 10.04 17.96 9.92 18.08 9.84C18.1 9.82 18.1 9.8 18.08 9.8L18.06 9.78C17.32 9.86 16.62 10.1 15.98 10.52C15.96 10.52 15.94 10.52 15.94 10.52V10.5C15.98 10.32 16.06 10.14 16.16 9.96C16.16 9.94 16.16 9.92 16.14 9.92H16.12C15.22 10.42 14.42 11.08 13.76 11.86C13.74 11.88 13.74 11.9 13.76 11.9C13.8 11.9 13.82 11.9 13.84 11.88ZM19.84 16.7C19.96 16.78 20.14 16.76 20.24 16.64C20.3 16.52 20.22 16.38 20.06 16.3C19.94 16.22 19.76 16.24 19.66 16.36C19.6 16.46 19.68 16.62 19.84 16.7ZM20.34 14.88C20.38 15.08 20.46 15.28 20.58 15.44C20.7 15.42 20.84 15.42 20.96 15.44C21.04 15.22 21.04 14.98 20.98 14.76C20.88 14.34 20.76 14.1 20.52 14.14C20.26 14.18 20.24 14.48 20.34 14.88ZM20.88 15.84C20.72 15.8 20.54 15.88 20.48 16.06C20.44 16.22 20.52 16.4 20.7 16.46C20.88 16.52 21.04 16.42 21.1 16.24C21.1 16.22 21.12 16.18 21.12 16.16C21.12 16 21.02 15.86 20.88 15.84Z" fill="black"/>
                        <path d="M16.66 15.8C16.62 15.8 16.6 15.78 16.6 15.76C16.58 15.68 16.7 15.58 16.8 15.48C17.14 15.22 17.6 15.18 17.98 15.34C18.16 15.42 18.32 15.54 18.42 15.7C18.46 15.76 18.46 15.82 18.44 15.84C18.4 15.88 18.3 15.84 18.12 15.76C17.92 15.66 17.68 15.6 17.46 15.62C17.2 15.66 16.92 15.72 16.66 15.8ZM18.38 16.16C18.22 16 18 15.92 17.8 15.96C17.64 15.98 17.5 16.04 17.38 16.14C17.32 16.18 17.28 16.24 17.28 16.32C17.28 16.34 17.28 16.36 17.3 16.36C17.32 16.36 17.32 16.38 17.34 16.38C17.4 16.38 17.46 16.36 17.5 16.34C17.74 16.26 17.98 16.22 18.22 16.26C18.34 16.28 18.38 16.28 18.42 16.24C18.4 16.2 18.4 16.18 18.38 16.16Z" fill="black"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_6173_178913">
                        <rect width="32" height="32" fill="white"/>
                        </clipPath>
                        </defs>
                      </svg>
                      <div class="grow ml-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Mailchimp.com</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Yes</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">3m</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2 flex gap-x-1">
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  </div>
                </td>

                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                      <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                      </svg>
                      Warning
                    </span>
                  </div>
                </td>
              </tr>

              <tr class="py-4">
                <td class="h-px w-px whitespace-nowrap">
                  <div class="pl-6 py-2">
                    <label for="hs-at-with-checkboxes-4" class="flex">
                      <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-4">
                      <span class="sr-only">Checkbox</span>
                    </label>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="flex items-center gap-x-2">
                      <svg class="inline-block h-5 w-5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M27.2192 10.9088C27.0336 11.0528 23.7568 12.8992 23.7568 17.0048C23.7568 21.7536 27.9264 23.4336 28.0512 23.4752C28.032 23.5776 27.3888 25.776 25.8528 28.016C24.4832 29.9872 23.0528 31.9552 20.8768 31.9552C18.7008 31.9552 18.1408 30.6912 15.6288 30.6912C13.1808 30.6912 12.3104 31.9968 10.32 31.9968C8.3296 31.9968 6.9408 30.1728 5.344 27.9328C3.4944 25.3024 2 21.216 2 17.3376C2 11.1168 6.0448 7.8176 10.0256 7.8176C12.1408 7.8176 13.904 9.2064 15.232 9.2064C16.496 9.2064 18.4672 7.7344 20.8736 7.7344C21.7856 7.7344 25.0624 7.8176 27.2192 10.9088ZM19.7312 5.1008C20.7264 3.92 21.4304 2.2816 21.4304 0.6432C21.4304 0.416 21.4112 0.1856 21.3696 0C19.7504 0.0608 17.824 1.0784 16.6624 2.4256C15.7504 3.4624 14.8992 5.1008 14.8992 6.7616C14.8992 7.0112 14.9408 7.2608 14.96 7.3408C15.0624 7.36 15.2288 7.3824 15.3952 7.3824C16.848 7.3824 18.6752 6.4096 19.7312 5.1008Z" fill="black"/>
                      </svg>
                      <div class="grow ml-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Apple.com</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Yes</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">500k</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2 flex gap-x-1">
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-300 dark:text-gray-700" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-300 dark:text-gray-700" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  </div>
                </td>
              </tr>

              <tr class="py-4">
                <td class="h-px w-px whitespace-nowrap">
                  <div class="pl-6 py-2">
                    <label for="hs-at-with-checkboxes-6" class="flex">
                      <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-6">
                      <span class="sr-only">Checkbox</span>
                    </label>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="flex items-center gap-x-2">
                      <svg class="inline-block h-5 w-5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 2L0 7.27424L8 12.471L15.9999 7.27424L8 2ZM23.9999 2L15.9999 7.27424L23.9999 12.471L31.9998 7.27424L23.9999 2ZM0 17.7451L8 23.0194L15.9999 17.7451L8 12.471L0 17.7451ZM23.9999 12.471L15.9999 17.7451L23.9999 23.0194L31.9998 17.7451L23.9999 12.471ZM8 24.7258L15.9999 30L23.9999 24.7258L15.9999 19.5291L8 24.7258Z" fill="#0062FF"/>
                      </svg>
                      <div class="grow ml-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Dropbox.com</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">1900</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">2m</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2 flex gap-x-1">
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-300 dark:text-gray-700" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                 {{--  <div class="px-6 py-3">
                    <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                      <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                      </svg>
                      Danger
                    </span>
                  </div> --}}
                   <div class="px-6 py-2">
                    <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-green-200">
                      <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"></path>
                      </svg>
                      Declined
                    </span>
                  </div>
                </td>
              </tr>

              <tr class="py-4">
                <td class="h-px w-px whitespace-nowrap">
                  <div class="pl-6 py-2">
                    <label for="hs-at-with-checkboxes-7" class="flex">
                      <input type="checkbox" class="shrink-0 border-gray-200 rounded text-gray-600 pointer-events-none focus:ring-gray-500 dark:bg-gray-800 dark:border-gray-700 dark:checked:bg-gray-500 dark:checked:border-gray-500 dark:focus:ring-offset-gray-800" id="hs-at-with-checkboxes-7">
                      <span class="sr-only">Checkbox</span>
                    </label>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <div class="flex items-center gap-x-2">
                      <svg class="inline-block h-5 w-5" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M28.6156 25.8849C28.3919 27.5493 27.2718 28.9884 25.7044 29.6283C24.9363 29.9475 24.1041 30.0444 23.2719 29.9475C22.4725 29.852 21.6716 29.5955 20.8409 29.1167C19.6895 28.4754 18.5367 27.4851 17.193 26.0131C19.3047 23.421 20.5844 21.0542 21.0647 18.9424C21.2884 17.9506 21.3212 17.0542 21.2242 16.222C21.0959 15.4227 20.8081 14.6858 20.3607 14.0461C19.3689 12.6069 17.7045 11.7747 15.8492 11.7747C13.9938 11.7747 12.3295 12.6381 11.3377 14.0461C10.8902 14.6858 10.6024 15.4227 10.4741 16.222C10.3459 17.0542 10.3787 17.9819 10.6337 18.9424C11.114 21.0542 12.4249 23.4538 14.5054 26.0444C13.193 27.5164 12.0103 28.5082 10.8574 29.1481C10.0253 29.6283 9.22585 29.8848 8.42644 29.9802C7.60169 30.0727 6.768 29.9623 5.99545 29.6611C4.42798 29.0213 3.30794 27.5806 3.08423 25.9176C2.98878 25.1183 3.0529 24.3189 3.37207 23.4225C3.46752 23.1019 3.62859 22.7827 3.78817 22.3994C4.01188 21.8879 4.2684 21.3435 4.52343 20.7992L4.55624 20.735C6.76352 15.967 9.13185 11.1036 11.5957 6.3683L11.6911 6.17591C11.9476 5.69717 12.2027 5.18413 12.4592 4.7039C12.7157 4.19234 13.0036 3.71211 13.3556 3.29601C14.0281 2.52793 14.923 2.11183 15.9148 2.11183C16.9066 2.11183 17.8029 2.52793 18.4741 3.29601C18.826 3.71211 19.1138 4.19234 19.3704 4.7039C19.6269 5.18413 19.8819 5.69568 20.1384 6.17442L20.2354 6.36681C22.6664 11.1333 25.0347 15.9968 27.242 20.7649V20.7961C27.4985 21.3077 27.7223 21.8849 27.9773 22.3964C28.1369 22.7813 28.2979 23.1004 28.3934 23.4195C28.6484 24.2533 28.7438 25.0527 28.6156 25.8849ZM15.8492 24.38C14.1221 22.2041 13.0021 20.1564 12.6173 18.4293C12.4577 17.6926 12.4249 17.0527 12.5218 16.477C12.586 15.9655 12.7784 15.5166 13.0334 15.1333C13.6419 14.2698 14.665 13.7254 15.8492 13.7254C17.0333 13.7254 18.0893 14.2384 18.665 15.1333C18.9215 15.5181 19.1124 15.9655 19.1765 16.477C19.2719 17.0527 19.2407 17.7253 19.081 18.4293C18.6978 20.125 17.5777 22.1727 15.8492 24.38ZM30.28 22.7171C30.1204 22.3338 29.9594 21.9177 29.7998 21.5658C29.5432 20.9901 29.2882 20.4457 29.063 19.9341L29.0317 19.9028C26.8244 15.1035 24.4561 10.24 21.961 5.44065L21.8655 5.24826C21.609 4.76802 21.3539 4.25648 21.0974 3.74492C20.7768 3.16924 20.4576 2.56074 19.9461 1.98506C18.9215 0.703944 17.4495 0 15.882 0C14.2817 0 12.8425 0.703944 11.7851 1.91944C11.3049 2.49512 10.9529 3.10361 10.6337 3.6793C10.3772 4.19085 10.1221 4.7024 9.86562 5.18263L9.77022 5.37354C7.30639 10.1729 4.90672 15.0364 2.69944 19.8357L2.66812 19.8998C2.44441 20.4114 2.18789 20.9558 1.93137 21.5314C1.75986 21.9103 1.60028 22.295 1.45114 22.6828C1.03503 23.867 0.906774 24.987 1.06635 26.1384C1.41833 28.5381 3.0186 30.5544 5.22588 31.4493C6.05809 31.8012 6.92162 31.9609 7.81795 31.9609C8.07447 31.9609 8.39363 31.9295 8.65014 31.8967C9.70607 31.7684 10.7933 31.418 11.8493 30.8095C13.1616 30.0742 14.4085 29.0183 15.8164 27.4821C17.2243 29.0183 18.5038 30.0742 19.7835 30.8095C20.8394 31.418 21.9267 31.7684 22.9826 31.8967C23.2391 31.9295 23.5583 31.9609 23.8148 31.9609C24.7111 31.9609 25.606 31.8012 26.4068 31.4493C28.6469 30.5529 30.2144 28.5052 30.5664 26.1384C30.8229 25.0198 30.6947 23.9013 30.28 22.7171Z" fill="#E0565B"/>
                      </svg>
                      <div class="grow ml-2">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Airbnb.com</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Yes</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">4m</span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </span>
                  </div>
                </td>
                <td class="h-px w-px whitespace-nowrap">
                  <div class="px-6 py-2 flex gap-x-1">
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-600 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                    <svg class="w-3 h-3 text-gray-300 dark:text-gray-700" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                    </svg>
                  </div>
                </td>

                <td class="p-4 align-middle pr-0">
                                    <div class="inline-flex items-center border rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground">NoFollow</div>
                                </td>

              </tr>

            </tbody>
          </table>
          <!-- End Table -->


          <!-- Table Footer -->
          <div class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200 dark:border-gray-700">

            {{-- Pagination --}}
            <div class="inline-flex items-center gap-x-2">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Showing:
              </p>
              <div class="max-w-sm space-y-3">
                <select class="py-2 px-3 pr-9 block w-full border-gray-200 rounded-md text-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400">
                  <option>1</option>
                  <option>2</option>
                  <option>3</option>
                  <option>4</option>
                  <option selected>9</option>
                  <option>20</option>
                </select>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                of 20
              </p>
            </div>

            <nav>
                    <ul class="flex items-center text-sm leading-tight bg-white border divide-x rounded h-9 text-neutral-500 divide-neutral-200 border-neutral-200">
                    <li class="h-full">
                        <a href="#" class="relative inline-flex items-center h-full px-3 ml-0 rounded-l group hover:text-neutral-900">
                            <span>Previous</span>
                        </a>
                    </li>
                    <li class="hidden h-full md:block">
                        <a href="#" class="relative inline-flex items-center h-full px-3 text-neutral-900 group bg-gray-50">
                            <span>1</span>
                            <span class="box-content absolute bottom-0 left-0 w-full h-px -mx-px translate-y-px border-l border-r bg-neutral-900 border-neutral-900"></span>
                        </a>
                    </li>
                    <li class="hidden h-full md:block">
                        <a href="#" class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                            <span>2</span>
                            <span class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li class="hidden h-full md:block">
                        <a href="#" class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                            <span>3</span>
                            <span class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li class="hidden h-full md:block">
                        <div class="relative inline-flex items-center h-full px-2.5 group">
                            <span>...</span>
                        </div>
                    </li>
                    <li class="hidden h-full md:block">
                        <a href="#" class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                            <span>6</span>
                            <span class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li class="hidden h-full md:block">
                        <a href="#" class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                            <span>7</span>
                            <span class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li class="hidden h-full md:block">
                        <a href="#" class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                            <span>8</span>
                            <span class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li class="h-full">
                        <a href="#" class="relative inline-flex items-center h-full px-3 rounded-r group hover:text-neutral-900">
                            <span>Next</span>
                        </a>
                    </li>
                    </ul>
                    </nav>


                    {{-- Prev - next -> Buttons --}}
            <div>
              <div class="inline-flex gap-x-2">
                <button type="button" class="py-2 px-3 inline-flex justify-center items-center gap-2 rounded-md border font-medium bg-white text-gray-700 shadow-sm align-middle hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-600 transition-all text-sm dark:bg-slate-900 dark:hover:bg-slate-800 dark:border-gray-700 dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-800">
                  <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
                  </svg>
                  Prev
                </button>

                <button type="button" class="py-2 px-3 inline-flex justify-center items-center gap-2 rounded-md border font-medium bg-white text-gray-700 shadow-sm align-middle hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-600 transition-all text-sm dark:bg-slate-900 dark:hover:bg-slate-800 dark:border-gray-700 dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-800">
                  Next
                  <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
                  </svg>
                </button>
              </div>
            </div>


          </div>
          <!-- End Footer -->
        </div>
      </div>
    </div>
  </div>
  <!-- End Card -->
</div>
<!-- End Table Section -->




{{-- LangFuse - https://cloud.langfuse.com/project/ --}}
<div class="md:container w-9/12 m-auto my-12">
  <div class="mb-4">
    <div></div>
    <div class="mt-2 flex flex-wrap items-center justify-between gap-2">
      <div class="flex items-center gap-3 md:gap-5">
        <div class="flex min-w-0 flex-row"><h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">langfuse-docs</h2></div>
      </div>
      <div class="flex items-center gap-3"></div>
    </div>
  </div>
  <div class="my-3 flex flex-wrap items-center justify-between gap-2">
    <div class="flex flex-col gap-2 lg:flex-row">
      <div class="my-0 flex max-w-full flex-col-reverse gap-2 overflow-x-auto md:flex-row">
        <button class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 w-[330px] items-center justify-start whitespace-nowrap rounded-md border px-4 py-2 text-left text-sm font-normal transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" id="date" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rd:" data-state="closed">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar mr-2 h-4 w-4">
            <path d="M8 2v4"></path>
            <path d="M16 2v4"></path>
            <rect width="18" height="18" x="3" y="4" rx="2"></rect>
            <path d="M3 10h18"></path></svg
          >Mar 11, 24 : 02:56 - Mar 18, 24 : 02:56</button
        ><button type="button" role="combobox" aria-controls="radix-:re:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring [&>span]:line-clamp-1 hover:bg-accent hover:text-accent-foreground flex h-10 w-[120px] items-center justify-between rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-0 focus:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50">
          <span style="pointer-events: none;">Date range</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 opacity-50" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg>
        </button>
      </div>
      <div class="flex items-center">
        <button class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rf:" data-state="closed">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-filter mr-3 h-4 w-4"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg><span>Filter</span>
        </button>
      </div>
    </div>
    <button class="ring-offset-background focus-visible:ring-ring border-input bg-background group hidden h-10 items-center justify-start gap-x-3 whitespace-nowrap rounded-md border px-4 py-2 text-left text-sm font-semibold text-gray-700 transition-colors hover:bg-gray-50 hover:text-indigo-600 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 lg:flex" id="date" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:rg:" data-state="closed">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bar-chart2 hidden h-6 w-6 shrink-0 text-gray-700 group-hover:text-indigo-600 lg:block" aria-hidden="true">
        <line x1="18" x2="18" y1="20" y2="10"></line>
        <line x1="12" x2="12" y1="20" y2="4"></line>
        <line x1="6" x2="6" y1="20" y2="14"></line></svg
      >Request Chart
    </button>
  </div>
  <div class="grid w-full grid-cols-1 gap-4 overflow-hidden lg:grid-cols-2 xl:grid-cols-6">
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-2">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Traces</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="tremor-Flex-root animate-in flex w-full flex-row items-baseline justify-start space-x-2">
          <p class="text-tremor-metric text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-semibold">412</p>
          <p class="text-tremor-default text-tremor-content dark:text-dark-tremor-content">Total traces tracked</p>
        </div>
        <div class="tremor-BarList-root mt-6 flex justify-between space-x-6">
          <div class="tremor-BarList-bars relative w-full">
            <div class="tremor-BarList-bar rounded-tremor-small mb-2 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 100%; transition: all 1s ease 0s;">
              <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">qa</p></div>
            </div>
            <div class="tremor-BarList-bar rounded-tremor-small mb-0 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 1%; transition: all 1s ease 0s;">
              <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">Unknown</p></div>
            </div>
          </div>
          <div class="min-w-min text-right">
            <div class="tremor-BarList-labelWrapper mb-2 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">411</p></div>
            <div class="tremor-BarList-labelWrapper mb-0 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">1</p></div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-2">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Model costs</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="tremor-Flex-root animate-in flex w-full flex-row items-baseline justify-start space-x-2">
          <p class="text-tremor-metric text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-semibold">$0.0698</p>
          <p class="text-tremor-default text-tremor-content dark:text-dark-tremor-content">Total cost</p>
          <a rel="noopener" target="_blank" class="mx-1 inline-block cursor-pointer whitespace-nowrap text-gray-500 sm:pl-0" data-state="closed" href="https://langfuse.com/docs/model-usage-and-cost"
            ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info h-4 w-4">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 16v-4"></path>
              <path d="M12 8h.01"></path></svg
          ></a>
        </div>
        <div class="mt-4">
          <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="animate-in animate-out min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">Model</th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">Tokens</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">USD</div></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">gpt-3.5-turbo</td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">105.15K</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">$0.0696</div></td>
                  </tr>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">text-embedding-ada-002</td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">2.26K</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">$0.0002</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-2">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Scores</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="tremor-Flex-root animate-in flex w-full flex-row items-baseline justify-start space-x-2">
          <p class="text-tremor-metric text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-semibold">67</p>
          <p class="text-tremor-default text-tremor-content dark:text-dark-tremor-content">Total scores tracked</p>
        </div>
        <div class="mt-4">
          <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="animate-in animate-out min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">Name</th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">#</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">Avg</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">0</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">1</div></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">user-feedback</td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">66</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.67</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">22</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">44</div></td>
                  </tr>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">manual-score</td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">-1</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-3">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Traces</h3></div>
      <div class="flex flex-1 flex-col content-end gap-4 p-6 pt-0">
        <div class="tremor-Flex-root animate-in flex w-full flex-row items-baseline justify-start space-x-2">
          <p class="text-tremor-metric text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-semibold">412</p>
          <p class="text-tremor-default text-tremor-content dark:text-dark-tremor-content">Traces tracked</p>
        </div>
        <div class="mt-4 h-full min-h-80 w-full self-stretch">
          <div class="recharts-responsive-container h-full w-full" style="width: 100%; height: 100%; min-width: 0px;">
            <div class="recharts-wrapper" role="region" style="position: relative; cursor: default; width: 100%; height: 100%; max-height: 374px; max-width: 610px;">
              <svg class="recharts-surface" width="610" height="374" viewBox="0 0 610 374" style="width: 100%; height: 100%;">
                <title></title>
                <desc></desc>
                <defs>
                  <clipPath id="recharts1-clip"><rect x="61" y="49" height="290" width="544"></rect></clipPath>
                </defs>
                <g class="recharts-cartesian-grid">
                  <g class="recharts-cartesian-grid-horizontal">
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="290" x1="61" y1="339" x2="605" y2="339"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="290" x1="61" y1="266.5" x2="605" y2="266.5"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="290" x1="61" y1="194" x2="605" y2="194"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="290" x1="61" y1="121.5" x2="605" y2="121.5"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="290" x1="61" y1="49" x2="605" y2="49"></line>
                  </g>
                </g>
                <g class="recharts-layer recharts-cartesian-axis recharts-xAxis xAxis text-tremor-label fill-tremor-content dark:fill-dark-tremor-content">
                  <g class="recharts-cartesian-axis-ticks">
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="81" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="81" dy="0.71em">3/11/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="153" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="153" dy="0.71em">3/12/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="225" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="225" dy="0.71em">3/13/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="297" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="297" dy="0.71em">3/14/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="369" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="369" dy="0.71em">3/15/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="441" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="441" dy="0.71em">3/16/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="513" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="513" dy="0.71em">3/17/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="585" y="347" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="585" dy="0.71em">3/18/24</tspan></text>
                    </g>
                  </g>
                </g>
                <g class="recharts-layer recharts-cartesian-axis recharts-yAxis yAxis text-tremor-label fill-tremor-content dark:fill-dark-tremor-content">
                  <g class="recharts-cartesian-axis-ticks">
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="290" transform="translate(-3, 0)" x="53" y="339" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">0</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="290" transform="translate(-3, 0)" x="53" y="266.5" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">25</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="290" transform="translate(-3, 0)" x="53" y="194" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">50</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="290" transform="translate(-3, 0)" x="53" y="121.5" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">75</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="290" transform="translate(-3, 0)" x="53" y="49" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">100</tspan></text>
                    </g>
                  </g>
                </g>
                <defs>
                  <linearGradient class="text-indigo-500" id="indigo" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stop-color="currentColor" stop-opacity="0.4"></stop>
                    <stop offset="95%" stop-color="currentColor" stop-opacity="0"></stop>
                  </linearGradient>
                </defs>
                <g class="recharts-layer recharts-area stroke-indigo-500">
                  <g class="recharts-layer">
                    <path class="recharts-curve recharts-area-area" stroke-opacity="1" name="Traces" fill="url(#indigo)" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" fill-opacity="0.6" width="544" height="290" stroke="none" d="M81,339L153,159.2L225,57.7L297,167.9L369,156.3L441,101.2L513,257.8L585,278.1L585,339L513,339L441,339L369,339L297,339L225,339L153,339L81,339Z"></path>
                    <path class="recharts-curve recharts-area-curve" stroke-opacity="1" name="Traces" stroke="" fill="none" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" fill-opacity="0.6" width="544" height="290" d="M81,339L153,159.2L225,57.7L297,167.9L369,156.3L441,101.2L513,257.8L585,278.1"></path>
                  </g>
                  <g class="recharts-layer recharts-area-dots"></g>
                </g>
                <g class="recharts-layer recharts-line cursor-pointer">
                  <path class="recharts-curve recharts-line-curve" stroke-opacity="0" name="Traces" stroke="transparent" fill="none" stroke-width="12" width="544" height="290" d="M81,339L153,159.2L225,57.7L297,167.9L369,156.3L441,101.2L513,257.8L585,278.1"></path>
                  <g class="recharts-layer"></g>
                  <g class="recharts-layer recharts-line-dots">
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="81" cy="339"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="153" cy="159.2"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="225" cy="57.70000000000001"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="297" cy="167.9"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="369" cy="156.29999999999998"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="441" cy="101.20000000000002"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="513" cy="257.8"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="Traces" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="290" cx="585" cy="278.1"></circle>
                  </g>
                </g>
              </svg>
              <div class="recharts-legend-wrapper" style="position: absolute; width: 600px; height: 44px; left: 5px; top: 5px;">
                <div class="flex items-center justify-end">
                  <ol class="tremor-Legend-root relative overflow-hidden">
                    <div tabindex="0" class="flex h-full">
                      <li class="tremor-Legend-legendItem rounded-tremor-small text-tremor-content hover:bg-tremor-background-subtle dark:text-dark-tremor-content dark:hover:bg-dark-tremor-background-subtle group inline-flex cursor-pointer items-center whitespace-nowrap px-2 py-0.5 transition">
                        <svg class="mr-1.5 h-2 w-2 flex-none text-indigo-500 opacity-100" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="4"></circle></svg>
                        <p class="text-tremor-default text-tremor-content group-hover:text-tremor-content-emphasis dark:text-dark-tremor-content dark:group-hover:text-dark-tremor-content-emphasis truncate whitespace-nowrap opacity-100">Traces</p>
                      </li>
                    </div>
                  </ol>
                </div>
              </div>
              <div tabindex="-1" role="dialog" class="recharts-tooltip-wrapper recharts-tooltip-wrapper-left recharts-tooltip-wrapper-top" style="visibility: hidden; pointer-events: none; position: absolute; top: 0px; left: 0px; outline: none; transform: translate(356.117px, 0px);"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex min-h-24 flex-col rounded-lg border shadow-sm xl:col-span-3">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Model Usage</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div>
          <div class="sm:hidden">
            <label for="tabs" class="sr-only">Select a tab</label
            ><select id="tabs" name="tabs" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm">
              <option>Total cost</option>
              <option>Total tokens</option>
            </select>
          </div>
          <div class="hidden sm:block">
            <div class="border-b border-gray-200">
              <nav class="-mb-px flex space-x-8" aria-label="Tabs"><a class="cursor-pointer whitespace-nowrap border-b-2 border-indigo-500 px-1 py-3 text-sm font-medium text-indigo-600" aria-current="page">Total cost</a><a class="cursor-pointer whitespace-nowrap border-b-2 border-transparent px-1 py-3 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">Total tokens</a></nav>
            </div>
          </div>
          <div class="mt-6">
            <div class="tremor-Flex-root animate-in flex w-full flex-row items-baseline justify-start space-x-2">
              <p class="text-tremor-metric text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-semibold">$0.0698</p>
              <p class="text-tremor-default text-tremor-content dark:text-dark-tremor-content">Token cost</p>
            </div>
            <div class="mt-4 h-80 w-full">
              <div class="recharts-responsive-container h-full w-full" style="width: 100%; height: 100%; min-width: 0px;">
                <div class="recharts-wrapper" role="region" style="position: relative; cursor: default; width: 100%; height: 100%; max-height: 320px; max-width: 610px;">
                  <svg class="recharts-surface" width="610" height="320" viewBox="0 0 610 320" style="width: 100%; height: 100%;">
                    <title></title>
                    <desc></desc>
                    <defs>
                      <clipPath id="recharts65-clip"><rect x="61" y="49" height="236" width="544"></rect></clipPath>
                    </defs>
                    <g class="recharts-cartesian-grid">
                      <g class="recharts-cartesian-grid-horizontal">
                        <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="285" x2="605" y2="285"></line>
                        <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="226" x2="605" y2="226"></line>
                        <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="167" x2="605" y2="167"></line>
                        <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="108" x2="605" y2="108"></line>
                        <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="49" x2="605" y2="49"></line>
                      </g>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis recharts-xAxis xAxis text-tremor-label fill-tremor-content dark:fill-dark-tremor-content">
                      <g class="recharts-cartesian-axis-ticks">
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="81" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="81" dy="0.71em">3/11/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="153" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="153" dy="0.71em">3/12/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="225" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="225" dy="0.71em">3/13/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="297" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="297" dy="0.71em">3/14/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="369" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="369" dy="0.71em">3/15/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="441" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="441" dy="0.71em">3/16/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="513" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="513" dy="0.71em">3/17/24</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="585" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="585" dy="0.71em">3/18/24</tspan></text>
                        </g>
                      </g>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis recharts-yAxis yAxis text-tremor-label fill-tremor-content dark:fill-dark-tremor-content">
                      <g class="recharts-cartesian-axis-ticks">
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="285" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">$0.00</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="226" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">$0.0045</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="167" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">$0.009</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="108" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">$0.0135</tspan></text>
                        </g>
                        <g class="recharts-layer recharts-cartesian-axis-tick">
                          <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="49" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">$0.018</tspan></text>
                        </g>
                      </g>
                    </g>
                    <g class="recharts-layer recharts-line stroke-indigo-500">
                      <path class="recharts-curve recharts-line-curve" stroke-opacity="1" name="gpt-3.5-turbo" stroke="" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" fill="none" width="544" height="236" d="M81,285L153,162.018L225,67.952L297,163.073L369,114.064L441,105.05L513,227.724L585,243.044"></path>
                      <g class="recharts-layer"></g>
                      <g class="recharts-layer recharts-line-dots"></g>
                    </g>
                    <g class="recharts-layer recharts-line stroke-cyan-500">
                      <path class="recharts-curve recharts-line-curve" stroke-opacity="1" name="text-embedding-ada-002" stroke="" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" fill="none" width="544" height="236" d="M81,285L153,284.612L225,284.299L297,284.605L369,284.415L441,284.414L513,284.818L585,284.873"></path>
                      <g class="recharts-layer"></g>
                      <g class="recharts-layer recharts-line-dots"></g>
                    </g>
                    <g class="recharts-layer recharts-line cursor-pointer">
                      <path class="recharts-curve recharts-line-curve" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="none" stroke-width="12" width="544" height="236" d="M81,285L153,162.018L225,67.952L297,163.073L369,114.064L441,105.05L513,227.724L585,243.044"></path>
                      <g class="recharts-layer"></g>
                      <g class="recharts-layer recharts-line-dots">
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="81" cy="285"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="153" cy="162.01777777777778"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="225" cy="67.9521111111111"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="297" cy="163.0732222222222"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="369" cy="114.06388888888887"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="441" cy="105.04999999999998"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="513" cy="227.7241111111111"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="gpt-3.5-turbo" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="585" cy="243.04444444444442"></circle>
                      </g>
                    </g>
                    <g class="recharts-layer recharts-line cursor-pointer">
                      <path class="recharts-curve recharts-line-curve" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="none" stroke-width="12" width="544" height="236" d="M81,285L153,284.612L225,284.299L297,284.605L369,284.415L441,284.414L513,284.818L585,284.873"></path>
                      <g class="recharts-layer"></g>
                      <g class="recharts-layer recharts-line-dots">
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="81" cy="285"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="153" cy="284.6119111111111"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="225" cy="284.29855555555554"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="297" cy="284.60535555555555"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="369" cy="284.41524444444445"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="441" cy="284.4139333333334"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="513" cy="284.81775555555555"></circle>
                        <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="text-embedding-ada-002" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="585" cy="284.8728222222222"></circle>
                      </g>
                    </g>
                  </svg>
                  <div class="recharts-legend-wrapper" style="position: absolute; width: 600px; height: 44px; left: 5px; top: 5px;">
                    <div class="flex items-center justify-end">
                      <ol class="tremor-Legend-root relative overflow-hidden">
                        <div tabindex="0" class="flex h-full">
                          <li class="tremor-Legend-legendItem rounded-tremor-small text-tremor-content hover:bg-tremor-background-subtle dark:text-dark-tremor-content dark:hover:bg-dark-tremor-background-subtle group inline-flex cursor-pointer items-center whitespace-nowrap px-2 py-0.5 transition">
                            <svg class="mr-1.5 h-2 w-2 flex-none text-indigo-500 opacity-100" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="4"></circle></svg>
                            <p class="text-tremor-default text-tremor-content group-hover:text-tremor-content-emphasis dark:text-dark-tremor-content dark:group-hover:text-dark-tremor-content-emphasis truncate whitespace-nowrap opacity-100">gpt-3.5-turbo</p>
                          </li>
                          <li class="tremor-Legend-legendItem rounded-tremor-small text-tremor-content hover:bg-tremor-background-subtle dark:text-dark-tremor-content dark:hover:bg-dark-tremor-background-subtle group inline-flex cursor-pointer items-center whitespace-nowrap px-2 py-0.5 transition">
                            <svg class="mr-1.5 h-2 w-2 flex-none text-cyan-500 opacity-100" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="4"></circle></svg>
                            <p class="text-tremor-default text-tremor-content group-hover:text-tremor-content-emphasis dark:text-dark-tremor-content dark:group-hover:text-dark-tremor-content-emphasis truncate whitespace-nowrap opacity-100">text-embedding-ada-002</p>
                          </li>
                        </div>
                      </ol>
                    </div>
                  </div>
                  <div tabindex="-1" role="dialog" class="recharts-tooltip-wrapper recharts-tooltip-wrapper-right recharts-tooltip-wrapper-top" style="visibility: hidden; pointer-events: none; position: absolute; top: 0px; left: 0px; outline: none; transform: translate(235px, 0px);"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-3">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">User consumption</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div>
          <div class="sm:hidden">
            <label for="tabs" class="sr-only">Select a tab</label
            ><select id="tabs" name="tabs" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm">
              <option>Token cost</option>
              <option>Count of Traces</option>
            </select>
          </div>
          <div class="hidden sm:block">
            <div class="border-b border-gray-200">
              <nav class="-mb-px flex space-x-8" aria-label="Tabs"><a class="cursor-pointer whitespace-nowrap border-b-2 border-indigo-500 px-1 py-3 text-sm font-medium text-indigo-600" aria-current="page">Token cost</a><a class="cursor-pointer whitespace-nowrap border-b-2 border-transparent px-1 py-3 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">Count of Traces</a></nav>
            </div>
          </div>
          <div class="mt-6">
            <div class="tremor-Flex-root animate-in flex w-full flex-row items-baseline justify-start space-x-2">
              <p class="text-tremor-metric text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis font-semibold">$0.0698</p>
              <p class="text-tremor-default text-tremor-content dark:text-dark-tremor-content">Total cost</p>
            </div>
            <div class="tremor-BarList-root mt-2 flex justify-between space-x-6">
              <div class="tremor-BarList-bars relative w-full">
                <div class="tremor-BarList-bar rounded-tremor-small mb-2 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 100%; transition: all 1s ease 0s;">
                  <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">u-LiqtOuy</p></div>
                </div>
                <div class="tremor-BarList-bar rounded-tremor-small mb-2 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 66.8047%; transition: all 1s ease 0s;">
                  <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">u-hFthw9D</p></div>
                </div>
                <div class="tremor-BarList-bar rounded-tremor-small mb-2 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 47.5332%; transition: all 1s ease 0s;">
                  <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">u-V0e3wqT</p></div>
                </div>
                <div class="tremor-BarList-bar rounded-tremor-small mb-2 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 39.694%; transition: all 1s ease 0s;">
                  <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">u-1ohA6qB</p></div>
                </div>
                <div class="tremor-BarList-bar rounded-tremor-small mb-0 flex h-9 items-center bg-indigo-500 bg-opacity-30" style="width: 34.4901%; transition: all 1s ease 0s;">
                  <div class="absolute left-2 flex max-w-full"><p class="tremor-BarList-barText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">u-oCUKn7G</p></div>
                </div>
              </div>
              <div class="min-w-min text-right">
                <div class="tremor-BarList-labelWrapper mb-2 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">$0.0057</p></div>
                <div class="tremor-BarList-labelWrapper mb-2 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">$0.0038</p></div>
                <div class="tremor-BarList-labelWrapper mb-2 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">$0.0027</p></div>
                <div class="tremor-BarList-labelWrapper mb-2 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">$0.0023</p></div>
                <div class="tremor-BarList-labelWrapper mb-0 flex h-9 items-center justify-end"><p class="tremor-BarList-labelText text-tremor-default text-tremor-content-emphasis dark:text-dark-tremor-content-emphasis truncate whitespace-nowrap">$0.002</p></div>
              </div>
            </div>
          </div>
        </div>
        <button class="ring-offset-background focus-visible:ring-ring hover:bg-accent hover:text-accent-foreground mt-2 inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down mr-2 h-4 w-4"><path d="m6 9 6 6 6-6"></path></svg> Show top 20
        </button>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-3">
      <div class="relative flex flex-col space-y-1.5 p-6">
        <h3 class="text-2xl font-semibold leading-none tracking-tight">Scores</h3>
        <p class="text-muted-foreground text-sm">Average score per name</p>
      </div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="mt-4 h-80 w-full">
          <div class="recharts-responsive-container h-full w-full" style="width: 100%; height: 100%; min-width: 0px;">
            <div class="recharts-wrapper" role="region" style="position: relative; cursor: default; width: 100%; height: 100%; max-height: 320px; max-width: 610px;">
              <svg class="recharts-surface" width="610" height="320" viewBox="0 0 610 320" style="width: 100%; height: 100%;">
                <title></title>
                <desc></desc>
                <defs>
                  <clipPath id="recharts8-clip"><rect x="61" y="49" height="236" width="544"></rect></clipPath>
                </defs>
                <g class="recharts-cartesian-grid">
                  <g class="recharts-cartesian-grid-horizontal">
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="285" x2="605" y2="285"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="226" x2="605" y2="226"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="167" x2="605" y2="167"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="108" x2="605" y2="108"></line>
                    <line class="stroke-tremor-border dark:stroke-dark-tremor-border stroke-1" stroke="#ccc" fill="none" x="61" y="49" width="544" height="236" x1="61" y1="49" x2="605" y2="49"></line>
                  </g>
                </g>
                <g class="recharts-layer recharts-cartesian-axis recharts-xAxis xAxis text-tremor-label fill-tremor-content dark:fill-dark-tremor-content">
                  <g class="recharts-cartesian-axis-ticks">
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="81" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="81" dy="0.71em">3/11/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="153" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="153" dy="0.71em">3/12/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="225" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="225" dy="0.71em">3/13/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="297" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="297" dy="0.71em">3/14/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="369" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="369" dy="0.71em">3/15/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="441" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="441" dy="0.71em">3/16/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="513" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="513" dy="0.71em">3/17/24</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text stroke="none" height="30" orientation="bottom" width="544" transform="translate(0, 6)" x="585" y="293" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="middle" fill=""><tspan x="585" dy="0.71em">3/18/24</tspan></text>
                    </g>
                  </g>
                </g>
                <g class="recharts-layer recharts-cartesian-axis recharts-yAxis yAxis text-tremor-label fill-tremor-content dark:fill-dark-tremor-content">
                  <g class="recharts-cartesian-axis-ticks">
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="285" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">-1</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="226" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">-0.5</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="167" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">0</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="108" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">0.5</tspan></text>
                    </g>
                    <g class="recharts-layer recharts-cartesian-axis-tick">
                      <text width="56" stroke="none" orientation="left" height="236" transform="translate(-3, 0)" x="53" y="49" class="recharts-text recharts-cartesian-axis-tick-value" text-anchor="end" fill=""><tspan x="53" dy="0.355em">1</tspan></text>
                    </g>
                  </g>
                </g>
                <g class="recharts-layer recharts-line stroke-indigo-500">
                  <path class="recharts-curve recharts-line-curve" stroke-opacity="1" name="user-feedback" stroke="" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" fill="none" width="544" height="236" d="M153,65.857L225,88.333L297,98.167L369,96.2L441,90.647L513,49L585,78.5"></path>
                  <g class="recharts-layer"></g>
                  <g class="recharts-layer recharts-line-dots"></g>
                </g>
                <g class="recharts-layer recharts-line stroke-cyan-500">
                  <path class="recharts-curve recharts-line-curve" stroke-opacity="1" name="manual-score" stroke="" stroke-width="2" stroke-linejoin="round" stroke-linecap="round" fill="none" width="544" height="236" d="M225,285Z"></path>
                  <g class="recharts-layer"></g>
                  <g class="recharts-layer recharts-line-dots"><circle cx="225" cy="285" r="5" stroke="" fill="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="recharts-dot stroke-tremor-background dark:stroke-dark-tremor-background cursor-pointer fill-cyan-500"></circle></g>
                </g>
                <g class="recharts-layer recharts-line cursor-pointer">
                  <path class="recharts-curve recharts-line-curve" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="none" stroke-width="12" width="544" height="236" d="M153,65.857L225,88.333L297,98.167L369,96.2L441,90.647L513,49L585,78.5"></path>
                  <g class="recharts-layer"></g>
                  <g class="recharts-layer recharts-line-dots">
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="153" cy="65.85714285714285"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="225" cy="88.33333333333334"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="297" cy="98.16666666666666"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="369" cy="96.19999999999999"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="441" cy="90.64705882352942"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="513" cy="49"></circle>
                    <circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="user-feedback" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="585" cy="78.5"></circle>
                  </g>
                </g>
                <g class="recharts-layer recharts-line cursor-pointer">
                  <path class="recharts-curve recharts-line-curve" stroke-opacity="0" name="manual-score" stroke="transparent" fill="none" stroke-width="12" width="544" height="236" d="M225,285Z"></path>
                  <g class="recharts-layer"></g>
                  <g class="recharts-layer recharts-line-dots"><circle r="3" class="recharts-dot recharts-line-dot" stroke-opacity="0" name="manual-score" stroke="transparent" fill="transparent" stroke-width="12" width="544" height="236" cx="225" cy="285"></circle></g>
                </g>
              </svg>
              <div class="recharts-legend-wrapper" style="position: absolute; width: 600px; height: 44px; left: 5px; top: 5px;">
                <div class="flex items-center justify-end">
                  <ol class="tremor-Legend-root relative overflow-hidden">
                    <div tabindex="0" class="flex h-full">
                      <li class="tremor-Legend-legendItem rounded-tremor-small text-tremor-content hover:bg-tremor-background-subtle dark:text-dark-tremor-content dark:hover:bg-dark-tremor-background-subtle group inline-flex cursor-pointer items-center whitespace-nowrap px-2 py-0.5 transition">
                        <svg class="mr-1.5 h-2 w-2 flex-none text-indigo-500 opacity-100" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="4"></circle></svg>
                        <p class="text-tremor-default text-tremor-content group-hover:text-tremor-content-emphasis dark:text-dark-tremor-content dark:group-hover:text-dark-tremor-content-emphasis truncate whitespace-nowrap opacity-100">user-feedback</p>
                      </li>
                      <li class="tremor-Legend-legendItem rounded-tremor-small text-tremor-content hover:bg-tremor-background-subtle dark:text-dark-tremor-content dark:hover:bg-dark-tremor-background-subtle group inline-flex cursor-pointer items-center whitespace-nowrap px-2 py-0.5 transition">
                        <svg class="mr-1.5 h-2 w-2 flex-none text-cyan-500 opacity-100" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="4"></circle></svg>
                        <p class="text-tremor-default text-tremor-content group-hover:text-tremor-content-emphasis dark:text-dark-tremor-content dark:group-hover:text-dark-tremor-content-emphasis truncate whitespace-nowrap opacity-100">manual-score</p>
                      </li>
                    </div>
                  </ol>
                </div>
              </div>
              <div tabindex="-1" role="dialog" class="recharts-tooltip-wrapper" style="visibility: hidden; pointer-events: none; position: absolute; top: 0px; left: 0px; outline: none;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-2">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Trace latencies</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="mt-4">
          <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="animate-in animate-out min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">Trace Name</th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">50th</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">90th</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">
                      <div class="text-right">95th<span class="ml-1">▼</span></div>
                    </th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">99th</div></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">qa</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">2.299s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">3.479s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">4.109s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">5.635s</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-2">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Generation latencies</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="mt-4">
          <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="animate-in animate-out min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">Generation Name</th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">50th</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">90th</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">
                      <div class="text-right">95th<span class="ml-1">▼</span></div>
                    </th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">99th</div></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">generation</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.070s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.894s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">2.466s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">4.963s</div></td>
                  </tr>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">prompt-embedding</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.403s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.661s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.770s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.610s</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-card text-card-foreground col-span-1 flex flex-col rounded-lg border shadow-sm xl:col-span-2">
      <div class="relative flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight">Span latencies</h3></div>
      <div class="flex flex-1 flex-col gap-4 p-6 pt-0">
        <div class="mt-4">
          <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="animate-in animate-out min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">Span Name</th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">50th</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">90th</div></th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0">
                      <div class="text-right">95th<span class="ml-1">▼</span></div>
                    </th>
                    <th scope="col" class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-xs font-semibold text-gray-900 sm:pl-0"><div class="text-right">99th</div></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">retrieval</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.765s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.463s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.659s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">2.486s</div></td>
                  </tr>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">vector-store</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.476s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.888s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.980s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.313s</div></td>
                  </tr>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">fetch-prompt-fro...</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.293s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.664s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">0.821s</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">1.398s</div></td>
                  </tr>
                  <tr>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0">
                      <div><div data-state="closed" class="mx-1 cursor-pointer">context-encoding</div></div>
                    </td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">-</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">-</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">-</div></td>
                    <td class="whitespace-nowrap py-2 pl-3 pr-2 text-xs text-gray-500 sm:pl-0"><div class="text-right">-</div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>








<div class="flex min-h-[92vh] w-9/12 m-auto flex-col items-center justify-start gap-12" id="features">
  <div class="flex flex-col items-center justify-center gap-5">
    <h1 class="relative text-center text-2xl font-semibold sm:text-3xl xl:text-[40px]">
      Explore Key <span class="text-primary relative">Features</span><svg width="100" height="29" viewBox="0 0 307 29" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute -bottom-7 right-0"><path d="M213.014 26.8415C178.272 19.4138 139.489 23.4441 104.338 24.1263C94.2307 24.3226 83.8895 25.6318 73.7918 25.0712C72.4748 24.9984 66.7288 24.7252 65.6509 23.2654C65.5102 23.0755 69.9908 22.3264 72.1676 22.006C76.4002 21.3829 80.6309 20.9232 84.86 20.2652C95.9785 18.5363 107.291 17.6927 118.507 16.9156C147.298 14.9223 198.803 8.77966 226.942 17.4422C228.336 17.8714 224.026 17.3684 222.568 17.3285C220.172 17.2635 217.778 17.1695 215.381 17.0942C207.566 16.8496 199.685 16.4146 191.869 16.483C166.68 16.702 141.403 15.6497 116.221 16.5922C108.643 16.8762 101.09 17.4658 93.5093 17.6937C89.1182 17.8256 89.315 17.9373 84.7768 17.7833C82.8091 17.7163 77.3531 18.3084 78.9093 17.1021C81.6501 14.9769 90.2167 15.5085 93.5299 15.0749C108.658 13.0974 123.749 10.515 138.954 9.1276C177.942 5.57026 217.632 5.56189 256.709 7.05018C272.694 7.65899 288.845 5.30402 304.762 7.20672C266.14 2.21866 225.996 2.92687 187.163 3.07107C143.44 3.23349 99.7666 3.24431 56.043 4.16564C38.0928 4.54362 20.5048 7.96207 2.5 7.71255" stroke="#7B39ED" stroke-width="4.05049" stroke-miterlimit="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
    </h1>
    <h1 class="text-center text-[13px] font-normal md:text-[17px]">Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas, voluptas.</h1>
  </div>
  <div class="grid w-full auto-rows-[250px] grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
    <div class="bg-muted relative row-span-1 flex flex-col items-start gap-4 rounded-2xl border-2 border-slate-400/10 p-4 dark:bg-neutral-900">
      <div class="relative h-[100%] w-full overflow-hidden rounded-lg border-2 border-slate-500/10 bg-white"><img alt="Secure Payments" loading="lazy" decoding="async" data-nimg="fill" sizes="100vw" srcset="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=640&q=75 640w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=750&q=75 750w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=828&q=75 828w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=1080&q=75 1080w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=1200&q=75 1200w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=1920&q=75 1920w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=2048&q=75 2048w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=3840&q=75 3840w" src="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fchart.png&w=3840&q=75" /></div>
      <div class="flex flex-col items-start gap-2">
        <p class="text-[18px] font-medium text-neutral-900 dark:text-neutral-100">Secure Payments</p>
        <p class="text-sm font-normal text-neutral-500 dark:text-neutral-400">Lock your account with a secure password.</p>
      </div>
    </div>
    <div class="bg-muted relative row-span-1 flex flex-col items-start gap-4 rounded-2xl border-2 border-slate-400/10 p-4 dark:bg-neutral-900">
      <div class="relative h-[100%] w-full overflow-hidden rounded-lg border-2 border-slate-500/10 bg-white w-8"><img alt="Worldwide Delivery" loading="lazy" decoding="async" data-nimg="fill"  sizes="100vw" srcset="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=640&q=75 640w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=750&q=75 750w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=828&q=75 828w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=1080&q=75 1080w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=1200&q=75 1200w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=1920&q=75 1920w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=2048&q=75 2048w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=3840&q=75 3840w" src="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2FlineChart.png&w=3840&q=75" /></div>
      <div class="flex flex-col items-start gap-2">
        <p class="text-[18px] font-medium text-neutral-900 dark:text-neutral-100">Worldwide Delivery</p>
        <p class="text-sm font-normal text-neutral-500 dark:text-neutral-400">We deliver to over 100 countries around the world.</p>
      </div>
    </div>
    <div class="bg-muted relative row-span-1 flex flex-col items-start gap-4 rounded-2xl border-2 border-slate-400/10 p-4 dark:bg-neutral-900">
      <div class="relative h-[100%] w-full overflow-hidden rounded-lg border-2 border-slate-500/10 bg-white"><img alt="24/7 Support" loading="lazy" decoding="async" data-nimg="fill" sizes="100vw" srcset="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=640&q=75 640w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=750&q=75 750w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=828&q=75 828w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=1080&q=75 1080w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=1200&q=75 1200w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=1920&q=75 1920w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=2048&q=75 2048w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=3840&q=75 3840w" src="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignup.png&w=3840&q=75" /></div>
      <div class="flex flex-col items-start gap-2">
        <p class="text-[18px] font-medium text-neutral-900 dark:text-neutral-100">24/7 Support</p>
        <p class="text-sm font-normal text-neutral-500 dark:text-neutral-400">Our support team is always available to help.</p>
      </div>
    </div>
    <div class="bg-muted relative row-span-1 flex flex-col items-start gap-4 rounded-2xl border-2 border-slate-400/10 p-4 dark:bg-neutral-900">
      <div class="relative h-[100%] w-full overflow-hidden rounded-lg border-2 border-slate-500/10 bg-white"><img alt="Easy Returns" loading="lazy" decoding="async" data-nimg="fill"  sizes="100vw" srcset="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=640&q=75 640w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=750&q=75 750w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=828&q=75 828w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=1080&q=75 1080w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=1200&q=75 1200w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=1920&q=75 1920w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=2048&q=75 2048w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=3840&q=75 3840w" src="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fsignin.png&w=3840&q=75" /></div>
      <div class="flex flex-col items-start gap-2">
        <p class="text-[18px] font-medium text-neutral-900 dark:text-neutral-100">Easy Returns</p>
        <p class="text-sm font-normal text-neutral-500 dark:text-neutral-400">We offer free returns within 30 days of purchase.</p>
      </div>
    </div>
    <div class="bg-muted relative col-span-1 row-span-1 flex flex-col-reverse items-start justify-end gap-4 rounded-2xl border-2 border-slate-400/10 p-4 lg:col-span-2 lg:min-h-[400px] dark:bg-neutral-900">
      <div class="relative h-[100%] w-full overflow-hidden rounded-lg border-2 border-slate-500/10 bg-white"><img alt="Original Products" loading="lazy" decoding="async" data-nimg="fill"  sizes="100vw" srcset="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=640&q=75 640w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=750&q=75 750w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=828&q=75 828w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=1080&q=75 1080w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=1200&q=75 1200w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=1920&q=75 1920w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=2048&q=75 2048w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=3840&q=75 3840w" src="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain1.png&w=3840&q=75" /></div>
      <div class="flex flex-col items-start gap-2">
        <p class="text-[18px] font-medium text-neutral-900 dark:text-neutral-100">Original Products</p>
        <p class="text-sm font-normal text-neutral-500 dark:text-neutral-400">We guarantee you only authentic products.</p>
      </div>
    </div>
    <div class="bg-muted relative col-span-1 row-span-1 flex flex-col-reverse items-start justify-end gap-4 rounded-2xl border-2 border-slate-400/10 p-4 lg:col-span-2 lg:min-h-[400px] dark:bg-neutral-900">
      <div class="relative h-[100%] w-full overflow-hidden rounded-lg border-2 border-slate-500/10 bg-white"><img alt="Cash On Delivery" loading="lazy" decoding="async" data-nimg="fill" sizes="100vw" srcset="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=640&q=75 640w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=750&q=75 750w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=828&q=75 828w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=1080&q=75 1080w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=1200&q=75 1200w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=1920&q=75 1920w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=2048&q=75 2048w, https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=3840&q=75 3840w" src="https://zelvira.indiekit.live/_next/image?url=%2Fimages%2Ffeatures%2Fmain2.png&w=3840&q=75" /></div>
      <div class="flex flex-col items-start gap-2">
        <p class="text-[18px] font-medium text-neutral-900 dark:text-neutral-100">Cash On Delivery</p>
        <p class="text-sm font-normal text-neutral-500 dark:text-neutral-400">Pay cash on delivery. No credit card required.</p>
      </div>
    </div>
  </div>
</div>



{{-- Primisic --}}
<style>

.bg-gray-15 {
    --tw-bg-opacity: 1;
    background-color: rgb(21 21 21 / var(--tw-bg-opacity));
}

.text-gray-50 {
    --tw-text-opacity: 1;
    color: rgb(80 80 80 / var(--tw-text-opacity));
}

  </style>

<div class="swiper-slide m-auto mx-auto my-12 block justify-center items-center flex pricing-card relative z-10 w-1/4 shrink-0" style="height: auto; transition-property: margin-left; transition-duration: 0.3s; margin-left: 0px; width: 324px;">
  <div class="h-full px-2 transition-all 2xl:px-3">
    <div class="border-gray-15 text-gray-15 hover:bg-quaternary-purple group flex h-full flex-col rounded-xl border-2 bg-white transition-colors">
      <h2 class="bg-gray-15 font-headings text-xl-tight 2xl:2xl-tight group-hover:bg-primary-purple rounded-t-[10px] p-4 text-center font-medium tracking-tight text-white transition-colors 2xl:p-6">Platinum</h2>
      <div class="p-4 2xl:p-6">
        <p>For companies continuously adding features to their websites.</p>
        <div class="mt-6 text-center"><span class="font-headings text-6xl font-semibold tracking-tight xl:text-7xl 2xl:text-8xl">$675</span><span class="text-sm-flat 2xl:text-base-flat font-bold">/month</span></div>
        <div class="text-base-flat mt-1 flex items-center justify-center gap-2 text-center font-medium">
          Per repository<button data-state="closed">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6">
              <circle opacity="0.25" cx="12" cy="12" r="12" fill="#A4A4A4"></circle>
              <path d="M7.96 7.748A2.63 2.63 0 0 1 10.433 6h2.733a2.958 2.958 0 0 1 2.958 2.958c0 1.06-.567 2.039-1.486 2.569l-1.514.867c-.01.61-.51 1.106-1.125 1.106a1.122 1.122 0 0 1-1.125-1.125v-.633c0-.403.216-.773.567-.975l2.077-1.19a.709.709 0 0 0-.352-1.322h-2.733a.37.37 0 0 0-.351.248l-.019.056c-.206.586-.853.891-1.434.685a1.129 1.129 0 0 1-.685-1.435l.02-.056-.006-.005ZM13.5 16.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5.67-1.5 1.5-1.5 1.5.67 1.5 1.5Z" fill="#A4A4A4"></path>
            </svg>
          </button>
        </div>
        <div class="mt-4 text-center text-sm opacity-50">paid annually</div>
        <div class="mt-8 flex min-h-[104px] flex-col">
          <div class="mt-2 flex first:mt-0"><a class="focus:ring-tertiary-purple border-gray-15 text-sm-flat 2xl:text-base-flat bg-gray-15 group block grow cursor-pointer whitespace-nowrap rounded-lg border-2 px-6 py-3 text-center font-bold leading-5 text-white transition-colors transition-opacity hover:bg-opacity-75 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:hover:bg-opacity-100 2xl:leading-5" href="https://prismic.io/dashboard/signup?redirectUri=/dashboard/new-repository?plan=platinum_yearly&source=pricingPage">Get started</a></div>
          <div class="mt-2 flex first:mt-0"><button class="focus:ring-tertiary-purple border-gray-15 text-sm-flat 2xl:text-base-flat group -mx-2.5 block grow cursor-pointer whitespace-nowrap rounded-lg px-2.5 py-3 text-center font-bold leading-5 underline underline-offset-8 hover:underline-offset-4 focus:outline-none focus:ring-4 disabled:opacity-50 2xl:leading-5">Request a 30 day free trial</button></div>
        </div>
      </div>
      <div class="border-gray-15 text-gray-15 grow border-t-2 p-4 2xl:p-6">
        <p class="lg:text-text-md my-6 text-base first:mt-0 last:mb-0">Everything in Medium and:</p>
        <li class="relative my-3 pl-6 text-base text-gray-50 first:mt-0 last:mb-0">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute left-0 top-1 h-4 w-4 text-gray-50">
            <circle opacity="0.2" cx="12" cy="12" r="12" fill="currentColor"></circle>
            <path d="M17.749 8.251a.858.858 0 0 1 0 1.213l-6.856 6.857a.858.858 0 0 1-1.214 0l-3.428-3.429a.858.858 0 0 1 1.213-1.213l2.823 2.82 6.251-6.248a.858.858 0 0 1 1.214 0h-.003Z" fill="currentColor"></path>
          </svg>
          <p>Unlimited users</p>
          <div class="text-gray-A4 text-sm leading-normal"></div>
        </li>
        <li class="relative my-3 pl-6 text-base text-gray-50 first:mt-0 last:mb-0">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute left-0 top-1 h-4 w-4 text-gray-50">
            <circle opacity="0.2" cx="12" cy="12" r="12" fill="currentColor"></circle>
            <path d="M17.749 8.251a.858.858 0 0 1 0 1.213l-6.856 6.857a.858.858 0 0 1-1.214 0l-3.428-3.429a.858.858 0 0 1 1.213-1.213l2.823 2.82 6.251-6.248a.858.858 0 0 1 1.214 0h-.003Z" fill="currentColor"></path>
          </svg>
          <p>8 locales</p>
          <div class="text-gray-A4 text-sm leading-normal"></div>
        </li>
        <li class="relative my-3 pl-6 text-base text-gray-50 first:mt-0 last:mb-0">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute left-0 top-1 h-4 w-4 text-gray-50">
            <circle opacity="0.2" cx="12" cy="12" r="12" fill="currentColor"></circle>
            <path d="M17.749 8.251a.858.858 0 0 1 0 1.213l-6.856 6.857a.858.858 0 0 1-1.214 0l-3.428-3.429a.858.858 0 0 1 1.213-1.213l2.823 2.82 6.251-6.248a.858.858 0 0 1 1.214 0h-.003Z" fill="currentColor"></path>
          </svg>
          <p>Development environment</p>
          <div class="text-gray-A4 text-sm leading-normal">
            <p><a target="_blank" class="text-primary-purple hover:underline" rel="noreferrer" href="https://prismic.io/docs/environments">Paid option</a> on yearly invoiced plan</p>
          </div>
        </li>
        <li class="relative my-3 pl-6 text-base text-gray-50 first:mt-0 last:mb-0">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute left-0 top-1 h-4 w-4 text-gray-50">
            <circle opacity="0.2" cx="12" cy="12" r="12" fill="currentColor"></circle>
            <path d="M17.749 8.251a.858.858 0 0 1 0 1.213l-6.856 6.857a.858.858 0 0 1-1.214 0l-3.428-3.429a.858.858 0 0 1 1.213-1.213l2.823 2.82 6.251-6.248a.858.858 0 0 1 1.214 0h-.003Z" fill="currentColor"></path>
          </svg>
          <p>10 million API calls a month</p>
          <div class="text-gray-A4 text-sm leading-normal"><p>Paid overages allowed</p></div>
        </li>
        <li class="relative my-3 pl-6 text-base text-gray-50 first:mt-0 last:mb-0">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute left-0 top-1 h-4 w-4 text-gray-50">
            <circle opacity="0.2" cx="12" cy="12" r="12" fill="currentColor"></circle>
            <path d="M17.749 8.251a.858.858 0 0 1 0 1.213l-6.856 6.857a.858.858 0 0 1-1.214 0l-3.428-3.429a.858.858 0 0 1 1.213-1.213l2.823 2.82 6.251-6.248a.858.858 0 0 1 1.214 0h-.003Z" fill="currentColor"></path>
          </svg>
          <p>1 TB of built-in CDN bandwidth a month</p>
          <div class="text-gray-A4 text-sm leading-normal"><p>Paid overages allowed up to 5 TB at $0.17/GB</p></div>
        </li>
      </div>
      <div class="border-gray-15 text-gray-15 border-t-2 p-4 text-center 2xl:p-6">
        <h3 class="font-headings wrap-balance text-md-tight scroll-mt-[120px] font-medium tracking-tight 2xl:text-lg">Helps you grow like</h3>
        <div class="mt-6 h-12">
          <div class="swiper swiper-flip swiper-3d swiper-initialized swiper-horizontal swiper-pointer-events swiper-watch-progress">
            <div class="swiper-wrapper" style="transition-duration: 0ms;">
              <div class="swiper-slide flex items-center justify-center" style="height: 3rem; backface-visibility: hidden; width: 248px; z-index: 3; transform: translate3d(0px, 0px, 0px) rotateX(0deg) rotateY(-180deg); transition-duration: 0ms;">
                <svg width="380" height="88" viewBox="0 0 380 88" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto h-auto max-h-12 w-auto max-w-[140px] text-gray-50">
                  <path d="M46.1733 0.0609112C21.9447 -1.20985 1.33092 17.5583 0.060877 41.8006C-1.20917 66.0429 17.5484 86.6683 41.777 87.9391C66.0055 89.2098 86.6193 70.4417 87.8894 46.1994C89.1594 21.8594 70.4018 1.23392 46.1733 0.0609112ZM70.5972 51.6735C66.7871 64.1856 58.1899 75.3292 51.7419 76.8932C45.294 78.4572 32.6913 72.2989 23.7033 62.9148C14.7152 53.5307 9.34198 40.8231 11.4913 34.0783C13.3475 28.2132 24.6802 19.7089 37.3807 16.6786C50.0811 13.6483 63.9539 15.9943 68.4479 20.3931C73.1373 25.1829 74.3097 39.0636 70.5972 51.6735Z" fill="currentColor"></path>
                  <path d="M21.4568 60.0771C18.4282 56.4603 15.7904 52.3548 13.8365 48.0537C13.2503 46.783 12.7619 45.5122 12.3711 44.1437C12.3711 44.1437 12.3711 44.1437 12.3711 44.0459C11.101 40.1359 10.8079 36.5191 11.5895 33.7821C12.5665 30.7518 15.6927 28.0148 15.595 28.0148C9.04942 34.2708 3.08997 45.1212 2.01532 56.5581L21.4568 60.0771Z" fill="url(#paint0_linear_31_34)"></path>
                  <path d="M69.4255 55.1894V54.7984C67.5692 60.077 60.242 74.6419 51.5471 76.6946C49.3001 76.9879 47.0531 76.7924 44.9038 76.2059L46.0762 76.4991C54.6734 79.1384 65.4199 78.5519 75.9711 73.8599L69.4255 55.1894Z" fill="currentColor"></path>
                  <path d="M53.6963 0.937653L40.9959 15.6003C47.1507 14.6228 53.4032 14.7205 59.4604 15.9913H59.5581C63.6613 16.9688 66.9829 18.2396 68.8392 20.3901C70.2069 22.0519 71.1839 24.1047 71.6723 26.2552C68.5461 16.0891 62.1958 7.19372 53.6963 0.937653Z" fill="currentColor"></path>
                  <path d="M199.654 56.9487V30.4582C199.654 28.8942 200.337 28.2099 201.705 28.2099H202.487C203.952 28.2099 204.636 28.8942 204.636 30.4582V55.287H218.314C219.779 55.287 220.658 56.069 220.658 57.4375V57.633C220.756 58.7083 219.877 59.7835 218.802 59.7835C218.607 59.7835 218.509 59.7835 218.314 59.7835H202.291C200.924 59.8813 199.654 58.806 199.654 57.3397C199.556 57.242 199.556 57.0465 199.654 56.9487Z" fill="currentColor"></path>
                  <path d="M235.02 56.558C239.123 56.558 241.077 54.7985 242.347 52.648C242.445 52.5502 244.887 52.9412 244.887 54.994C244.887 57.0468 241.272 60.2726 234.922 60.2726C227.497 60.2726 222.612 55.385 222.612 48.0537C222.612 41.0156 227.693 35.7371 234.531 35.7371C241.272 35.7371 245.766 40.5269 245.766 47.4672V47.8582C245.766 48.8357 245.473 49.2267 244.301 49.2267H227.106C227.302 53.5277 230.819 56.7535 235.02 56.558ZM241.077 46.0987C240.882 42.0909 238.341 39.4516 234.434 39.4516C230.623 39.4516 227.497 42.2864 227.009 46.0987H241.077Z" fill="currentColor"></path>
                  <path d="M265.11 45.2191C265.11 41.7001 263.058 39.745 259.346 39.745C256.513 39.6473 253.875 41.2113 252.605 43.7528C252.605 43.7528 249.674 43.7528 249.674 41.4068C249.674 39.0608 253.093 35.835 259.737 35.835C266.38 35.835 269.897 39.2563 269.897 45.1213V59.393C269.897 59.5885 269.018 59.8817 268.138 59.8817C266.478 59.8817 265.208 59.002 265.208 56.0695C263.254 58.7087 260.127 60.2727 256.806 60.175C251.823 60.175 248.892 57.5357 248.892 53.5279C248.892 48.1516 254.07 45.8056 264.524 45.6101H265.11V45.2191ZM265.11 50.4976V48.7381H263.84C257.001 48.9336 253.582 50.2044 253.582 53.4302C253.582 55.4829 255.243 56.8515 257.978 56.8515C262.081 56.8515 265.11 54.2122 265.11 50.4976Z" fill="currentColor"></path>
                  <path d="M286.212 35.835C288.655 35.835 290.022 37.008 290.022 38.963C290.12 40.0383 289.534 41.0158 288.557 41.4068C287.678 40.7225 286.701 40.4293 285.626 40.4293C281.816 40.4293 280.351 44.1438 280.351 49.5201V57.7312C280.351 59.4907 279.374 59.9795 278.201 59.9795H277.713C276.443 59.9795 275.563 59.4907 275.563 57.7312V36.7148C276.052 36.3237 276.638 36.1282 277.322 36.226C278.787 36.226 280.253 37.008 280.351 40.3315C281.328 37.3013 283.281 35.835 286.212 35.835Z" fill="currentColor"></path>
                  <path d="M306.142 35.8349C312.297 35.8349 315.13 40.0382 315.13 45.8055V57.6334C315.13 59.3929 314.153 59.8816 312.981 59.8816H312.493C311.223 59.8816 310.343 59.3929 310.343 57.6334V46.392C310.343 42.482 308.389 40.0382 304.97 40.0382C302.137 39.9404 299.597 41.6022 298.424 44.1437V57.7311C298.424 59.4906 297.545 59.9794 296.275 59.9794H295.787C294.517 59.9794 293.637 59.4906 293.637 57.7311V36.7147C294.126 36.3236 294.712 36.1281 295.396 36.2259C296.764 36.2259 298.229 37.0079 298.424 40.0382C300.085 37.3012 303.016 35.7371 306.142 35.8349Z" fill="currentColor"></path>
                  <path d="M320.601 28.699C320.601 26.9394 321.676 25.9619 323.532 25.9619C324.9 25.7664 326.17 26.7439 326.365 28.1124C326.365 28.3079 326.365 28.5035 326.365 28.699C326.365 30.4585 325.388 31.3382 323.532 31.3382C321.676 31.3382 320.601 30.4585 320.601 28.699ZM320.992 57.6333V38.4741C320.992 36.7145 321.871 36.128 323.141 36.128H323.63C324.9 36.128 325.779 36.6168 325.779 38.4741V57.6333C325.779 59.3928 324.9 59.8815 323.63 59.8815H323.141C321.969 59.9793 320.992 59.3928 320.992 57.6333Z" fill="currentColor"></path>
                  <path d="M344.537 35.8349C350.691 35.8349 353.525 40.0382 353.525 45.8055V57.6334C353.525 59.3929 352.645 59.8816 351.375 59.8816H350.887C349.617 59.8816 348.737 59.3929 348.737 57.6334V46.392C348.737 42.482 346.784 40.0382 343.364 40.0382C340.531 39.9404 337.991 41.6022 336.819 44.1437V57.7311C336.819 59.4906 335.939 59.9794 334.669 59.9794H334.181C332.911 59.9794 332.031 59.4906 332.031 57.7311V36.7147C332.52 36.3236 333.106 36.1281 333.79 36.2259C335.158 36.2259 336.623 37.0079 336.819 40.0382C338.479 37.3012 341.41 35.7371 344.537 35.8349Z" fill="currentColor"></path>
                  <path d="M374.724 34.9548C375.213 33.8795 376.288 33.0975 377.46 33.0975C378.632 33.0975 379.707 33.9773 379.902 35.1503L375.994 39.2558C377.265 40.7221 377.948 42.5794 377.948 44.5344C377.948 49.422 373.748 53.0387 367.886 53.0387C367.593 53.0387 367.3 53.0387 367.007 53.0387C366.811 53.332 366.713 53.6252 366.811 53.9185C366.811 55.3848 369.058 55.5803 373.161 56.5578C376.483 57.4375 380 59.0993 380 62.9116C380 67.3104 375.506 70.0474 367.788 70.0474C360.07 70.0474 356.26 67.9946 356.26 63.5959C356.26 60.1746 359.484 57.9263 363.685 57.1443C363.001 56.5578 362.708 55.7758 362.708 54.9938C362.708 54.0162 363.196 53.0387 364.076 52.4522C360.363 51.2792 357.921 48.3467 357.921 44.6322C357.921 39.7446 362.122 35.8346 367.886 35.8346C369.84 35.8346 371.794 36.3233 373.454 37.3008L374.724 34.9548ZM360.754 63.4003C360.754 65.4531 363.099 66.6261 367.788 66.6261C372.477 66.6261 375.213 65.3554 375.213 63.0093C375.213 60.6633 371.501 60.1746 367.983 59.2948C364.076 59.4903 360.754 60.8588 360.754 63.4003ZM373.357 44.5344C373.454 41.6996 371.207 39.2558 368.374 39.2558C368.179 39.2558 368.081 39.2558 367.886 39.2558C364.955 39.3536 362.708 41.8951 362.806 44.8277C362.903 47.7602 365.443 50.0085 368.374 49.9107C371.11 49.7152 373.357 47.3692 373.357 44.5344Z" fill="currentColor"></path>
                  <path d="M129.02 42.9703C132.927 44.2411 135.077 47.0759 135.077 50.9859C135.077 56.9487 130.29 60.4678 122.083 60.4678C114.463 60.4678 110.067 56.6555 110.067 53.3319C110.067 49.7151 114.268 49.6174 114.268 49.9106C115.245 53.2342 118.175 54.8959 121.986 54.8959C125.796 54.8959 128.238 53.2342 128.238 50.3994C128.238 47.4669 126.382 46.0984 122.669 46.0984H121.009C118.664 46.0984 117.687 45.2186 117.687 43.6546V43.2636C117.687 41.6018 118.859 40.8198 120.911 40.8198H123.256C126.186 40.8198 127.75 39.4513 127.75 37.203C127.75 34.7592 125.6 33.1952 121.986 33.1952C117.882 33.1952 115.928 35.3457 114.951 37.6918C114.951 37.985 110.848 37.8873 110.848 34.3682C110.848 31.0447 115.245 27.7212 122.083 27.7212C129.997 27.7212 134.491 30.7514 134.491 36.3233C134.393 39.6468 132.537 41.8951 129.02 42.9703Z" fill="currentColor"></path>
                  <path d="M154.03 39.8423C160.282 39.8423 164.581 43.2636 164.581 49.2264V49.5196C164.581 55.9712 159.501 60.4678 152.174 60.4678C143.576 60.4678 138.496 54.6027 138.496 45.7073V42.8726C138.496 33.4885 143.87 27.7212 152.662 27.7212C159.599 27.7212 163.311 30.6537 163.311 33.7817C163.311 36.5188 159.892 37.1053 159.794 36.9098C158.329 34.5637 155.788 33.1952 153.053 33.3907C148.07 33.3907 145.433 36.5188 145.433 42.5793V42.7748C147.875 40.9175 150.904 39.7445 154.03 39.8423ZM151.978 55.0915C155.3 55.0915 157.742 53.0387 157.742 50.0084V49.8129C157.742 46.6849 155.691 44.9253 152.076 44.9253C149.731 45.0231 147.484 46.0006 145.726 47.5646C145.921 52.2567 148.168 55.0915 151.978 55.0915Z" fill="currentColor"></path>
                  <path d="M167.805 45.7075V42.5795C167.805 33.4887 173.08 27.8191 181.58 27.8191C190.079 27.8191 195.257 33.4887 195.257 42.5795V45.6098C195.257 54.7006 190.079 60.4679 181.482 60.4679C172.885 60.4679 167.805 54.7984 167.805 45.7075ZM188.125 46.0985V42.0907C188.125 36.5189 185.781 33.4887 181.482 33.4887C177.184 33.4887 174.937 36.4212 174.937 42.0907V46.0985C174.937 51.6703 177.281 54.6029 181.482 54.6029C185.683 54.6029 188.125 51.6703 188.125 46.0985Z" fill="currentColor"></path>
                </svg>
              </div>
              <div class="swiper-slide swiper-slide-prev flex items-center justify-center" style="height: 3rem; backface-visibility: hidden; width: 248px; z-index: 3; transform: translate3d(-248px, 0px, 0px) rotateX(0deg) rotateY(-180deg); transition-duration: 0ms;">
                <svg width="100" height="30" viewBox="0 0 100 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto h-auto max-h-12 w-auto max-w-[140px] text-gray-50"><path d="M32.5 10.5H35.1L37.7 16.5L40.4 10.5H43L36.6 24.4H34L36.4 19.5L32.5 10.5ZM52.9 15.2C52.9 18 50.7 20.3 47.9 20.3C45 20.3 42.9 18.1 42.9 15.2C42.9 12.4 45.1 10.2 48 10.2C50.7 10.1 52.9 12.4 52.9 15.2V15.2ZM45.4 15.2C45.4 16.9 46.4 18.1 48 18.1C49.5 18.1 50.6 16.9 50.6 15.2C50.6 13.5 49.6 12.4 48 12.4C46.4 12.3 45.4 13.5 45.4 15.2ZM60.6 19.4C59.9 20 59.3 20.2 58.4 20.2C57.3 20.2 56.2 19.7 55.5 18.9C54.8 18.1 54.5 17.1 54.5 15.5V10.4H56.9V15.3C56.9 16.5 57 17 57.4 17.4C57.7 17.7 58.1 17.9 58.7 17.9C59.4 17.9 59.9 17.7 60.3 17.2C60.6 16.8 60.7 16.2 60.7 15V10.3H63.1V19.7H60.7V19.4H60.6ZM69.8 13.3C69.3 12.5 68.9 12.3 68.4 12.3C67.8 12.3 67.4 12.6 67.4 13.1C67.4 13.5 67.5 13.6 68.6 14L69.5 14.3C71.1 14.9 71.8 15.7 71.8 17C71.8 18.8 70.3 20.2 68.3 20.2C67.1 20.2 66 19.7 65.4 18.8C65.1 18.4 64.9 18.1 64.7 17.3L66.7 16.5C67.3 17.7 67.7 18 68.4 18C69 18 69.5 17.6 69.5 17C69.5 16.6 69.3 16.4 68.3 16.1L67.3 15.7C65.9 15.2 65.2 14.3 65.2 13.1C65.2 11.4 66.6 10.1 68.5 10.1C70 10.1 71 10.8 71.8 12.3L69.8 13.3V13.3ZM76.1 7.2C76.1 8 75.4 8.7 74.6 8.7C73.7 8.7 73.1 8 73.1 7.2C73.1 6.4 73.8 5.7 74.7 5.7C75.4 5.7 76.1 6.4 76.1 7.2V7.2ZM75.8 19.9H73.4V10.5H75.8V19.9ZM84.8 19.3C83.8 20 83.2 20.2 82.2 20.2C79.5 20.2 77.3 17.9 77.3 15.1C77.3 12.3 79.5 10.1 82.2 10.1C83.2 10.1 83.8 10.3 84.8 11V10.4H87.2V19.5C87.2 22.5 85.2 24.6 82.1 24.6C80.8 24.6 79.6 24.2 78.7 23.4C78.3 23.1 78.2 23 77.5 22.1L79.1 20.5L79.5 20.9C80.4 22 81 22.3 82.2 22.3C83.9 22.3 84.9 21.2 84.9 19.4V19.3H84.8ZM79.8 15.2C79.8 16.9 80.8 18.1 82.4 18.1C83.9 18.1 84.9 16.9 84.9 15.2C84.9 13.5 83.9 12.4 82.3 12.4C80.8 12.3 79.8 13.5 79.8 15.2V15.2ZM91.9 10.9C92.6 10.3 93.2 10.1 94.1 10.1C95.2 10.1 96.3 10.6 97 11.4C97.7 12.2 98 13.2 98 14.8V19.9H95.6V15C95.6 13.8 95.5 13.3 95.1 12.9C94.8 12.6 94.4 12.4 93.8 12.4C93.1 12.4 92.6 12.6 92.2 13.1C91.9 13.5 91.8 14.1 91.8 15.3V20H89.4V10.6H91.8V10.9H91.9ZM22 1.8C22.1 1.8 22.1 1.9 22.2 1.9L10.4 27.4H4.8C4.7 27.4 4.7 27.3 4.6 27.3L16.4 1.8H22ZM22 0.5H16.2C15.8 0.5 15.5 0.7 15.3 1L3.5 26.8C3.1 27.6 3.9 28.6 4.8 28.6H10.6C11 28.6 11.3 28.4 11.5 28.1L23.4 2.3C23.7 1.6 23 0.5 22 0.5V0.5ZM2.9 0.5H10C10.7 0.5 11.2 1.3 10.9 1.9L6 12.6C5.6 13.5 4.3 13.3 4.1 12.4L1.9 1.7C1.8 1.1 2.3 0.5 2.9 0.5Z" fill="currentColor"></path></svg>
              </div>
              <div class="swiper-slide swiper-slide-visible swiper-slide-active flex items-center justify-center" style="height: 3rem; backface-visibility: hidden; width: 248px; z-index: 4; transform: translate3d(-496px, 0px, 0px) rotateX(0deg) rotateY(0deg); transition-duration: 0ms;">
                <svg width="1456" height="813" viewBox="0 0 1456 813" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto h-auto max-h-12 w-auto max-w-[140px] text-gray-50">
                  <mask id="mask0_162_99" maskUnits="userSpaceOnUse" x="0" y="0" width="1456" height="713" style="mask-type: luminance;"><path d="M1455.92 0H0V713H1455.92V0Z" fill="white"></path></mask>
                  <g mask="url(#mask0_162_99)">
                    <path d="M1034.08 9.51422L864.844 171.193L1045.28 344.063V13.2453C1045.28 8.27058 1037.81 5.78318 1034.08 9.51422ZM578.637 171.193L409.402 9.51422C405.667 5.78318 398.201 8.27058 398.201 13.2453V344.063L578.637 171.193Z" fill="currentColor"></path>
                    <path d="M398.201 344.063H1045.28L730.45 36.8758C725.474 31.9011 718.006 31.9011 713.03 36.8758L398.201 344.063Z" fill="currentColor"></path>
                    <path d="M1264.29 622.647H1284.2C1286.69 622.647 1291.67 621.404 1291.67 615.185V594.042C1291.67 589.067 1287.93 587.825 1285.44 587.825H1276.73C1271.75 587.825 1268.02 584.092 1268.02 579.119V427.39C1268.02 423.659 1265.53 421.172 1261.8 421.172H1233.18C1229.45 421.172 1226.96 423.659 1226.96 427.39V587.825C1228.2 608.967 1238.16 622.647 1264.29 622.647ZM1138.61 478.381C1119.94 478.381 1103.76 485.843 1097.54 497.037V427.39C1097.54 423.659 1095.05 421.172 1091.32 421.172H1061.46C1057.72 421.172 1055.23 423.659 1055.23 427.39V617.672C1055.23 621.404 1057.72 623.891 1061.46 623.891H1090.08C1093.81 623.891 1096.3 621.404 1096.3 617.672V610.21C1103.76 621.404 1119.94 628.866 1137.36 628.866C1174.69 628.866 1207.05 600.262 1207.05 554.245C1208.29 506.985 1174.69 478.381 1138.61 478.381ZM1131.14 589.067C1111.23 589.067 1096.3 572.9 1096.3 553.001C1096.3 533.103 1111.23 516.935 1131.14 516.935C1151.05 516.935 1165.98 533.103 1165.98 553.001C1165.98 572.9 1151.05 589.067 1131.14 589.067ZM1026.61 480.868C1012.92 480.868 1000.48 488.331 993.014 500.768V489.573C993.014 485.843 989.281 483.356 986.793 483.356H959.415C955.682 483.356 953.194 485.843 953.194 489.573V617.672C953.194 621.404 955.682 623.891 959.415 623.891H988.036C991.77 623.891 994.259 621.404 994.259 617.672V555.488C994.259 534.347 1004.21 525.641 1021.63 525.641H1029.1C1031.59 525.641 1036.57 524.397 1036.57 518.178V489.573C1036.57 483.356 1032.83 482.112 1029.1 482.112L1026.61 480.868ZM861.11 478.381C817.556 478.381 786.447 511.96 786.447 553.001C786.447 597.773 818.801 626.378 861.11 626.378C893.463 626.378 910.885 613.941 920.841 601.504C923.329 599.018 923.329 595.287 919.596 592.798L902.174 576.631C899.686 574.144 895.953 574.144 893.463 576.631C884.753 585.337 874.798 590.312 861.11 590.312C844.933 590.312 831.244 579.119 828.756 561.707H925.817C932.04 561.707 933.284 557.976 933.284 555.488V548.026C933.284 511.96 907.152 478.381 861.11 478.381ZM831.245 536.834C834.978 520.666 847.421 511.96 862.355 511.96C876.042 511.96 889.73 519.422 893.463 536.834H831.245ZM760.315 419.928H731.694C727.961 419.928 725.473 422.415 725.473 426.147V495.793C719.251 485.843 703.074 477.137 684.407 477.137C648.321 477.137 614.723 505.741 614.723 551.757C614.723 596.531 648.321 626.378 684.407 626.378C703.074 626.378 716.762 618.916 725.473 607.723V616.429C725.473 620.16 727.961 622.647 731.694 622.647H760.315C764.048 622.647 766.537 620.16 766.537 616.429V426.147C766.537 423.659 764.048 419.928 760.315 419.928ZM691.875 589.067C671.964 589.067 657.032 572.9 657.032 553.001C657.032 533.103 671.964 516.935 691.875 516.935C711.785 516.935 726.717 533.103 726.717 553.001C726.717 572.9 710.54 589.067 691.875 589.067ZM552.504 616.429C552.504 620.16 556.237 622.647 558.726 622.647H587.347C591.08 622.647 593.568 620.16 593.568 616.429V538.078C593.568 503.254 569.925 477.137 532.594 477.137C510.195 477.137 495.263 485.843 486.552 495.793V487.087C486.552 483.356 482.819 480.868 480.331 480.868H451.709C447.976 480.868 445.487 483.356 445.487 487.087V615.185C445.487 618.916 447.976 621.404 451.709 621.404H480.331C484.064 621.404 486.552 618.916 486.552 615.185V549.27C486.552 529.372 500.24 515.691 518.905 515.691C538.816 515.691 550.015 529.372 550.015 549.27L552.504 616.429ZM352.158 477.137C309.851 477.137 276.252 509.472 276.252 551.757C276.252 592.798 311.094 626.378 352.158 626.378C394.468 626.378 428.066 594.043 428.066 551.757C429.311 510.716 394.468 477.137 352.158 477.137ZM352.158 587.825C332.249 587.825 317.317 571.657 317.317 551.757C317.317 531.859 332.249 515.691 352.158 515.691C372.07 515.691 387.002 531.859 387.002 551.757C387.002 571.657 372.07 587.825 352.158 587.825ZM1453.43 492.062C1455.92 485.843 1453.43 482.112 1448.46 482.112H1418.59C1413.61 482.112 1412.37 485.843 1411.13 488.331L1378.77 571.657L1342.68 488.331C1340.2 483.356 1338.95 482.112 1335.22 482.112H1302.86C1295.4 482.112 1294.15 487.087 1296.64 492.062L1351.4 608.967L1357.62 622.647L1342.68 654.982C1338.95 662.445 1333.97 668.663 1322.77 668.663H1319.04C1316.55 668.663 1312.82 669.907 1312.82 674.882V697.267C1312.82 703.487 1317.8 704.731 1320.29 704.731H1325.26C1353.88 704.731 1368.82 688.562 1380.02 661.201L1453.43 492.062ZM277.496 419.928H245.142C241.409 419.928 238.921 423.659 238.921 426.147L206.566 569.169L171.724 454.751C170.48 449.776 165.502 446.046 159.281 446.046H129.415C123.193 446.046 118.216 449.776 116.971 454.751L82.1288 570.413L49.7748 427.39C48.5305 423.659 46.0417 421.172 43.5529 421.172H8.71036C3.73285 421.172 -0.000269019 424.903 1.24411 431.121L48.5306 616.429C49.775 621.404 53.5081 622.647 55.9969 622.647H104.528C107.016 622.647 110.749 621.404 111.994 616.429L144.348 510.716L176.702 616.429C177.946 621.404 181.679 622.647 184.168 622.647H230.21C231.454 622.647 237.676 621.404 237.676 616.429L283.718 431.121C286.207 422.415 281.229 419.928 277.496 419.928Z" fill="currentColor"></path>
                  </g>
                </svg>
              </div>
              <div class="swiper-slide swiper-slide-next flex items-center justify-center" style="height: 3rem; backface-visibility: hidden; width: 248px; z-index: 3; transform: translate3d(-744px, 0px, 0px) rotateX(0deg) rotateY(180deg); transition-duration: 0ms;">
                <svg width="281" height="59" viewBox="0 0 281 59" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto h-auto max-h-12 w-auto max-w-[140px] text-gray-50">
                  <g clip-path="url(#clip0_129_3932)">
                    <path d="M79.9483 14.3328H75.3359V6.70964C75.3359 5.91556 74.8234 5.39941 74.1138 5.39941H68.989C68.2399 5.39941 67.7669 5.91556 67.7669 6.70964V14.2931H64.14C63.391 14.2931 62.918 14.8092 62.918 15.6033V19.137C62.918 19.931 63.4305 20.4472 64.14 20.4472H67.7669V37.7581C67.7669 43.1181 70.7629 46.0959 76.1638 46.0959C76.9916 46.0959 81.1703 46.0165 81.1703 44.1107V40.7756C81.1703 40.3786 81.0521 40.0609 80.8156 39.8624C80.5396 39.6242 80.1454 39.5051 79.6723 39.5448C79.0021 39.6242 78.2531 39.6639 77.7406 39.6639C76.0849 39.6639 75.3359 38.8301 75.3359 37.0434V20.4869H79.9483C80.6973 20.4869 81.2492 19.9707 81.2492 19.1767V15.643C81.2098 14.8489 80.6973 14.3328 79.9483 14.3328Z" fill="currentColor"></path>
                    <path d="M98.0815 12.9033C88.8173 12.9033 82.0762 19.8515 82.0762 29.3804C82.0762 39.227 89.0539 46.1355 99.0276 46.1355C103.64 46.1355 107.622 44.7061 110.263 42.1254C110.697 41.6886 111.13 41.0534 110.539 40.2593L108.804 37.8771C108.449 37.3212 107.858 37.2418 107.306 37.5991C104.902 39.227 102.497 39.9417 99.5795 39.9417C94.021 39.9417 90.3154 37.2021 89.4481 32.3979H109.79C112.471 32.3979 113.022 30.651 113.022 27.9908C112.983 20.4868 107.858 12.9033 98.0815 12.9033ZM98.0815 19.3354C102.694 19.3354 105.769 22.0749 106.321 26.7203H89.3692C90.0788 22.194 93.3903 19.3354 98.0815 19.3354Z" fill="currentColor"></path>
                    <path d="M131.117 29.7781L139.948 15.9612C140.342 15.4053 140.224 14.9686 140.145 14.7701C140.027 14.5715 139.79 14.2539 139.12 14.2539H133.64C133.01 14.2539 132.497 14.5318 132.221 15.048L126.623 24.2593L120.946 15.0877C120.631 14.5318 120.197 14.2936 119.567 14.2936H114.048C113.614 14.2936 113.259 14.4524 113.062 14.7701C112.865 15.0877 112.904 15.5244 113.18 15.9612L122.09 29.7781L112.786 44.3495C112.392 44.9053 112.51 45.3421 112.589 45.5406C112.707 45.7391 112.944 46.0567 113.614 46.0567H119.094C119.724 46.0567 120.237 45.7788 120.513 45.2627L126.544 35.3764L132.576 45.2627C132.891 45.8185 133.325 46.0567 133.956 46.0567H139.475C139.948 46.0567 140.303 45.8582 140.5 45.5406C140.657 45.2229 140.618 44.7862 140.342 44.3892L131.117 29.7781Z" fill="currentColor"></path>
                    <path d="M159.541 14.2934H154.928V6.74968C154.928 5.9556 154.416 5.43945 153.706 5.43945H148.621C147.872 5.43945 147.399 5.9556 147.399 6.74968V14.2934H143.772C143.023 14.2934 142.55 14.8096 142.55 15.6037V19.1373C142.55 19.9314 143.062 20.4475 143.772 20.4475H147.399V37.7981C147.399 43.1582 150.395 46.136 155.796 46.136C156.623 46.136 160.802 46.0565 160.802 44.1508V40.8156C160.802 40.4186 160.684 40.101 160.447 39.9024C160.171 39.6642 159.777 39.5451 159.304 39.5848C158.634 39.6642 157.885 39.7039 157.372 39.7039C155.717 39.7039 154.968 38.8701 154.968 37.0835V20.4475H159.58C160.329 20.4475 160.881 19.9314 160.881 19.1373V15.6037C160.842 14.8096 160.329 14.2934 159.541 14.2934Z" fill="currentColor"></path>
                    <path d="M181.026 13.4993C176.216 13.4993 173.378 15.8419 171.762 18.1447V1.31023C171.762 0.516151 171.249 0 170.54 0H165.494C164.745 0 164.271 0.516151 164.271 1.31023V44.6669C164.271 45.461 164.784 45.9771 165.494 45.9771H170.54C171.289 45.9771 171.762 45.461 171.762 44.6669V27.8324C171.762 23.3062 174.442 20.5269 178.779 20.5269C182.879 20.5269 185.165 23.1077 185.165 27.8324V44.6669C185.165 45.461 185.678 45.9771 186.387 45.9771H191.354C192.103 45.9771 192.655 45.461 192.655 44.6669V26.5619C192.616 18.502 188.201 13.4993 181.026 13.4993Z" fill="currentColor"></path>
                    <path d="M211.893 12.9033C202.629 12.9033 195.888 19.8515 195.888 29.3804C195.888 39.227 202.865 46.1355 212.839 46.1355C217.452 46.1355 221.433 44.7061 224.074 42.1254C224.508 41.6886 224.942 41.0534 224.35 40.2593L222.616 37.8771C222.261 37.3212 221.67 37.2418 221.118 37.5991C218.713 39.227 216.308 39.9417 213.391 39.9417C207.833 39.9417 204.127 37.2021 203.26 32.3979H223.601C226.282 32.3979 226.834 30.651 226.834 27.9908C226.834 20.4868 221.709 12.9033 211.893 12.9033ZM211.893 19.3354C216.505 19.3354 219.58 22.0749 220.132 26.7203H203.181C203.89 22.194 207.202 19.3354 211.893 19.3354Z" fill="currentColor"></path>
                    <path d="M236.69 0H231.644C230.895 0 230.422 0.516151 230.422 1.31023V44.6669C230.422 45.461 230.934 45.9771 231.644 45.9771H236.69C237.439 45.9771 237.912 45.461 237.912 44.6669V1.31023C237.912 0.516151 237.439 0 236.69 0Z" fill="currentColor"></path>
                    <path d="M260.658 12.9033C256.164 12.9033 252.537 14.69 250.093 18.0251L250.014 14.9679C250.014 14.1738 249.541 13.6577 248.792 13.6577H244.219C243.47 13.6577 242.997 14.1738 242.997 14.9679V56.1012C242.997 56.8952 243.51 57.4114 244.219 57.4114H249.265C250.014 57.4114 250.487 56.8952 250.487 56.1012V41.4901C252.853 44.5473 256.361 46.1355 260.698 46.1355C269.489 46.1355 275.875 39.1476 275.875 29.4996C275.875 19.8515 269.449 12.9033 260.658 12.9033ZM259.357 19.8515C264.758 19.8515 268.543 23.8219 268.543 29.4996C268.543 35.1772 264.758 39.1476 259.357 39.1476C253.996 39.1476 250.408 35.2963 250.408 29.579C250.408 23.7425 253.996 19.8515 259.357 19.8515Z" fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.06706 5.39941H50.1842C52.6678 5.39941 53.2985 6.86846 51.564 8.57573L43.6402 16.318V36.8846C43.6402 41.927 39.5797 46.0165 34.5731 46.0165H27.4771V56.3792C27.4771 57.8086 26.2945 58.9997 24.8753 58.9997C23.4561 58.9997 22.2734 57.8086 22.2734 56.3792V46.0165H19.7898V56.3792C19.7898 57.8086 18.6072 58.9997 17.188 58.9997C15.7688 58.9997 14.5861 57.8086 14.5861 56.3792V46.0165H9.06706C4.06047 46.0165 0 41.927 0 36.8846V14.6504C0 9.56832 4.02104 5.47882 9.06706 5.39941ZM33.2158 13.6904C33.6637 14.1375 34.2814 14.4122 34.9673 14.4122C36.3471 14.4122 37.4509 13.3005 37.4509 11.9109C37.4509 11.2635 37.2114 10.6765 36.816 10.2341C36.7605 10.1719 36.7019 10.1127 36.6404 10.0564C36.2002 9.65359 35.6139 9.40918 34.967 9.40918C33.5872 9.40918 32.4834 10.5209 32.4834 11.9105C32.4834 12.6095 32.7627 13.2382 33.2158 13.6904Z" fill="currentColor"></path>
                    <path d="M281 7.90141C281 10.7998 278.713 13.1026 275.875 13.1026C273.036 13.1026 270.75 10.7998 270.75 7.90141C270.75 5.00302 273.036 2.7002 275.875 2.7002C278.713 2.7002 281 5.00302 281 7.90141ZM279.778 7.90141C279.778 5.67799 278.082 3.93102 275.875 3.93102C273.707 3.93102 272.012 5.67799 272.012 7.90141C272.012 10.1248 273.707 11.8718 275.875 11.8718C278.082 11.8718 279.778 10.1248 279.778 7.90141ZM278.319 10.1645C278.398 10.2439 278.358 10.363 278.24 10.363H277.333C277.255 10.363 277.176 10.3233 277.136 10.2836L275.796 8.77489H275.008V10.2042C275.008 10.2836 274.929 10.363 274.85 10.363H274.061C273.983 10.363 273.904 10.2836 273.904 10.2042V5.55888C273.904 5.47947 273.943 5.40006 274.061 5.40006H276.309C277.491 5.40006 278.319 5.95591 278.319 7.06762C278.319 7.94111 277.807 8.49696 276.979 8.65578L278.319 10.1645ZM275.008 7.8617H276.348C276.939 7.8617 277.215 7.54407 277.215 7.10733C277.215 6.63088 276.939 6.35295 276.348 6.35295H275.008V7.8617Z" fill="currentColor"></path>
                  </g>
                  <defs>
                    <clipPath id="clip0_129_3932"><rect width="281" height="59" fill="currentColor"></rect></clipPath>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



@endsection