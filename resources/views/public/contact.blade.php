@extends('layouts.public')
@section('content')


@php
    $title = 'Contact - PressBear';
@endphp


<div class="isolate bg-white px-6 py-10">
  <div class="absolute inset-x-0 top-[-10rem] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[-20rem]" aria-hidden="true">
    <div class="relative left-1/2 -z-10 aspect-[1155/678] w-[36.125rem] max-w-none -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-40rem)] sm:w-[72.1875rem]" style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"></div>
  </div>
  <div class="mx-auto max-w-2xl text-center">
    <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Get in Touch</h2>
    <p class="mt-2 text-lg leading-8 text-gray-600">Need any help? Feel free to get in touch.</p>
  </div>
  <form id="form-submission" method="POST" class="mx-auto mt-16 max-w-xl sm:mt-20">
    <div class="grid grid-cols-1 gap-x-8 gap-y-6 sm:grid-cols-2">
       <input type="hidden" name="access_key" value="e238e72e-aa72-48a4-b715-4a7facba7d84">
     <div class="sm:col-span-2">
        <label for="company" class="block text-sm font-semibold leading-6 text-gray-900">
          Name
          <span class="text-xs text-red-700 -mt-6" title="required">*</span>
        </label>
        
        <div class="mt-2.5">
          <input type="text" name="name" id="name" autocomplete="name" required class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
        </div>
      </div>
      <div class="sm:col-span-2">
        <label for="email" class="block text-sm font-semibold leading-6 text-gray-900">
            Email
            <span class="text-xs text-red-700 -mt-6" title="required">*</span>
        </label>
        <div class="mt-2.5">
          <input type="email" name="email" id="email" autocomplete="email" required class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
        </div>
      </div>
      <div class="sm:col-span-2">
        <label for="company" class="block text-sm font-semibold leading-6 text-gray-900">Company</label>
        <div class="mt-2.5">
          <input type="text" name="company" id="company" autocomplete="organization" class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
        </div>
      </div>
      <div class="sm:col-span-2">
        <label for="message" class="block text-sm font-semibold leading-6 text-gray-900" required>
            Message
            <span class="text-xs text-red-700 -mt-6" title="required">*</span>
        </label>
        <div class="mt-2.5">
          <textarea name="message" id="message" rows="4" class="block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
        </div>
      </div>
    </div>
    <input type="checkbox" name="botcheck" class="hidden" style="display: none;">
    <div class="mt-10">
      <button type="submit" class="block w-full rounded-md bg-indigo-600 px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Let's talk</button>
    </div>

     <div id="result" class="text-emerald-700 pt-8 flex"></div>

  </form>

   

  <script>
const form = document.getElementById('form-submission');
const result = document.getElementById('result');

form.addEventListener('submit', function(e) {
  e.preventDefault();
  const formData = new FormData(form);
  const object = Object.fromEntries(formData);
  const json = JSON.stringify(object);
  result.innerHTML = "Please wait..."

    fetch('https://api.web3forms.com/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: json
        })
        .then(async (response) => {
            let json = await response.json();
            if (response.status == 200) {
                result.innerHTML = "Form submitted successfully";
            } else {
                console.log(response);
                result.innerHTML = json.message;
            }
        })
        .catch(error => {
            console.log(error);
            result.innerHTML = "Something went wrong!";
        })
        .then(function() {
            form.reset();
            setTimeout(() => {
                result.style.display = "none";
            }, 3000);
        });
});
</script>

   
</div>

<div class="bg-white py-24 sm:py-32">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl space-y-16 divide-y divide-gray-100 lg:mx-0 lg:max-w-none">
      <div class="grid grid-cols-1 gap-x-8 gap-y-10 lg:grid-cols-3">
        <div>
          <h2 class="text-3xl font-bold tracking-tight text-gray-900">Get in touch</h2>
          <p class="mt-4 leading-7 text-gray-600">You can also reachout us via email.</p>
        </div>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:col-span-2 lg:gap-8">
          <div class="rounded-2xl bg-gray-50 p-10">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Support</h3>
            <dl class="mt-3 space-y-1 text-sm leading-6 text-gray-600">
              <div>
                <dt class="sr-only">Email</dt>
                <dd><a class="font-semibold text-indigo-600" href="mailto:<EMAIL>"><EMAIL></a></dd>
              </div>
              <div class="mt-1">
                <dt class="sr-only">Phone number</dt>
                <dd>+****************</dd>
              </div>
            </dl>
          </div>
          <div class="rounded-2xl bg-gray-50 p-10">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Sales</h3>
            <dl class="mt-3 space-y-1 text-sm leading-6 text-gray-600">
              <div>
                <dt class="sr-only">Email</dt>
                <dd><a class="font-semibold text-indigo-600" href="mailto:<EMAIL>"><EMAIL></a></dd>
              </div>
              <div class="mt-1">
                <dt class="sr-only">Phone number</dt>
                <dd>+****************</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-x-8 gap-y-10 pt-16 lg:grid-cols-3">
        <div>
          <h2 class="text-3xl font-bold tracking-tight text-gray-900">Locations</h2>
          <p class="mt-4 leading-7 text-gray-600">Our office locations.</p>
        </div>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:col-span-2 lg:gap-8">
          <div class="rounded-2xl bg-gray-50 p-10">
            <h3 class="text-base font-semibold leading-7 text-gray-900">San Francisco</h3>
            <address class="mt-3 space-y-1 text-sm not-italic leading-6 text-gray-600">
              <p>8 Octavia street</p>
              <p>San Francisco, CA, US - 94102</p>
            </address>
          </div>
          <div class="rounded-2xl bg-gray-50 p-10">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Dubai</h3>
            <address class="mt-3 space-y-1 text-sm not-italic leading-6 text-gray-600">
              <p>One Central 8th Floor - Trade Centre</p>
              <p>Dubai - United Arab Emirates</p>
            </address>
          </div>
         {{--  <div class="rounded-2xl bg-gray-50 p-10">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Toronto</h3>
            <address class="mt-3 space-y-1 text-sm not-italic leading-6 text-gray-600">
              <p>7363 Cynthia Pass</p>
              <p>Toronto, ON N3Y 4H8</p>
            </address>
          </div>
          <div class="rounded-2xl bg-gray-50 p-10">
            <h3 class="text-base font-semibold leading-7 text-gray-900">Chicago</h3>
            <address class="mt-3 space-y-1 text-sm not-italic leading-6 text-gray-600">
              <p>726 Mavis Island</p>
              <p>Chicago, IL 60601</p>
            </address>
          </div> --}}
        </div>
      </div>
    </div>
  </div>
</div>


@endsection