<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credit Note</title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet">

    <style>
        body {
            font-family: Inter, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            line-height: 140%;
        }

        .credit-note-container {
            max-width: 900px;
            margin: auto;
            background-color: #fff;
            padding: 20px 25px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header,
        .billing,
        .items,
        .total,
        .terms {
            margin-bottom: 20px;
        }

        .header h2,
        .billing h2,
        .items h2 {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .header div,
        .billing div,
        .items div {
            margin-bottom: 10px;
        }

        .credit-note-number {
            text-align: right;
        }

        .amount {
            font-size: 26px;
            font-weight: 600;
            color: #333;
            text-align: right;
        }

        .refunded-status {
            display: inline-block;
            background-color: #f97316;
            color: #fff;
            font-weight: bold;
            padding: 5px 10px;
            font-size: 16px;
            border-radius: 5px;
            margin-top: 10px;
            text-transform: capitalize;
        }

        .billing,
        .items {
            border-top: 1px solid #ddd;
            padding-top: 30px;
            margin-top: 10px;
        }

        .billing h2,
        .items h2 {
            margin-top: 0;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .items-table th,
        .items-table td {
            text-align: left;
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }

        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .total {
            text-align: right;
        }

        .terms p {
            font-size: 13px;
            color: #888;
        }

        .font-one {
            font-size: 15px;
        }
    </style>
</head>

<body>
    <div class="credit-note-container">
        <div style="display: flex; align-items: center; margin-bottom: 20px;">
            <x-icons.etc.bear />
            <h2 style="font-size:22px;">Pressbear</h2>
        </div>

        <div class="header" style="display: flex; justify-content: space-between;">
            <div class="font-one">
                <div>Softsynk LLC</div>
                <div id="our-address" class="">
                    8 Octavia St
                    <br>
                    San Francisco, CA 94102
                    <br>
                    United States
                </div>
                <div><EMAIL></div>
            </div>

            <div class="credit-note-number font-one" style="margin-top: 20px;">
                <div>
                    Credit Note #{{ $orderItem->created_at->format('my') }}-{{ $orderItem->id }}
                    <br>
                    Date: {{ $orderItem->created_at->format('M d, Y') }}
                </div>
                <span class="refunded-status">Refunded</span>
            </div>
        </div>

        <div class="amount">
            <div>${{ $orderItem->price_paid }}</div>
            <div class="refunded-status">Refunded to Wallet</div>
        </div>

        <div class="billing font-one">
            <h2>Bill to</h2>
            <div class="">{{ $user->company ?? $user->name }}</div>
            <div id="customer-address">
                {{ $user->address_data['address'] ?? '' }}<br>
                {{ $user->address_data['city'] ?? '' }}<br>
                {{ $user->address_data['postal_code'] ?? '' }}<br>
                {{ $user->country->name ?? '' }}
            </div>
            <div>{{ $user->email ?? '' }}</div>
        </div>

        <div class="items">
            <h2 style="padding-bottom: 4px;">Refunded Item</h2>
            <table class="items-table">
                <tr>
                    <th>Media Publication</th>
                    <th>Amount</th>
                </tr>
                <tr>
                    <td>
                        <span>{{ $orderItem->website->website_domain }}</span>
                        <br>
                        <small style="color: #666;">Order Item #{{ $orderItem->id }}</small>
                    </td>
                    <td>${{ $orderItem->price_paid }}</td>
                </tr>
            </table>
        </div>

        <div class="total">
            <div>Refund Amount: ${{ $orderItem->price_paid }}</div>
        </div>

        <div class="terms" style="margin-top: 40px;">
            <p>This credit note serves as proof of refund to your wallet. The amount has been credited to your Pressbear
                wallet balance.</p>
            <p>Terms and conditions: https://pressbear.com/terms-of-service</p>
        </div>
    </div>
</body>

</html>