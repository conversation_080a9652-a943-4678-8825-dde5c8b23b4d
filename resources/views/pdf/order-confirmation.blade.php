<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Order Confirmation</title>
    <style>
        /* Tailwind-like utility classes for PDF */
        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-base {
            font-size: 1rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .font-bold {
            font-weight: 700;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-gray-600 {
            color: #4B5563;
        }

        .text-gray-700 {
            color: #374151;
        }

        .text-gray-800 {
            color: #1F2937;
        }

        .text-gray-900 {
            color: #111827;
        }

        .bg-gray-50 {
            background-color: #F9FAFB;
        }

        .border {
            border: 1px solid #E5E7EB;
        }

        .border-collapse {
            border-collapse: collapse;
        }

        .p-4 {
            padding: 1rem;
        }

        .p-6 {
            padding: 1.5rem;
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .py-2 {
            padding-top: 0.5rem;
            padding-bottom: 0.5rem;
        }

        .py-4 {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .my-4 {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .my-6 {
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .my-8 {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .mt-6 {
            margin-top: 1.5rem;
        }

        .mt-8 {
            margin-top: 2rem;
        }

        .w-full {
            width: 100%;
        }

        .space-y-4>*+* {
            margin-top: 1rem;
        }

        .space-y-6>*+* {
            margin-top: 1.5rem;
        }
    </style>
</head>

<body class="text-base text-gray-800">
    <div class="text-center my-8">
        <h1 class="text-2xl font-bold text-gray-900">Order Confirmation</h1>
    </div>

    <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Pressbear</h2>
        <p class="text-gray-700">
            8 Octavia St<br>
            San Francisco, CA 94102<br>
            United States<br>
            <EMAIL>
        </p>
    </div>

    <div class="mb-6 space-y-4">
        <p><span class="font-semibold">Order Number:</span> #{{ $order->id }}</p>
        <p><span class="font-semibold">Date:</span> {{ $order->created_at->format('F d, Y') }}</p>
        <p><span class="font-semibold">Customer:</span> {{ $user->name }}</p>
        <p><span class="font-semibold">Email:</span> {{ $user->email }}</p>
    </div>

    <table class="w-full border-collapse mb-6">
        <thead>
            <tr class="bg-gray-50">
                <th class="border p-4 text-left font-semibold text-gray-700">Website</th>
                <th class="border p-4 text-left font-semibold text-gray-700">Niche</th>
                <th class="border p-4 text-left font-semibold text-gray-700">Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($items as $item)
            <tr>
                <td class="border p-4">{{ $item->website->website_domain }}</td>
                <td class="border p-4">{{ $item->niche ?? 'N/A' }}</td>
                <td class="border p-4">${{ number_format($item->price_paid, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="text-right mt-6">
        <p class="text-lg font-bold">Total Amount: ${{ number_format($order->price_paid, 2) }}</p>
    </div>

    <div class="text-center mt-8 text-sm text-gray-600">
        <p class="mb-4">Thank you for choosing Pressbear!</p>
        <p>If you have any questions about your order, please contact our support <NAME_EMAIL></p>
    </div>
</body>

</html>