<x-app-layout>
    <x-app.header :cartItems="$cartItems" />


    {{-- Main Body Wrapper --}}
    <div id="app-body" class="flex justify-center flex-col max-w-7xl mx-auto">



        {{-- STEPS --}}
        <div class="steps w-full py-12 hidden md:block">
            <x-marketplace.cart.steps :steps="2" />
        </div>



        {{-- BODY --}}
        @fragment('address-content')
        <div id="content-body">


            <form id="form-billing" method="POST" action="{{ route('data-process') }}">


                <div id="billing-page-main-wrapper" class="justify-items-end bg-gray-50 my-0 h-auto rounded-lg mx-auto
                 w-full px-6 py-6 lg:py-12 sm:px-10 lg:max-w-screen-2xl lg:px-12 
                 lg:grid lg:grid-cols-2  gap-x-16 ">




                    <!------------------------------->
                    <!-- Billing Section Left Side -->
                    <!------------------------------->
                    <!-- 1. Billing Address Form   -->
                    <!-- 2. Stripe Form            -->
                    <!------------------------------->
                    <div id="billing-section-left" class="w-full">
                        <h2 class="sr-only">Checkout</h2>


                        {{-- 1. Billing Address Form --}}
                        <div id="billing-form" class=" ">


                            <h2 class="flex items-center text-lg font-medium text-gray-900 pb-4">
                                <x-icons.lucide.building width="20" height="20" class="mr-2" />
                                Billing Details
                            </h2>


                            <div id="billing-address-input"
                                class="mt-4 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4">
                                @CSRF
                                {{-- Show Errors --}}
                                @if ($errors->any())
                                <div class="alert alert-danger text-sm text-red-600">
                                    <ul>
                                        @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                                @endif


                                <input type="hidden" name="data" value="address">


                                {{-- ADDRESS --}}
                                <div id="address-input" class="sm:col-span-2">
                                    <label for="address" class="block text-sm font-medium text-gray-700">Address
                                        *</label>
                                    <div class="mt-1">
                                        <input type="text" {{-- x-model="$store.data.address" --}}
                                            value="{{ old('address') ?? $userData->address_data['address'] ?? '' }}"
                                            name="address" id="address" placeholder="Billing address"
                                            autocomplete="street-address" class="block w-full rounded-md border-gray-300 shadow-sm
                               focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                                    </div>
                                </div>

                                {{-- COMPANY --}}
                                <div id="company-name">
                                    <label for="company" class="block text-sm font-medium text-gray-700">
                                        Business Name
                                    </label>
                                    <div class="mt-1">
                                        <input
                                                type="text"
                                                name="company"
                                                id="company"
                                                value="{{ old('company') ?? $userData->company }}"
                                                placeholder="Your company name"
                                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                pattern="^[A-Za-z0-9 ]+$"
                                                title="Only letters, numbers, and spaces allowed"
                                        >
                                    </div>
                                </div>

                                {{-- POSTAL CODE --}}
                                <div id="postal-code">
                                    <label for="postal_code" class="block text-sm font-medium text-gray-700">
                                        Postal Code *
                                    </label>
                                    <div class="mt-1">
                                        <input
                                                type="text"
                                                name="postal_code"
                                                id="postal_code"
                                                value="{{ old('postal_code') ?? $userData->address_data['postal_code'] ?? '' }}"
                                                placeholder="e.g. 75500"
                                                autocomplete="postal-code"
                                                required
                                                pattern="^[0-9]{5}$"
                                                title="Please enter a 5-digit postal code (e.g. 12345)"
                                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                        >
                                    </div>
                                </div>

                                {{-- CITY --}}
                                <div id="city-input">
                                    <label for="city" class="block text-sm font-medium text-gray-700">City *</label>
                                    <div class="mt-1">
                                        <input type="text" {{-- x-model="$store.data.city" --}}
                                            value="{{ old('city') ?? $userData->address_data['city'] ?? '' }}"
                                            name="city" id="city" placeholder="City" autocomplete="address-level2"
                                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            required pattern="^[a-z A-Z]+$">
                                    </div>
                                </div>


                                {{-- COUNTRY --}}
                                <div id="country-input">
                                    <label for="country" class="block text-sm font-medium text-gray-700">Country
                                        *</label>
                                    <div class="mt-1">
                                        <select id="country" name="country_id"
                                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            required>
                                            @foreach ($countriesList as $country)
                                            <option value="{{ $country->id }}" @selected($userData->country_id ==
                                                $country->id)>
                                                {{ $country->name }}
                                            </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>


                                {{-- ORDER MEMO --}}
                                <div id="memo-input" class="sm:col-span-2 mt-4">
                                    <label for="memo" class="block text-sm font-medium text-gray-700">Order Memo</label>
                                    <div class="mt-1">
                                        <textarea x-model="$store.data.OrderMemo" name="order_memo" id="memo"
                                            placeholder="For your personal reference shown on order page." rows="4"
                                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">{{ old('order_memo') ?? session('order_memo') ?? '' }}</textarea>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>








                    <!------------------------------->
                    <!--  Order summary Right Side -->
                    <!------------------------------->
                    <div id="order-summary" class="mt-10 lg:mt-0  w-full lg:pl-6 max-w-[800px]">


                        <div id="order-summary-data" class="mt-4 rounded-lg border border-gray-200 bg-white shadow-sm">

                            <h2 class="flex pl-6 pt-6 items-center text-lg font-medium text-gray-900 pb-4">
                                <x-icons.lucide.scroll width="20" height="20" class="mr-2" />
                                Order Summary
                            </h2>


                            {{-- summary info --}}
                            <ul role="list" class="divide-y divide-gray-200">
                                <li class="flex  sm:px-6">
                                    <div class="ml-6 my-4 flex flex-1 flex-col">
                                        <div id="cart-data" class="flex">
                                            <div class="min-w-0 flex-1">
                                                <h4 class="text-sm">
                                                    <a href="#" class="font-bold text-gray-700 hover:text-gray-800">
                                                        Sponsorship
                                                    </a>
                                                </h4>
                                                <p class="mt-1 text-sm text-gray-500 flex items-center">
                                                    <x-icons.lucide.check class="w-4 h-4 pr-1" />
                                                    {{ $cartStats['count'] }} Publications
                                                </p>
                                                <p class="mt-1 text-sm text-gray-500 flex items-center">
                                                    <x-icons.lucide.check class="w-4 h-4 pr-1" />
                                                    1000 words article included
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>

                            <dl id="pricing-data-cart" class="border-t border-gray-200">
                                <div class="flex items-center justify-between border-gray-200 py-6 px-10">
                                    <dt class="text-base font-medium">Total</dt>
                                    <dd class="text-base font-medium text-gray-900">${{ $cartStats['totalPrice'] }}</dd>
                                </div>
                            </dl>


                            {{-- ACTION BUTTON: proceed to payment --}}
                            {{-- @click.once="spinning = !spinning" --}}
                            <div x-data="{ spinning: false }" id="payment-process-button"
                                class="border-t border-gray-200 px-4 py-6 sm:px-6">

                                <button id="submit-button" type="submit"
                                    class="flex justify-center w-full rounded-md border border-transparent bg-emerald-500 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-50 disabled:bg-gray-500 h-12">

                                    <div x-cloak x-show="spinning">
                                        <span class="spinner" id="spinner">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4">
                                                </circle>
                                                <path class="opacity-75" fill="currentColor"
                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                                </path>
                                            </svg>
                                        </span>
                                    </div>
                                    <span id="payment-button-text">Proceed to Payment</span>
                                </button>
                            </div>

                        </div>

                    </div>
                </div>

            </form>

        </div>
        @endfragment



        {{-- Body Main Wrapper End --}}
    </div>
</x-app-layout>