<x-app-layout>
  <x-app.header />


  <div id="app-body" class="flex min-h-screen py-2 px-0 md:py-4 md:px-8 bg-gray-100">


    {{-- Main body --}}
    <div id="inner-body" class="flex flex-col w-full ">

      <div id="body-wrapper" class="my-2 sm:my-8 mx-2 sm:mx-8">
        <div id="intro-section" class="ps-4 sm:ps-0 my-5 text-slate-700 text-xl font-bold tracking-wide">
          Your Orders
        </div>


        {{-- Filter Buttons --}}
        {{--
        <x-marketplace.order.filter-orders-history /> --}}


        @if(count($orders) > 0)

        @foreach($orders as $order)

        <div class="single-order w-full border rounded-md shadow-sm bg-white py-4 px-6 md:py-8 md:px-10 my-6">
          <div class=" md:justify-between top-bar-so grid grid-cols-2 lg:grid-cols-6 space-y-6 lg:flex lg:space-y-0">

            <div class="flex flex-col justify-center">
              <div class="text-xs font-medium text-gray-500 mb-2">Order ID</div>
              <div class="text-xl md:text-2xl text-emerald-900 font-bold">
                <a href="{{route('advertiser.order-details', $order->id)}}">
                  #{{ $order->id }}
                </a>
              </div>
            </div>


            <div class="row-info-so text-sm flex flex-col justify-center">
              <div class="text-xs font-medium text-gray-500 mb-2">Order Status:</div>
              {{-- Order Status --}}
              <div>
                @php
                $statusClass = match($order->status) {
                'delivered' => 'bg-green-100 text-green-800',
                'inprogress' => 'bg-yellow-100 text-yellow-800',
                'pending' => 'bg-yellow-100 text-gray-800',
                'cancelled' => 'bg-red-100 text-red-800',
                default => 'bg-gray-100 text-gray-800',
                };
                @endphp

                <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $statusClass }}">
                  {{ ucfirst($order->status) }}
                </span>

              </div>
            </div>

            <div class="row-info-so text-sm flex flex-col justify-center">
              <div class="text-xs font-medium text-gray-500 mb-2">Total Items:</div>
              <div class="font-semibold">{{$order->items_in_orders}} publications</div>
            </div>

            <div class="row-info-so text-sm flex flex-col justify-center">
              <div class="text-xs font-medium text-gray-500 mb-2">Date Ordered:</div>
              <div class="font-semibold">{{ date_format($order->created_at, 'd M Y') }}</div>
            </div>

            <div class="flex flex-col justify-center">
              <div class="text-xs font-medium text-gray-500 mb-2">Price Paid</div>
              <div class="text-xl font-bold text-orange-600">${{ $order->price_paid }}</div>
            </div>

            <div class="flex flex-col justify-center lg:items-end">
              <a href="{{route('advertiser.order-details', $order->id)}}" wire:navigate class="">
                <button
                  class="whitespace-nowrap hover:bg-emerald-700 bg-emerald-600 cursor-pointer font-semibold text-white text-sm rounded-md py-2 px-4">
                  Order Page
                </button>
              </a>
            </div>

          </div>

        </div>
        @endforeach


        {{-- Empty State --}}
        @else
        <div id="no-order-items"
          class="flex flex-col justify-center items-center w-full border rounded-md shadow-sm bg-white py-8 px-10 my-6">

          <img class="w-1/2 max-w-2xl" src="{{ Storage::url('graphics/illustrations/empty-cart.png') }}">

          <div class="mb-8 text-xl font-medium text-gray-900">
            No Orders Yet
          </div>

          <a href="{{ route('marketplace') }}" class="mb-8" wire:navigate>
            <button
              class="whitespace-nowrap border-2 bg-emerald-700 text-white text-sm font-medium px-4 py-2 rounded-lg hover:bg-emerald-800">
              Go To Marketplace
            </button>
          </a>

        </div>
        @endif

        {{-- Pagination Links --}}
        <div class="mt-8">
          {{ $orders->links() }}
        </div>

        <!-- End Body Wrapper Div Below -->
      </div>




    </div>

  </div>

</x-app-layout>