<x-app-layout>
	<x-app.header :cartItems="$cartItems" />


	{{-- **** --}}
	{{-- Body --}}
	<div class="app-body">
		<div id="inner-body" class="py-2 px-2  mt-4 flex flex-col overflow-x-scroll ">


			{{-- STATS BLOCK --}}
			{{--
			<x-marketplace.table.top-stats :websites="$websites" :cartItems="$cartItems" /> --}}


			{{-- Filters --}}
			<div class="w-full">
				<x-marketplace.table.filters :filtersData="$filtersData" />
			</div>


			{{-- Market Table --}}
			@fragment('marketTable')
			<div id="marketplace-main-table" class="w-full" x-data>
				<x-marketplace.table.market-table :websites="$websites" :cartItems="$cartItems"
					:cartWebsitesids="$cartWebsitesids" :cartData="$cartData" />
			</div>
			@endfragment

		</div>
	</div>



	{{-- ************ --}}
	{{-- Dynamic URLS --}}
	<script type="text/javascript">
		// JS Global variables containng urls for dynamic table updates
    let tableUpdateURL         = '{{ route('market-table') }}';
    let fragmentCartPreviewURL = '{{ route('fragment-cartPreview') . '?cartPreview=true' }}';
	</script>

</x-app-layout>