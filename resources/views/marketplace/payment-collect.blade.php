<x-app-layout>
  <x-app.header :cartItems="$cartItems" />


  {{-- ********************** --}}
  {{-- Strip Functionality JS --}}
  <script type="text/javascript">
    // Stripe Publishable Api Key
  const stripe = Stripe("{{ $stripePublishableKey }}"); //publishable API key.

  // set global return url
  const paymentSuccessReturnURL = '{{ route('payment-success') }}'

  // clientsecret generated
  const clientSecret = '{{$clientSecret}}';
  </script>





  {{-- Main Body Wrapper --}}
  <div id="app-body" class="flex justify-center flex-col max-w-7xl mx-auto">



    <div class="steps w-full py-12 hidden md:block">
      <x-marketplace.cart.steps />
    </div>



    @fragment('payment-content')
    <div id="content-body" class="justify-items-end bg-gray-50 my-8 h-auto rounded-lg mx-auto 
                max-w-full px-10 py-6 lg:py-12 sm:px-8 lg:max-w-screen-2xl lg:px-16 
                lg:grid lg:grid-cols-12  gap-x-12 ">





      {{-- Stripe JS Loading --}}
      @vite(['resources/js/checkout.js'])





      <!------------------------------->
      <!-- Billing Section Left Side -->
      <!------------------------------->
      <!-- 1. Billing Address Form   -->
      <!-- 2. Stripe Form            -->
      <!------------------------------->
      <div id="billing-section-left" class="col-span-7">



        <h2 class="sr-only">Checkout</h2>



        <!-- 2. Payment form -->
        <div id="payment-form-s" class="stripe-payment-form md:mt-12">

          <div class="">

            <div class="top flex justify-between">
              <h2 class="flex items-center text-lg font-medium text-gray-900 pb-4">
                <x-icons.lucide.credit-card width="20" height="20" class="mr-2" />
                Payment
              </h2>


              <div class="powered-by-stripe-logo w-28 h-auto pb-2">
                <a href="https://stripe.com" target="_blank" title="open stripe website in another tab">
                  <x-icons.etc.powered-by-stripe />
                </a>
              </div>
            </div>

            {{-- Stripe Form --}}
            {{-- <form id="stripe-form" data-secret="{{$clientSecret}}"> --}}
              <div id="stripe-form-auto" class="mt-5 w-full">
                <div id="payment-message"
                  class="hide-content text-left mb-4 px-3 py-4 border rounded-lg bg-white text-orange-600 border-orange-600 font-semibold">
                  <!--Stripe.js injects the Payment Message-->
                </div>
                <div id="payment-element">
                  <!--Stripe.js injects the Payment Element-->
                </div>
              </div>
              {{--
            </form> --}}

            {{-- Payment Disclaimer --}}
            <div id="payment-diclaimer" class="mt-8">
              <p class="text-sm text-gray-400 leading-6">
                {{ __('market.payment_disclaimer') }}
              </p>
            </div>

          </div>
        </div>

      </div>


      <!------------------------------->
      <!--  Order summary Right Side -->
      <!------------------------------->
      <div id="order-summary" class="mt-10 lg:mt-0  w-full lg:pl-6 max-w-[800px] col-span-5">


        <div id="order-summary-data" class="mt-4 rounded-lg border border-gray-200 bg-white shadow-sm">



          <h3 class="sr-only">Cart Data</h3>

          <h2 class="flex pl-6 pt-6 items-center text-lg font-medium text-gray-900 pb-4">
            <x-icons.lucide.scroll width="20" height="20" class="mr-2" />
            Order Summary
          </h2>


          <ul role="list" class="divide-y divide-gray-200">
            <li class="flex  sm:px-6">

              <div class="ml-1 my-2 flex flex-1 flex-col">

                <div id="cart-data" class="flex">
                  <div class="ml-4">
                    {{-- <h4 class="text-sm">
                      <a href="#" class="font-bold text-gray-700 hover:text-gray-800">
                        Sponsorships
                      </a>
                    </h4> --}}
                    <p class="mt-1 text-sm text-gray-500 flex items-center">
                      <x-icons.lucide.check class="w-4 h-4 mr-1.5" />
                      {{$cartStats['count']}} Publications
                    </p>
                    <p class="mt-1 text-sm text-gray-500 flex items-center">
                      <x-icons.lucide.check class="w-4 h-4 mr-1.5" />
                      1000 words article included
                    </p>
                  </div>
                </div>

              </div>
            </li>
            <!-- More products... -->
          </ul>

          <dl id="pricing-data-cart" class="space-y-6 border-t border-gray-200 mt-4 px-4 py-6 sm:px-6">
            <div class="flex items-center justify-between">
              <dt class="text-sm">Subtotal</dt>
              <dd class="text-sm font-medium text-gray-900">${{$cartStats['totalPrice']}}</dd>
            </div>
            <div class="flex items-center justify-between border-t border-gray-200 pt-6">
              <dt class="text-base font-medium">Total</dt>
              <dd class="text-base font-medium text-gray-900">${{$cartStats['totalPrice']}}</dd>
            </div>
          </dl>


          {{-- Get & Save Addres -> redirect to payment page --}}
          <form id="payment-form">
            <div id="payment-process-button" class="border-t border-gray-200 px-4 py-6 sm:px-6">
              <button id="submit-button" type="submit"
                class="flex justify-center w-full rounded-md border border-transparent bg-emerald-500 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-50 disabled:bg-gray-500 h-12">
                <span class="spinner hide-content" id="spinner">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                </span>
                <span id="payment-button-text">Pay Now</span>
              </button>
            </div>
          </form>

        </div>

      </div>

    </div>
    @endfragment


    {{-- Body Main Wrapper End --}}
  </div>





  {{-- Stripe Styles Copied From Template --}}
  <style type="text/css">
    .hide-content {
      display: none;
    }

    /*  #payment-message {
    color: rgb(105, 115, 134);
    font-size: 16px;
    line-height: 20px;
    padding-top: 12px;
    text-align: center;
  }
*/
    #payment-element {
      margin-bottom: 24px;
      min-width: auto;
    }


    button:hover {
      filter: contrast(115%);
    }

    button:disabled {
      /*    opacity: 0.5;*/
      cursor: default;
    }

    /* spinner/processing state, errors */
    /*  .spinner,
  .spinner:before,
  .spinner:after {
    border-radius: 50%;
  }
  .spinner {
    color: #ffffff;
    font-size: 22px;
    text-indent: -99999px;
    margin: 0px auto;
    position: relative;
    width: 20px;
    height: 20px;
    box-shadow: inset 0 0 0 2px;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
  }
  .spinner:before,
  .spinner:after {
    position: absolute;
    content: "";
  }
  .spinner:before {
    width: 10.4px;
    height: 20.4px;
    background: #ffffff;
    border-radius: 20.4px 0 0 20.4px;
    top: -0.2px;
    left: -0.2px;
    -webkit-transform-origin: 10.4px 10.2px;
    transform-origin: 10.4px 10.2px;
    -webkit-animation: loading 2s infinite ease 1.5s;
    animation: loading 2s infinite ease 1.5s;
  }
  .spinner:after {
    width: 10.4px;
    height: 10.2px;
    background: #ffffff;
    border-radius: 0 10.2px 10.2px 0;
    top: -0.1px;
    left: 10.2px;
    -webkit-transform-origin: 0px 10.2px;
    transform-origin: 0px 10.2px;
    -webkit-animation: loading 2s infinite ease;
    animation: loading 2s infinite ease;
  }

  @-webkit-keyframes loading {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
  @keyframes loading {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    100% {
      -webkit-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }*/

    @media only screen and (max-width: 600px) {
      form {
        width: 80vw;
        min-width: auto;
      }
    }
  </style>

</x-app-layout>