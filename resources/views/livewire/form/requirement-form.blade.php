<div class="border-gray-200  py-12 px-8 w-full">
    <form wire:submit.prevent="submit" class="grid grid-cols-1 lg:grid-cols-3 space-y-6 lg:space-y-0 lg:space-x-10">
        <input type="hidden" wire:model.defer="update_existing_requirement"
            value="{{ $orderItem->requirements ? true : false }}">
        {{-- 1. LEFT COLUMN --}}
        <div class="flex flex-col space-y-4 !ml-0">
            {{-- Article Topic --}}
            <div class="space-y-1">
                <div class="order-requirements-label-wrapper justify-between">
                    <label for="article_topic" class="order-requirement-input-label">
                        <x-icons.lucide.book-open class="w-4 h-4 order-requirement-input-label-icon" />
                        Article Topic
                        <span class="ml-1">
                            <x-ui.tooltip text="?" tooltip="What is the main topic for your article?" />
                        </span>
                    </label>
                    <span class="required-asterisk">*</span>
                </div>
                <div class="flex items-center">
                    <input type="text" @disabled($disable_fields) id="article_topic" wire:model.defer="article_topic"
                        class="order-requirements-text-input @error('article_topic') !border-red-600 @enderror"
                        placeholder="Example: Best CRM Tools for Startups" wire:loading.attr="disabled">
                    <div wire:loading wire:target="submit" class="ml-2">
                        <x-icons.lucide.loader class="w-4 h-4 animate-spin text-gray-500" />
                    </div>
                </div>
                @error('article_topic') <p class="text-red-600 text-sm">{{ $message }}</p> @enderror
            </div>
        </div>

        {{-- 2. MIDDLE COLUMN --}}
        <div class="flex flex-col space-y-4">
            {{-- Anchor Text --}}
            <div class="space-y-1">
                <label for="anchor_text" class="order-requirement-input-label">
                    <x-icons.lucide.link class="w-4 h-4 order-requirement-input-label-icon" />
                    Anchor Text
                    <span class="required-asterisk">*</span>
                </label>
                <input type="text" @disabled($disable_fields) id="anchor_text" wire:model.defer="anchor_text"
                    class="order-requirements-text-input @error('anchor_text') !border-red-600 @enderror"
                    placeholder="Example: CRM software for small teams" wire:loading.attr="disabled">
                @error('anchor_text') <p class="text-red-600 text-sm">{{ $message }}</p> @enderror
            </div>

            {{-- Advertiser URL --}}
            <div class="space-y-1">
                <label for="advertiser_url" class="order-requirement-input-label">
                    <x-icons.lucide.globe-earth class="w-4 h-4 order-requirement-input-label-icon" />
                    Advertiser URL
                    <span class="required-asterisk">*</span>
                </label>
                <input type="url" @disabled($disable_fields) id="advertiser_url" wire:model.defer="advertiser_url"
                    class="order-requirements-text-input @error('advertiser_url') !border-red-600 @enderror"
                    placeholder="https://example.com/landing-page" wire:loading.attr="disabled">
                @error('advertiser_url') <p class="text-red-600 text-sm">{{ $message }}</p> @enderror
            </div>
        </div>

        {{-- 3. RIGHT COLUMN --}}
        <div class="flex flex-col space-y-4">
            {{-- Requirement Comments --}}
            <div class="space-y-1">
                <label for="requirement_comments" class="order-requirement-input-label">
                    <x-icons.lucide.mail class="w-4 h-4 order-requirement-input-label-icon" />
                    Additional Comments
                </label>
                <textarea @disabled($disable_fields) id="requirement_comments" wire:model.defer="requirement_comments"
                    class="order-requirements-text-input @error('requirement_comments') !border-red-600 @enderror"
                    rows="5" placeholder="Anything else you'd like us to know?" wire:loading.attr="disabled"></textarea>
                @error('requirement_comments') <p class="text-red-600 text-sm">{{ $message }}</p> @enderror
            </div>

            {{-- Submit Button --}}
            @if (!$disable_fields)
            <div class="flex justify-end mt-2">
                <button wire:loading.attr="disabled" wire:target="submit" type="submit"
                    class="py-2 px-6 bg-emerald-700 text-white font-semibold rounded-md hover:bg-emerald-800 flex items-center justify-center min-w-[100px]">
                    <span wire:loading.remove wire:target="submit">
                        @if ($orderItem->requirements)
                        Update
                        @else
                        Submit
                        @endif
                    </span>
                    <span wire:loading wire:target="submit" class="flex items-center">
                        Processing...
                    </span>
                </button>
            </div>
            @endif
        </div>
    </form>
</div>