<div>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <!-- Header -->
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold">Transaction Details</h2>
                        <a href="{{ route('advertiser.wallet.index') }}"
                            class="text-blue-600 hover:text-blue-800 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                            Back to Wallet
                        </a>
                    </div>

                    <!-- Transaction Details -->
                    <div class="bg-white rounded-lg border border-gray-200">
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Transaction ID -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Transaction ID</h3>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{ $this->transaction->id }}</p>
                                </div>

                                <!-- Transaction Type -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Type</h3>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{
                                        ucfirst($this->transaction->type)
                                        }}</p>
                                </div>

                                <!-- Amount -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Amount</h3>
                                    <p
                                        class="mt-1 text-lg font-semibold {{ $this->transaction->type === 'withdraw' ? 'text-red-600' : 'text-green-600' }}">
                                        ${{ number_format($this->transaction->amount / 100, 2) }}
                                    </p>
                                </div>

                                <!-- Status -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Status</h3>
                                    <span
                                        class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->transaction->confirmed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $this->transaction->confirmed ? 'Approved' : 'Pending' }}
                                    </span>
                                </div>

                                <!-- Reference -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Reference</h3>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{
                                        $this->transaction->meta['reference'] ?? 'N/A' }}</p>
                                </div>

                                <!-- Order ID -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Order ID</h3>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{
                                        $this->transaction->meta['order_id'] ?? 'N/A' }}</p>
                                </div>

                                <!-- Date -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">Date</h3>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{
                                        $this->transaction->created_at->format('Y-m-d H:i:s') }}</p>
                                </div>

                                <!-- User -->
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500">User</h3>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{
                                        $this->transaction->wallet->holder->name }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>