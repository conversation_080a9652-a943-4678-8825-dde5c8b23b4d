@php
    $writerWords = str_word_count($guidelines_for_writer);
    $publisherWords = str_word_count($guidelines_for_publisher);
    $limit = config('pressbear.advertiser_guideline_words_limit');
@endphp

<div id="app-body" class="flex flex-col min-h-screen pt-14 mx-2 sm:mx-16">
    <main>
        <div class="relative isolate overflow-hidden  bg-white rounded-xl border">
            <!-- Secondary navigation -->
            <header class="pb-4 pt-6 sm:pb-6 bg-white">
                <div class=" px-4 sm:px-6 lg:px-8">
                    <h1 class="text-base font-semibold leading-7 text-zinc-700">Advertiser Guidelines</h1>
                    <div class="text-sm text-gray-500">
                        As an advertiser, you can set clear and consistent guidelines for both writers and publishers in one place. These guidelines will be visible globally, helping ensure your expectations are
                        understood and followed across all tasks and collaborations.
                    </div>
                </div>
            </header>
            <!-- Stats -->
            <div class="border-b border-b-gray-900/10 lg:border-t rounded lg:border-t-gray-900/10">
                <div class="p-6 space-y-6">


                    <form wire:submit.prevent="save" class="space-y-6 px-6 py-6">
                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-1 uppercase">Guidelines for Writer</label>
                            <textarea wire:model.live="guidelines_for_writer" rows="5" class="w-full border rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                            <!-- Writer Counter -->
                            <div class="pe-2 text-xs mt-1 text-right {{ $writerWords >= $limit ? 'text-red-500' : 'text-gray-500' }}">
                                {{ $writerWords }}/{{ $limit }} Words
                            </div>
                            @error('guidelines_for_writer')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-bold text-gray-700 mb-1 uppercase ">Guidelines for Publisher</label>
                            <textarea wire:model.live="guidelines_for_publisher" rows="5" class="w-full border rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                            <!-- Publisher Counter -->
                            <div class="pe-2 text-xs mt-1 text-right {{ $publisherWords >= $limit ? 'text-red-500' : 'text-gray-500' }}">
                                {{ $publisherWords }}/{{ $limit }} Words
                            </div>
                            @error('guidelines_for_publisher')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>


                        @if (session()->has('success'))
                            <div class="p-3 rounded bg-green-100 text-green-700">
                                {{ session('success') }}
                            </div>
                        @endif

                        <div class="flex gap-3 items-center">
                            <button type="submit" class="px-5 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 focus:outline-none disabled:opacity-30 disabled:cursor-not-allowed" @disabled($writerWords > $limit || $publisherWords > $limit)>
                                Save Guidelines
                            </button>

                            @if ($writerWords > $limit || $publisherWords > $limit)
                                <div class=" mt-1 text-right text-red-500">
                                    Guidelines must be less than {{ $limit }} words.
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
</div>
