<div id="body-wrapper" class="mx-16 my-12">
  <div id="intro-section" class="mb-6 text-slate-700 text-xl font-bold tracking-wide">All Orders</div>

  <!-- Table -->
  <div class="bg-white border shadow rounded-lg ">

    <table class="w-full table-auto divide-y divide-gray-200 dark:divide-gray-700 
                border-gray-300 rounded-lg">

      <thead class="bg-gray-100 dark:bg-slate-800 rounded-lg">

        <tr class="border-b border-gray-300">
          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Order Id
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Order Date
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Customer
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Order Status
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Amount
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Items
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Last Updated
            </span>
          </th>

          <th scope="col" class="admin-orders-list-th">
            <span class="">
              Action
            </span>
          </th>
      </thead>

      <tbody class="divide-y divide-gray-200 dark:divide-gray-700">

        @foreach($orders as $order)
        <tr class="admin-orders-list-tr">

          <td class="admin-orders-list-td">
            <a class=" text-emerald-900 decoration-2 font-medium hover:underline dark:text-emerald-500"
              href="{{route('admin-single-order', $order->id)}}">#{{$order->id}}</a>
          </td>

          <td class="admin-orders-list-td">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              @php
              $date=date_create($order['created_at']);
              echo date_format($date,"d M Y");
              @endphp
            </span>
          </td>

          <td class="admin-orders-list-td flex flex-col">
            <span class="text-sm text-gray-600 dark:text-gray-400 capitalize">
              {{$order->user->name ?? ''}}
            </span>
            <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {{$order->user->email ?? ''}}
            </span>
          </td>

          <td class="admin-orders-list-td">

            @if($order->order_completed)

            <span
              class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-green-200">
              Completed
            </span>

            @else

            @php
            $statusClass = match($order->status) {
            'delivered' => 'bg-green-100 text-green-800',
            'inprogress' => 'bg-yellow-100 text-yellow-800',
            'pending' => 'bg-yellow-100 text-gray-800',
            'cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
            };
            @endphp

            <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $statusClass }}">
              {{ ucfirst($order->status) }}
            </span>

            @endif


            {{--
            <span
              class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-green-200">
              <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                viewBox="0 0 16 16">
                <path
                  d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z">
                </path>
              </svg>
              Inprogress
            </span>

            <span
              class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 capitalize">
              <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                viewBox="0 0 16 16">
                <path
                  d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
              </svg>
              Delivered
            </span>

            <span
              class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-green-200">
              <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                viewBox="0 0 16 16">
                <path
                  d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z">
                </path>
              </svg>
              Late
            </span> --}}

          </td>

          <td class="admin-orders-list-td">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              ${{$order->price_paid}}
            </span>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <span class="text-sm text-gray-600 dark:text-gray-400 tracking-wider ">
              {{$order->items_in_orders}} Items
            </span>
          </td>


          <td class="admin-orders-list-td">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              @php
              $date=date_create($order['updated_at']);
              echo date_format($date,"d M Y");
              @endphp
            </span>
          </td>

          <td class="admin-orders-list-td">
            <div class="h-fit">
              <a class="text-white px-3 py-2 bg-emerald-600 hover:bg-emerald-700 text-sm rounded-lg"
                href="{{route('admin-single-order', $order->id)}}?id={{$order->id}}">
                Order Page
              </a>
            </div>
          </td>

        </tr>

        @endforeach



      </tbody>
    </table>
    <!-- End Table -->


    {{-- Pagination --}}
    <div class="p-4">
      {{ $orders->links() }}
    </div>

  </div>

  <!-- End Body Wrapper Div Below -->
</div>