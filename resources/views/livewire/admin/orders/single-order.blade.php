<div id="body-wrapper" class="m-16">

 <x-ui.toast />


 {{-- Top Info Box --}}
  <x-admin.single-order.order-info-box  :order="$order" 
                                        :orderStatus="$orderStatus" 
                                        :updateMessage="$updateMessage" />





    <div  class="space-y-8 border bg-white px-6 py-6 rounded-xl shadow" 
          x-data="{
            activeAccordion: '{{ $order->orderItems[0]->website->website_domain }}', 

            {{-- According set --}}
            setActiveAccordion(id) { 
                this.activeAccordion = (this.activeAccordion == id) ? '' : id 
            },

            {{-- Table link click toggle and scroll to box --}}
            scrollToggle(websiteID){
              this.setActiveAccordion(websiteID);

              {{-- timeout because of x-collapse duration in scroll --}}
              setTimeout(function() {
                   const element = document.getElementById(websiteID);
                    element.scrollIntoView();
              }, 250);

            } 
          }"
      @toggle.window="scrollToggle($event.detail)">

      @foreach($order->orderItems as $item)
        <livewire:admin.components.SingleOrderBox :key="$item->id" :item="$item" />
      @endforeach
    </div>




  <style type="text/css">
    .order-meta{
      font-size: 14px;
    }
    .order-meta li{
      margin-bottom: 3px; 
    }
  </style>


</div>








