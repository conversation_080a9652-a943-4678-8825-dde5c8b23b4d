<div x-data="{id:'{{$item->website->website_domain}}'}" id="{{$item->website->website_domain}}" key="{{$item->id}}"
  class="cursor-pointer box-section overflow-hidden">


  <div id="info-bar" @click="setActiveAccordion(id)"
    class="grid grid-cols-2 gap-y-6 md:grid-cols-5 md:items-center py-6 px-8 text-center md:justify-between bg-gray-50  ">

    <div id="info-single">
      <div id="site-info" class="flex">
        <span id="site-icon" class="">
          <img width="20px" height="20px"
            src="https://www.google.com/s2/favicons?sz=64&domain_url={{$item->website->website_domain}}"
            class="w-6 h-6 border-1 rounded-full mr-1.5"></span>
        <span id="site-name" class=" tracking-wide text-emerald-900 font-bold">
          {{$item->website->website_domain}}
        </span>
      </div>
    </div>

    <div id="info-single">
      <div id="info-label" class="text-xs font-medium text-gray-500 mb-1">
        Niche
      </div>
      <div id="info-data" class="text-sm font-semibold text-emerald-900 capitalize">
        {{$item->niche}}
      </div>
    </div>

    <div id="info-single">
      <div id="info-label" class="text-xs font-medium text-gray-500 mb-1">
        Payment
      </div>
      <div id="info-data" class="font-semibold text-sm text-emerald-900">
        ${{$item->price_paid}}
      </div>
    </div>

    <div id="info-single">
      <div id="info-label" class="text-xs font-medium text-gray-500 mb-1">
        Last Updated
      </div>
      <div id="info-data" class="font-semibold text-sm text-emerald-900">
        {{date_format($item->updated_at, 'd M Y')}}
      </div>
    </div>

    <div id="info-single" class="flex col-span-2 md:col-span-1">

      {{-- Order status controlled via alpine --}}
      <div id="order-status" class="ml-auto" x-data="{state: '{{ $item->state }}'}">

        @if($item->state == "requirements-pending")
        <span class="inline-flex items-center gap-1.5 py-1 px-3 rounded-full text-sm font-medium
                     bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-green-200">
          <x-icons.lucide.list />
          Requirements Pending
        </span>

        @elseif($item->state == "inprogress" )
        {{-- State: Inprogress --}}
        <span class="inline-flex items-center gap-1.5 py-1 px-3 rounded-full text-sm font-medium
                   bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-green-200">
          <x-icons.lucide.write />
          Inprogress
        </span>

        @elseif($item->state == 'order-started' )
        {{-- State: Inprogress --}}
        <span class="inline-flex items-center gap-1.5 py-1 px-3 rounded-full text-sm font-medium
                   bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-200">
          <x-icons.lucide.sparkles class="w-4 h-4" />
          Order Started
        </span>

        @elseif($item->state == "delivered")
        {{-- State: Deliverd --}}
        <span class="capitalize inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs 
                      font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <x-icons.lucide.drive />
          Delivered
        </span>
        @endif
      </div>

      {{-- Toggle Down icon --}}
      <div class="ml-auto p-2 cursor-pointer">
        <x-icons.animated.toggle-down />
      </div>
    </div>

  </div>


  {{-- test --}}
  <div>
    @if (session()->has('message'))
    <div class="alert alert-success">
      {{ session('message') }}
    </div>
    @endif
  </div>

  {{-- FORM INPUT SECTION ORDER --}}
  <form wire:submit="save">


    {{-- According hide/show --}}
    <div x-show="activeAccordion==id" x-collapse x-cloak>

      {{-- Requirements inputs --}}
      <div id="requirements-section" class="border-t-2 border-gray-200 grid grid-cols-3 lg:grid-cols-6 xl:grid-cols-9 
                  gap-y-12 gap-x-8 py-12  px-8">



        {{-- ****************************** --}}
        {{-- CUSTOMER PROVIDED REQUIREMENTS --}}
        <div class="requirement-one rounded-lg col-span-3 bg-gray-50 p-3 mr-4 shadow">

          {{-- <div class="flex font-bold text-xs uppercase mb-8 text-teal-800">
            <span class="w-4 h-4 mr-1">
              <x-icons.lucide.logs />
            </span>
            <span>Requirements</span>
          </div>
          --}}
          {{-- <ol class="font-medium list-decimal text-sm space-y-4 ml-4 pr-2">

            <li>
              <span class="text-gray-700 font-bold">Content:</span>
              @if(!$item->is_content_provided_by_customer)
              <span class="px-2 py-1 rounded-ful text-amber-800 bg-amber-100">Write For Me</span>
              @elseif($item->is_content_provided_by_customer)
              <span class="px-2 py-1 rounded-full text-emerald-800 bg-emerald-100">Customer Provided Content</span>
              @endif
            </li>

            <li>
              <div class="flex">
                <span class="text-gray-700 font-bold mr-2">
                  Article Topic:
                </span>
                <span class="text-emerald-900 break-all line-clamp-1">
                  {{ $item->content_topic }}
                </span>
              </div>
            </li>

            <li>
              <span class="text-gray-700 font-bold">
                Anchor:
              </span>
              <span class="text-orange-800 break-all">
                {{ $item->anchor_text }}
              </span>
            </li>

            <li>
              <span class="text-gray-700 font-bold">
                Target Link:
              </span>
              <a href="{{ $item->target_url }}" target="_blank">
                <span
                  class="text-emerald-900 border-b border-dashed border-orange-800 hover:border-orange-600 hover:text-orange-600 break-all">
                  {{ $item->target_url }}
                </span>
              </a>
            </li>

            <li>
              <span class="text-gray-700 font-bold">
                Order Item ID:
              </span>
              <span class="text-emerald-900">
                #{{ $item->id }}
              </span>
            </li>

            <li>
              <span class="text-gray-700 font-bold">
                Customer Comments:
              </span>
              <p class="text-emerald-900 text-pretty text-sm">
                {{ $item->customer_comments }}
              </p>
            </li>

          </ol> --}}

          <table
            class="text-sm table-auto border-collapse border bg-white w-full h-full border-gray-300 rounded-lg px-2 py-2 space-y-4  mr-2">

            <tr>
              <th colspan="2" class="py-2 admin-order-td text-gray-800">Requirements</th>
            </tr>

            <tr>

              <td class="admin-order-td">
                <span class="text-gray-700 font-bold">Content:</span>
              </td>

              <td class="admin-order-td">
                @if(!$item->is_content_provided_by_customer)
                <span class="px-2 py-1 rounded-ful text-amber-800 bg-amber-50">Write For Me</span>
                @elseif($item->is_content_provided_by_customer)
                <span class="px-2 py-1 rounded-full text-emerald-800 bg-emerald-50">Customer Provided Content</span>
                @endif
              </td>
            </tr>

            <tr>
              <td class="text-gray-700 font-bold admin-order-td">
                Article Topic:
              </td>
              <td class="text-emerald-900 admin-order-td">
                {{ $item->content_topic }}
              </td>
            </tr>

            <tr>
              <td class="text-gray-700 font-bold admin-order-td">
                Anchor:
              </td>
              <td class="text-emerald-800 break-all border admin-order-td">
                {{ $item->anchor_text }}
              </td>
            </tr>

            <tr>
              <td class="text-gray-700 font-bold admin-order-td">
                Target Link:
              </td>
              <td class="admin-order-td">
                <a href="{{ $item->target_url }}" target="_blank">
                  <span
                    class="text-emerald-900 border-b border-dashed border-orange-800 hover:border-orange-600 hover:text-orange-600 break-all">
                    {{ $item->target_url }}
                  </span>
                </a>
              </td>
            </tr>

            <tr>

              <td class="text-gray-700 font-bold admin-order-td">
                Order Item ID:
              </td>
              <td class="text-emerald-900 admin-order-td">
                #{{ $item->id }}
              </td>
            </tr>

            <tr>
              <td class="text-gray-700 font-bold admin-order-td">
                Customer Comments:
              </td>
              <td class="text-emerald-900 text-pretty text-sm admin-order-td">
                {{ $item->customer_comments }}
              </td>
            </tr>

          </table>
        </div>



        {{-- *************** --}}
        {{-- CONTENT SECTION --}}
        <div class="requirements-two flex flex-col space-y-6 col-span-3 xl:col-span-2">

          <div class="flex font-bold text-xs uppercase mb-2 text-teal-800">
            <span class="w-4 h-4 mr-1">
              <x-icons.lucide.square-pen />
            </span>
            <span>Content</span>
          </div>

          {{-- Content URL --}}
          <div class="space-y-1">
            <div class="label flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Content URL
              </label>
              <span class="">
                <x-ui.tooltip text="?"
                  tooltip="Please provide the link to your content source. Make sure its publicly accessible. (Google docs/notion/drive,..etc)" />
              </span>
            </div>

            <div class="flex items-center">
              <input wire:model="content_url" value="{{ $item->content_url }}" type="text"
                class="bg-gray-50/70 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700 ease-out disabled:bg-gray-300 disabled:text-gray-400 font-medium text-emerald-900"
                placeholder="Article URL (google docs,.etc)">

              @if($item->content_url)
              <a target="_blank" href="{{ $item->content_url ?? '#' }}"
                class="w-7 h-7 text-emerald-900 -ml-10 hover:bg-emerald-200 rounded-full p-1.5 bg-emerald-50 transition">
                <x-icons.lucide.external-link />
              </a>
              @endif
            </div>

            @error('content_url')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div>


          {{-- Writer --}}
          <div class="space-y-1">
            <div class="label flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Writer Assigned
              </label>
              <span class="">
                <x-ui.tooltip text="?" tooltip="In-house writer name" />
              </span>
            </div>
            <input wire:model="content_writer" @disabled($item->is_content_provided_by_customer)
            {{-- value="{{ $item->content_url }}" --}}
            type="text"
            class="bg-gray-50/70 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700
            ease-out disabled:bg-gray-100 disabled:text-gray-400 font-medium disabled:cursor-not-allowed
            text-emerald-900"
            placeholder="Writer Assigned To">

            @error('content_writer')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div>

        </div>






        {{-- ****************** --}}
        {{-- PUBLISHING SECTION --}}
        <div class="requirements-three flex flex-col space-y-6 col-span-3 xl:col-span-2">

          {{-- Publishing Label --}}
          <div class="flex font-bold text-xs uppercase mb-1 text-teal-800">
            <span class="w-4 h-4 mr-1">
              <x-icons.lucide.newspaper />
            </span>
            <span>Publishing</span>
          </div>


          {{-- Publisher Email --}}
          {{-- <div class="space-y-1">
            <div class="flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Publisher Email
              </label>
              <x-ui.tooltip text="?" tooltip="Your primary link you want to place in the article." />
            </div>
            <input type="text" wire:model="publisher_email"
              class="bg-gray-50/70 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700 ease-out font-medium text-emerald-900"
              placeholder="Email used to communicate with publisher.">
            @error('publisher_email')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div> --}}


          {{-- Publisher Amount Paid --}}
          <div class="space-y-1">
            <div class="flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Publisher Amount Paid
              </label>
              <x-ui.tooltip text="?" tooltip="Amount paid to the publisher." />
            </div>
            <div class="flex items-center">
              <span class="pl-3 z-50 absolute font-medium text-gray-500">$</span>
              <input type="number" wire:model="publisher_payment_paid"
                class="bg-gray-50/30 pl-6 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700 ease-out font-medium text-emerald-900"
                placeholder="Price paid.">
            </div>
            @error('publisher_payment_paid')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div>


          {{-- Payment Proof --}}
          <div class="space-y-1">
            <div class="flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Payment Proof
              </label>
              <x-ui.tooltip text="?" tooltip="Your primary link you want to place in the article." />
            </div>
            <input type="text" wire:model="payment_proof"
              class="bg-gray-50/70 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700 ease-out font-medium text-emerald-900"
              placeholder="Invoice or screenshot link">
            @error('payment_proof')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div>

        </div>




        {{-- ******************** --}}
        {{-- FULLFULIMENT SECTION --}}
        <div class="fullfilment-section flex flex-col space-y-6 col-span-3 xl:col-span-2">

          {{-- Top Label --}}
          <div class="flex font-bold text-xs uppercase mb-2 text-teal-800">
            <span class="w-4 h-4 mr-1.5">
              <x-icons.lucide.truck-ship />
            </span>
            <span>Fullfilment</span>
          </div>


          {{-- Input: Published Link --}}
          <div class="space-y-1">
            <div class="flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Published Article Link
              </label>
              <x-ui.tooltip text="?" tooltip="Your primary link you want to place in the article." />
            </div>

            <div class="flex items-center">
              <input type="text" wire:model="published_post_url"
                class="bg-gray-50/70 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700 ease-out font-medium text-emerald-900"
                placeholder="Published post link.">


              @if($item->publication->publication_url)
              <a target="_blank" href="{{ $item->publication->publication_url }}"
                class="w-7 h-7 text-emerald-900 -ml-10 hover:bg-emerald-200 rounded-full p-1.5 bg-emerald-50 transition">
                <x-icons.lucide.external-link />
              </a>
              @endif
            </div>

            @error('published_url')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror

          </div>



          {{-- ****************** --}}
          {{-- Internal Comments --}}
          <div class="space-y-1">
            <div class="flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Internal Order Note
              </label>
              <x-ui.tooltip text="?" tooltip="Note for internal use." />
            </div>
            <textarea placeholder="Internal Notes" wire:model="internal_note"
              class="bg-gray-50/70 border border-gray-300 text-sm rounded-lg block p-3 w-full transition-all duration-700 ease-out font-medium text-emerald-900"></textarea>

            @error('internal_note')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div>

          {{-- Target Link --}}
          <div class="space-y-1">
            <div class="flex items-center space-x-1">
              <label for="topic" class="text-xs font-semibold text-gray-500">
                Order Status
              </label>
              <x-ui.tooltip text="?" tooltip="Your primary link you want to place in the article." />
            </div>
            <select name="status" wire:model="order_status"
              class="bg-gray-50/70 border border-gray-300 text-sm rounded-full block px-5 py-2.5 w-2/3 transition-all duration-700 ease-out font-medium text-emerald-900 capitalize">
              @foreach($orderStates as $state)
              <option value="{{ $state }}">{{ $state }}</option>
              {{-- <option value="inprogress">✽ Inprogress</option>
              <option value="requirements-pending">Requirements Pending</option>
              <option value="order-started">✈ Order Started</option>
              <option value="delievered">✔ Delivered</option>
              <option value="needs-attention">✿ Customer Attention</option>
              <option value="cancelled">✘ Cancelled</option> --}}
              @endforeach
            </select>

            @error('order_status')
            <span class="text-sm text-red-700 error">{{ $message }}</span>
            @enderror
          </div>

        </div>

      </div>




      {{-- Bottom bar --}}
      <div id="action-buttons"
        class="flex justify-between bg-gray-50 border-dashed border-gray-300 border-t py-6 px-8 ">


        {{-- show published post url here too --}}
        @if(empty(!$item->publication->publication_url))
        <div id="published-data"
          class="flex items-center w-fit bg-gray-100 px-8 py-3 rounded-full text-gray-600 text-sm">
          <div class="w-3 h-3 mr-1">
            <x-icons.lucide.newspaper />
          </div>
          <div id="published-label" class="font-semibold mr-2 text-gray-600">
            Published URL:
          </div>
          <div id="published-data" class="text-emerald-800">
            <a target="_blank" class="flex items-center" href="{{ $item->publication->publication_url }}">
              <div id="published-url" class="italic underline tracking-wide font-medium">
                {{ $item->publication->publication_url }}
              </div>
              <div class="w-3 h-3 ml-1 text-emerald-800">
                <x-icons.lucide.external-link />
              </div>
            </a>
          </div>
          <div id="publication meta" class="mx-4 text-xs text-gray-600">{{ $item->delivery_date }}</div>
        </div>
        @endif


        {{-- Notify Customer button --}}
        <div x-data="{  modalOpen: false, 
                        notify(type){
                          this.modalOpen = false;
                          $wire.notifyCustomer(type);
                        } }" @keydown.escape.window="modalOpen = false">
          <div type="notification" id="save-button" @click="modalOpen=true" class=" flex
                          w-fit 
                          items-center 
                          py-2 
                          font-semibold 
                          px-6 
                          ml-auto 
                          border-2 
                          rounded-full 
                          text-xs
                          text-gray-500 
                          disabled:bg-gray-400 
                          hover:border-emerald-800
                          hover:bg-emerald-50 
                          hover:text-emerald-800">
            <span>Notify Customer</span>
          </div>

          {{-- Modal Notifications --}}
          {{-- <div x-data="{ modalOpen: false }" @keydown.escape.window="modalOpen = false"
            class="relative z-50 w-auto h-auto"> --}}
            {{-- <button @click="modalOpen=true"
              class="inline-flex items-center justify-center h-10 px-4 py-2 text-sm font-medium transition-colors bg-white border rounded-md hover:bg-neutral-100 active:bg-white focus:bg-white focus:outline-none focus:ring-2 focus:ring-neutral-200/60 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none">Open</button>
            --}}
            <template x-teleport="body">
              <div x-show="modalOpen"
                class="fixed top-0 left-0 z-[99] flex items-center justify-center w-screen h-screen" x-cloak>
                <div x-show="modalOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
                  x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-300"
                  x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" @click="modalOpen=false"
                  class="absolute inset-0 w-full h-full bg-black bg-opacity-40"></div>
                <div x-show="modalOpen" x-trap.inert.noscroll="modalOpen" x-transition:enter="ease-out duration-300"
                  x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                  x-transition:leave="ease-in duration-200"
                  x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                  x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  class="relative w-full py-6 bg-white px-7 sm:max-w-lg sm:rounded-lg">
                  <div class="flex items-center justify-between pb-2">
                    <h3 class=" font-semibold">Notify Customer</h3>
                    <button @click="modalOpen=false"
                      class="absolute top-0 right-0 flex items-center justify-center w-8 h-8 mt-5 mr-5 text-gray-600 rounded-full hover:text-gray-800 hover:bg-gray-50">
                      <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div class="relative w-auto text-sm">
                    <p>Email Customer About This Order Item</p>
                  </div>
                  <div class="flex flex-col-reverse mt-8 sm:flex-row sm:justify-between sm:space-x-2">
                    <button @click="notify('needs-attention')" type="button"
                      class="inline-flex items-center justify-center h-10 px-4 py-2 text-sm font-medium transition-colors border-2 rounded-md focus:outline-none focus:ring-2 focus:ring-neutral-100 focus:ring-offset-2 text-gray-700 hover:bg-gray-100">
                      Needs Attention
                    </button>
                    <button @click="notify('item-delivered')" type="button"
                      class="inline-flex items-center justify-center h-10 px-4 py-2 text-sm font-medium text-white transition-colors border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-neutral-900 focus:ring-offset-2 bg-neutral-950 hover:bg-neutral-900 hover:bg-opacity-90">
                      Item Delivered
                    </button>
                  </div>
                </div>
              </div>
            </template>
            {{--
          </div> --}}
        </div>

        {{-- update button --}}
        <div>
          <button type="submit" x-data="{processing: false}" id="save-button" @click="processing == true" class="flex w-fit items-center h-fit py-2 font-semibold px-6 ml-auto bg-emerald-700 
                      text-white rounded-md disabled:bg-gray-400 hover:bg-emerald-800">
            <span x-show="processing" x-cloak class="-ml-1 mr-2 h-4 w-4 text-white">
              <x-icons.animated.spin />
            </span>
            <span>Update</span>
          </button>
        </div>

      </div>

    </div>
  </form>

</div>