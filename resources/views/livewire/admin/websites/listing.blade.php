<div id="body-wrapper" class="m-16">


  <div id="intro-section" class=" text-slate-700 text-xl font-bold tracking-wide mb-6">
    All Websites
  </div>


  {{-- Action: buttons/filters --}}
  <div id="top-filters-bar" class="box-section-filters flex  mb-2">

    <a href="{{route('site-editor-form')}}" class="filter-button-admin">
      <button >
        Add a New Website
      </button>
    </a>

    <button class="filter-button-admin">
        Show All Websites
    </button>
    
    <button class="filter-button-admin">
        Inactive Websites
    </button>

    <button class="filter-button-admin">
        Sort By No Of Orders
    </button>

    <div class="search-admin ml-auto">
        
        <form wire:submit="getWebsites">
            <input  class="rounded-full border-gray-400 px-3" 
                    type="text"
                    name="search" 
                    wire:model="search" 
                    placeholder="Search...">
        </form>

    </div>

  </div>


    
  {{-- Table Styling --}}
  <style type="text/css">
    table tr td, table tr th {
      padding: 15px 0;
    }
  </style>


  @if (session('status'))
    <div class="flex py-3 px-4 bg-green-50 my-2 text-green-900">
        {{ session('status') }}
    </div>
  @endif


  {{-- Table --}}
  <div class="bg-white border border-zinc-200 rounded-md px-6 py-4">

    <!-- Table -->
    <table class="border-collapse  min-w-full  dark:divide-gray-700 border-gray-100 border-spacing-6 ">

      <thead>
        <tr>

          <th scope="col" >
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                Website
              </span>
            </div>
          </th>

          <th scope="col" >
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                Updated
              </span>
            </div>
          </th>

          <th scope="col" >
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                Publisher
              </span>
            </div>
          </th>

          <th scope="col" >
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                Status
              </span>
            </div>
          </th>

          <th scope="col" >
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                Price
              </span>
            </div>
          </th>

          <th scope="col" >
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                Verified
              </span>
            </div>
          </th>

        </tr>

      </thead>



      {{-- Table body --}}
      <tbody class="divide-y divide-gray-200 border-spacing-1  dark:divide-gray-700">

        @foreach($websites as $website)
        <tr>
          <td class="h-px w-px  whitespace-nowrap">
            <div >
              <a class="text-sm font-semibold text-blue-600 decoration-2 hover:underline dark:text-blue-500" 
                href="{{route('admin-website-single', $website->id)}}">{{$website['website_domain']}}</a>
            </div>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">
                @php
                  $date=date_create($website['updated_at']);
                  echo date_format($date,"d M Y");
                @endphp
              </span>
            </div>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <div class="flex flex-col">
              <span class="text-sm text-gray-600 dark:text-gray-400">NA</span>
              <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">NA</span>
            </div>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <div>

              @if($website['active'])
              <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                </svg>
                Active
              </span>
              @else
              <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-green-200">
                <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"></path>
                </svg>
                Inactive
              </span>
              @endif
            </div>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">${{$website['guest_post_price']}}</span>
            </div>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span>
                @if($website['contact_email'])  
                  <x-icons.lucide.check-circle class="text-emerald-700 w-4 h-4 inline" />
                @else
                <x-icons.lucide.circle-x class="text-amber-900 w-4 h-4 inline" />
                @endif
              </span>
            </div>
          </td>

        </tr>

        @endforeach

        <!--
        <tr>

          <td class="h-px w-px whitespace-nowrap">
            <div >
              <a class="text-sm font-semibold text-blue-600 decoration-2 hover:underline dark:text-blue-500" href="#">youtube.com</a>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">Aug 17, 2020</span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">Nicky Olvsson</span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-green-200">
                <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"></path>
                </svg>
                Inactive
              </span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">$ 300</span>
            </div>
          </td>

          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">9 items</span>
            </div>
          </td>

        </tr>



        <tr>

          <td class="h-px w-px whitespace-nowrap">
            <div >
              <a class="text-sm font-semibold text-blue-600 decoration-2 hover:underline dark:text-blue-500" href="#">example.com</a>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">Aug 17, 2020</span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">David Nunez</span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-green-200">
                <svg class="w-2.5 h-2.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"></path>
                </svg>
                Terminated
              </span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">$1200</span>
            </div>
          </td>
          <td class="h-px w-px whitespace-nowrap">
            <div >
              <span class="text-sm text-gray-600 dark:text-gray-400">3 Items</span>
            </div>
          </td>
        </tr>

    -->

      </tbody>
    </table>
    <!-- End Table -->

  <div class="pagination mt-6 mb-2">
  {{ $websites->links() }}
  </div>

  </div>

<!-- End Body Wrapper Div Below -->
</div>

