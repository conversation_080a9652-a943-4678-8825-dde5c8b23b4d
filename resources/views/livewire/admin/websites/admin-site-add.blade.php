<div>
   
<section class="form bg-white dark:bg-gray-900">
    <div class="py-8 px-4 mx-auto max-w-3xl lg:py-12">

    <h2 class="mb-8 text-lg border-b-2 font-bold text-orange-900 tracking-widest uppercase dark:text-white">
        Add New Website
    </h2>

    <form wire:submit="update">

      <div class="flex flex-col gap-4 space-y-6">


        <div class="space-y-4">
          <div class="w-full ">
            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Website Domain
            </label>
            <input wire:model="website_domain" type="text" name="name" id="name" class="admin-input-field" placeholder="Type website domain name" required value="">
          </div>

          <div class="w-full">
            <label for="brand" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
              Publisher Email
            </label>
            <input wire:model="contact_email" type="number" name="brand" id="brand"  class="admin-input-field" placeholder="contact email" value="">
          </div>
        </div>


          
        {{-- ******* --}}
        {{-- Pricing --}}
        <div class="w-full py-4 border-t">

          <div class="text-amber-600 font-bold uppercase text-sm py-2">Pricing</div>

          <div class="grid grid-cols-2 space-x-3 mb-6">
            <div class="">
              <label for="brand" class=" mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Guest Post Price ($)</label>
              <input wire:model="guest_post_price" type="number" name="brand" id="brand" class="admin-input-field"placeholder="Price" required value="">
            </div>   
            <div class="">
              <label for="brand" class=" mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Link Inser Price ($)</label>
              <input wire:model="link_insert_price" type="number" name="brand" id="brand" class="admin-input-field" placeholder="Price" required value="">
            </div>   
          </div>


          <div class="my-4 grid grid-cols-3 gap-5">
            <div class="">
                <label for="price" class="text-sm font-medium text-gray-900 dark:text-white">
                  Crypto Post Price
                </label>
                <input wire:model="casino_post_price" type="number" class="admin-input-field" placeholder="" value="">
            </div>

            <div class="">
                <label for="price" class="text-sm font-medium text-gray-900 dark:text-white">
                  Casino Post Price
                </label>
                <input wire:model="crypto_post_price" type="number" class="admin-input-field" placeholder="" required value="">
            </div>

            <div class="">
                <label for="price" class="text-sm font-medium text-gray-900 dark:text-white">
                  Adult Post Price
                </label>
                <input wire:model="adult_post_price" type="number" class="admin-input-field" placeholder="" required value="">
            </div>

            <div class="">
                <label for="price" class="text-sm font-medium text-gray-900 dark:text-white">
                  Finance Post Price
                </label>
                <input wire:model="finance_post_price" type="number" class="admin-input-field" placeholder="" required value="">
            </div>

            <div class="">
                <label for="price" class="text-sm font-medium text-gray-900 dark:text-white">
                  Dating Post Price
                </label>
                <input wire:model="dating_post_price" type="number" class="admin-input-field" placeholder="" required value="">
            </div>

            <div class="">
                <label for="price" class="text-sm font-medium text-gray-900 dark:text-white">
                  CBD Post Price
                </label>
                <input wire:model="cbd_post_price" type="number" class="admin-input-field" placeholder="" required value="">
            </div>
          </div>
        
        </div>


          <div class="flex flex-col py-4 space-y-4 border-t">

            <div class="text-amber-600 font-bold uppercase text-sm py-2">Publishing</div>

            <div class="w-full">
                  <label for="brand" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Example Post URL
                  </label>
                  <input wire:model="example_post_url" type="number" name="brand" id="brand"  class="admin-input-field" placeholder="contact email" value="">
              </div>

            <div class="w-full">
              <label for="note" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Site Requirements
              </label>
              <textarea wire:model="site_requirements"  id="note" rows="2"  class="admin-input-field" placeholder="Site Publishing Requirements"></textarea>
            </div>


            <div class="grid grid-cols-2 gap-4">
              <div class="w-full">
                  <label for="price" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Avg Publishing Time (Days)
                  </label>
                  <input wire:model="turn_around_time_in_days" type="number" name="price" id="price"  class="admin-input-field" placeholder="Number of Days" required value="">
              </div>

               <div class="w-full">
                  <label for="brand" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Article Validity (Months)
                  </label>
                  <input wire:model="article_validity_in_months" type="number" name="brand" id="brand"  class="admin-input-field" placeholder="contact email" value="">
              </div>
            </div>

          </div>






          {{-- CATEGORY + LANGUAGE  --}}

          <div class="border-t py-4">
            
            <div class="text-amber-600 font-bold uppercase text-sm py-6">Data</div>

          
          <div class="grid grid-cols-2  gap-5">

              <div>
                <label for="category" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                Site Category</label>
                <select wire:model="" id="category" class="admin-input-field">
                  @foreach($categories as $category)
                      <option value="{{ $category->id }}">{{ $category->category }}</option>
                  @endforeach
                  </select>
                  
              </div>

               <div>
                  <label  class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Site Language
                  </label>
                  <select wire:model="site_language_id" id="language" class="admin-input-field">
                    @foreach($languages as $language)
                      <option value="{{ $language->id }}">{{ $language->name }}</option>
                    @endforeach
                  </select>
              </div> 


              <div>
                  <label for="item-weight" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    Active Status
                  </label>
                  <select wire:model="" id="category" class="admin-input-field">
                      <option value="1" >Active</option>
                      <option value="0">Cancelled</option>
                  </select>
              </div> 

              <div>
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  Link Relation
                </label>
                <select wire:model="link_relation" id="status" class="admin-input-field">
                      <option value="nofollow">Dofollow</option>
                      <option value="nofollow">Nofollow</option>
                  </select>
              </div>

              <div>
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  Indexed Article
                </label>
                <select wire:model="indexed_article" id="status" class="admin-input-field">
                      <option value="1">Yes</option>
                      <option value="0">No</option>
                  </select>
              </div>


              <div>
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  Sponsorship Label
                </label>
                <select wire:model="sponsorship_label" id="status" class="admin-input-field">
                      <option value="1">Yes</option>
                      <option value="0">No</option>
                  </select>
              </div>


              <div>
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  Homepage Visible
                </label>
                <select wire:model="homepage_visible" id="status" class="admin-input-field">
                      <option value="1">Yes</option>
                      <option value="0">No</option>
                  </select>
              </div>

            </div>

          </div>
            

          
            <div class="sm:col-span-2 mt-3">
                <label for="note" class="block mb-2 text-sm font-medium text-orange-900 dark:text-white">
                  Internal Note
                </label>
                <textarea wire:model="active" id="note" rows="2"  class="admin-input-field" placeholder="This is for internal use."></textarea>
            </div>

          </div>

          <button type="submit" class="inline-flex items-center px-5 py-2.5 mt-4 sm:mt-6 text-sm font-medium text-center text-white bg-primary rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800">
              Add Website
          </button>

      </form>
  </div>
</section>



</div>




{{-- 
            <div class="col-span-2 mb-3">
               
               <label class="blocktext-sm mb-4 font-medium text-gray-900 dark:text-white block">
                    Acceptable Niches
                </label>

                <span class="mr-5 text-gray-600 border-2 rounded-lg p-3">
                    <input type="checkbox" class="rounded-md p-2 mr-0.5 -mt-1" name="casino" value="1" checked>
                    <label for="casino">Casino</label>
                </span>

                <span class="mr-5 text-gray-600 border-2 rounded p-3">
                    <input type="checkbox" class="rounded-md p-2 mr-0.5 -mt-1" name="adult" value="1">
                    <label for="casino">Adult</label>
                </span>

                <span class="mr-5 text-gray-600 border-2 rounded p-3">
                    <input type="checkbox" class="rounded-md p-2 mr-0.5 -mt-1" name="crypto" value="1">
                    <label for="casino">Crypto</label>
                </span>

                <span class="mr-5 text-gray-600 border-2 rounded p-3">
                    <input type="checkbox" class="rounded-md p-2 mr-0.5 -mt-1" name="cbd" value="1" checked>
                    <label for="casino">CBD</label>
                </span>
            </div> --}}

