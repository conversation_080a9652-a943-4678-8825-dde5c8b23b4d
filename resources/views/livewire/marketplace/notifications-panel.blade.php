<div>
    <div x-data="{
        visible: false,
        unreadCount: {{ $unreadCount }},
        animateBell: false,
        startAnimation() {
            this.animateBell = true;
            setTimeout(() => this.animateBell = false, 2000); // Match your CSS animation duration
        },
        init() {
            // Animate on load if there are unread notifications
            if (this.unreadCount > 0) {
                this.startAnimation();
            }
    
            // Animate every 10 seconds if unreadCount > 0
            setInterval(() => {
                if (this.unreadCount > 0) {
                    this.startAnimation();
                }
            }, 10000);
        }
    }" x-init="init()" id="">

        {{-- Icons --}}
        <div id="notification-icon-top-menu" @click="visible = !visible" :class="!visible || 'bg-gray-100'"
            class="p-3 hover:bg-gray-100 rounded-full cursor-pointer relative">
            <div :class="{ 'animate-shake': unreadCount > 0 && animateBell }">
                <x-icons.etc.notification-bell />
            </div>
            <span x-show="unreadCount > 0" x-cloak
                class="absolute -top-0 -right-0 inline-flex items-center justify-center rounded-full bg-red-500 px-1.5 text-xs text-white">
                <span x-text="unreadCount"></span>
            </span>
        </div>

        {{-- Notification Panel --}}
        <div x-show="visible" x-cloak id="notifications-panel" @click.away="visible=false"
            x-transition:enter="ease-out duration-200" x-transition:enter-start="-translate-y-2"
            x-transition:enter-end="translate-y-0" class="absolute right-6 md:right-32 z-50 mt-2 min-h-32 max-h-96 overflow-y-scroll w-72 md:w-80 origin-top-right border rounded-md 
                        bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none items-center"
            role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">

            <div class="flex justify-between items-center border-b p-2">
                <div class="text-center font-bold text-xs text-gray-500 uppercase tracking-wide">
                    Notifications
                </div>
                @if ($unreadCount > 0)
                <button wire:click="markAllAsRead" class="text-xs text-blue-600 hover:text-blue-800">
                    Mark all as read
                </button>
                @endif
            </div>

            {{-- Notifications List --}}
            @if (count($notifications) > 0)
            <div class="flex flex-col items-center justify-center content-center">
                {{-- Single Notification item --}}
                @foreach ($notifications as $notification)
                @php
                $href = isset($notification->data['url'])
                ? $notification->data['url']
                : (isset($notification->data['data']['is_open_chat'])
                ? route('advertiser.order-item-details', $notification->data['order_id']) . '?openChat=true'
                : (isset($notification->data['order_id'])
                ? route('advertiser.order-item-details', $notification->data['order_id'])
                : '#'));
                @endphp
                <a href="{{ $href }}" wire:navigate wire:click="markAsRead('{{ $notification->id }}')"
                    class="flex w-full flex-col text-sm space-y-1 border-b px-5 py-4 hover:bg-gray-100 {{ $notification->read_at ? '' : 'bg-gray-50 font-medium' }}">
                    <div class="text-sm font-bold text-gray-600">
                        {{ $notification->data['title'] ?? '' }}
                    </div>
                    <div class="text-xs text-gray-600">
                        {{ $notification->data['message'] ?? '' }}
                    </div>
                    <div class="text-xs text-gray-400 text-right">
                        {{ $notification->created_at->diffForHumans() }}
                    </div>
                </a>

                @php
                if ($loop->iteration > 20) {
                break;
                }
                @endphp
                @endforeach
            </div>

            {{-- Empty State: No Notifications --}}
            @else
            <div class="flex flex-col items-center justify-center content-center p-4">
                <x-icons.etc.drawer class="w-3 h-3 stroke-gray-500" />
                <span class="text-gray-500 p-2">No Notifications</span>
            </div>
            @endif
        </div>
    </div>

    {{-- Pusher Integration --}}
    @script
    <script>
        document.addEventListener('livewire:initialized', function() {
                // Listen for new notifications
                window.Echo.private('App.Models.User.{{ auth()->id() }}')
                    .notification((notification) => {

                        // Show toast notification
                        toast(notification.message, {
                            type: 'info',
                            position: 'bottom-right'
                        });

                        $wire.loadNotifications();
                    });
            });
    </script>
    @endscript
</div>