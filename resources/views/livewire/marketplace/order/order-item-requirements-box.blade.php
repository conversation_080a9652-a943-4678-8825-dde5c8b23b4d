@php
use App\Enums\OrderItemStates;
@endphp
<div x-data="{ 
    id: '{{ $item->website->website_domain }}', 
    state: '{{ $item->state_name }}',
    stateLabel: '{{ $item->state_label }}'
  }" id="{{$item->website->website_domain}}" class="cursor-pointer box-section overflow-hidden shadow-sm">


    <div id="info-bar" @click="setActiveAccordion(id)" x-data class="grid grid-cols-2 space-x-2 md:space-x-0 md:grid-cols-4 lg:grid-cols-5 items-center 
              py-4 px-6 md:py-6 md:px-8 text-center 
              justify-between bg-gray-50  ">

        <div id="info-single">
            {{-- <div id="info-label" class="text-xs font-medium text-gray-500 mb-2">Site</div> --}}
            <div id="site-info" class="flex">
                <span id="site-icon" class="">
                    <img width="20px" height="20px"
                        src="https://www.google.com/s2/favicons?sz=64&domain_url={{$item->website->website_domain}}"
                        class="w-6 h-6 border-1 rounded-full mr-1.5">
                </span>
                <span id="site-name" class=" tracking-wide text-emerald-900 font-bold">
                    {{$item->website->website_domain}}
                </span>
            </div>
        </div>

        <div id="info-single" class="hidden lg:block">
            <div id="info-label" class="text-xs font-medium text-gray-500 mb-1">
                Niche
            </div>
            <div id="info-data" class="text-sm font-semibold text-emerald-900 capitalize">
                {{$item->niche}}
            </div>
        </div>

        <div id="info-single" class="hidden lg:block">
            <div id="info-label" class="text-xs font-medium text-gray-500 mb-1">
                Price
            </div>
            <div id="info-data" class="font-semibold text-sm text-emerald-900">
                ${{$item->price_paid}}
            </div>
        </div>

        <div id="info-single" class="hidden lg:block">
            <div id="info-label" class="text-xs font-medium text-gray-500 mb-1">
                Last Updated
            </div>
            <div id="info-data" class="font-semibold text-sm text-emerald-900">
                {{date_format($item->updated_at, 'd M Y')}}
            </div>
        </div>

        <div id="info-single" class="flex  items-center">

            {{-- Order status controlled via alpine --}}
            <div id="order-status" class="ml-auto">

                {{-- State: Badge --}}
                <div x-cloak>
                    <x-ui.badges.order.statusBadge :item="$item" />
                </div>

            </div>


            {{-- Toggle Down icon --}}
            <div class="ml-auto p-2 cursor-pointer">
                <svg class="w-4 h-4 duration-200 ease-out" :class="{ 'rotate-180': activeAccordion==id }"
                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
            </div>
        </div>

    </div>

    {{-- @php dd($item->is_content_provided_by_customer); @endphp --}}




    {{-- According hide/show --}}
    <div x-show="activeAccordion==id" class="border-t-2" x-collapse x-cloak>


        {{-- Form Submission erros --}}
        <div x-show="$store.errors.error.id == {{ $item->id }}" class="errors py-4 px-8">
            <ul class="space-y-2">
                <template x-for="error in $store.errors.error">
                    <li class="text-red-600 text-sm" x-text="error"></li>
                </template>
            </ul>
        </div>



        @if($item->state_name == OrderItemStates::RequirementsPending->value || $item->state_name ==
        OrderItemStates::RequirementAwaitingPublisherApproval->value)
        <livewire:form.requirement-form :orderItem="$item"
            :disable_fields="$item->state_name == OrderItemStates::RequirementAwaitingPublisherApproval->value" />

        @elseif($item->state_name == OrderItemStates::RequirementRevisionRequested->value)
        <div class="p-4 mb-6 bg-amber-50 border-l-4 border-amber-400 rounded-r-lg shadow-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <x-icons.etc.alert class="w-5 h-5 text-amber-500 mr-3" />
                    <p class="text-amber-800 font-medium">Your requirements need revision. Please update them below.
                        <br>
                        <span class="text-sm text-gray-500">
                            Reason: {{ $item->requirements->advertiser_revision_reason }}
                        </span>
                    </p>
                </div>
            </div>
        </div>
        <livewire:form.requirement-form :orderItem="$item"
            :disable_fields="$item->state_name == OrderItemStates::RequirementAwaitingPublisherApproval->value" />

        @elseif($item->state_name == OrderItemStates::ContentPending->value || $item->state_name ==
        OrderItemStates::ContentAwaitingPublisherApproval->value)
        @if($item->state_name == OrderItemStates::ContentAwaitingPublisherApproval->value)
        <div class="p-4 flex justify-end">
            <button wire:click="openChat"
                class="inline-flex items-center px-5 py-2.5 bg-amber-500 text-white rounded-lg shadow-sm hover:bg-amber-600 hover:shadow-md transition-all duration-200 font-medium">
                <x-icons.lucide.mail class="w-4 h-4 mr-2" />
                Chat with Publisher
            </button>
        </div>
        @endif
        <livewire:form.content-form :orderItem="$item" :disable_fields="$item->state_name ==
        OrderItemStates::ContentAwaitingPublisherApproval->value" />
        @endif

        @if($item->state_name == OrderItemStates::RequirementAwaitingPublisherApproval->value)
        <div class="p-4 mb-6 bg-amber-50 border-l-4 border-amber-400 rounded-r-lg shadow-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <x-icons.etc.alert class="w-5 h-5 text-amber-500 mr-3" />
                    <p class="text-amber-800 font-medium">Your requirement is awaiting publisher approval. </p>
                </div>
                @if($showResendButton)
                <button wire:click="resendEmail"
                    class="text-amber-800 hover:text-amber-900 font-medium flex items-center">
                    <x-icons.lucide.mail class="w-4 h-4 mr-1" />
                    Resend Reminder
                </button>
                @endif

                <button wire:click="openChat" class="text-amber-800 hover:text-amber-900 font-medium flex items-center">
                    <x-icons.lucide.mail class="w-4 h-4 mr-1" />
                    Chat with Publisher
                </button>
            </div>
        </div>
        @endif

        {{-- Chat Modal --}}
        @if($showChat)
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeChat"></div>
        <div class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <div
                    class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    <div class="absolute right-0 top-0 pr-4 pt-4">
                        <button type="button" wire:click="closeChat"
                            class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none">
                            <span class="sr-only">Close</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <livewire:marketplace.order.order-item-chat :item="$item" />
                </div>
            </div>
        </div>
        @endif

        {{-- Cant update --}}
        @if(!$item->customerCanUpdateRequirements())
        <div class="p-4 mb-6 justify-center text-amber-900/80 flex flex-col md:flex-row items-center w-full">
            <x-icons.etc.alert class="inline w-4 h-4 mb-2 md:mb-0 md:mr-1" />
            You can't make changes to the order once team starts working on it. If you need any assistance, please
            contact support.
        </div>
        @endif

        {{-- Bottom bar --}}
        <div id="action-buttons" class="flex bg-gray-50 border-dashed border-gray-300 border-t py-6 px-8 ">

            {{-- show published post url here too --}}
            @if(empty(!$item->publication->publication_url))
            <div id="published-data" class="flex items-center w-fit bg-gray-100 px-8 py-3 rounded-lg text-gray-900 ">
                <div class="w-4 h-4 mr-1">
                    <x-icons.lucide.newspaper />
                </div>
                <div id="published-label" class="font-bold mr-2 text-gray-700">
                    Published URL:
                </div>
                <div id="published-data" class="text-blue-800">
                    <a target="_blank" class="flex items-center" href="{{ $item->publication->publication_url }}">
                        <div id="published-url" class="italic underline">
                            {{ $item->publication->publication_url }}
                        </div>
                        <div class="w-4 h-4 ml-2 text-blue-800">
                            <x-icons.lucide.external-link />
                        </div>
                    </a>
                </div>
                <div id="publication meta">{{ $item->delivery_date }}</div>
            </div>
            @endif

        </div>

    </div>


</div>