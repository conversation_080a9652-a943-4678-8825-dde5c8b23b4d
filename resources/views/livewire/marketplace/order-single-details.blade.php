<div id="app-body" class="app-body flex">


    <div id="main-section" class="flex flex-col w-full overflow-hidden">
        <div id="body-wrapper" class="my-4 md:m-10">


            {{-- ************************* --}}
            {{-- Top Order Information Box --}}
            <div id="order-details" class="box-section-wrapper">


                {{-- Intro --}}
                <div id="intro-section" class="mb-10">
                    <div id="page-title" class="text-2xl font-bold text-slate-800 tracking-tight">
                        Order Information
                    </div>
                    <div id="page-info" class="text-sm text-slate-600 pt-1.5">
                        Please find all the information regarding your order here.
                    </div>
                </div>


                {{-- Data Info --}}
                <ul class="order-meta space-y-3 columns-1 md:columns-2 lg:columns-3 text-sm">
                    <li>
                        <span class="o-label font-bold text-slate-600">Order id:</span>
                        <span class="o-data text-teal-700 font-medium">#{{ $order->id }}</span>
                    </li>

                    <li>
                        <span class="o-label font-bold text-slate-600">Date Ordered:</span>
                        <span class="o-data text-teal-700 font-medium">{{ date_format($order->created_at, 'd M Y')
                            }}</span>
                    </li>

                    <li>
                        <span class="o-label font-bold text-slate-600">Est Delievery:</span>
                        <span class="o-data text-teal-700 font-medium">
                            {{ $order->created_at->addDays(7)->format('d M Y') }}
                        </span>

                    </li>

                    <li>
                        <span class="o-label font-bold text-slate-600">Amount Paid:</span>
                        <span class="o-data text-teal-700 font-medium">{{ Number::currency($order->price_paid) }}</span>
                    </li>

                    <li>
                        <span class="o-label font-bold text-slate-600">Payment Id:</span>
                        <span class="o-data text-teal-700 font-medium">
                            <a href="#billing-summary">#{{ $order->payment_id }}</a>
                        </span>
                    </li>

                    <li>
                        <span class="o-label font-bold text-slate-600">Support Email:</span>
                        <span class="o-data text-teal-700 font-medium"><EMAIL></span>
                    </li>

                    <li>
                        <span class="o-label font-bold text-slate-600">Order Items Delivered:</span>
                        <span class="o-data text-teal-700 font-medium">
                            {{ $order->orderItems()->isDelivered()->count() }}
                            of
                            {{ count($order->orderItems) }}
                        </span>
                    </li>

                    <li class="flex items-center">
                        <span class="o-label font-bold text-slate-600 pr-1">Invoice: </span>
                        <a target="_blank" href="{{ route('advertiser.show-invoice') }}?id={{ $order->id }}"
                            class="hidden text-sm font-medium text-teal-700 hover:text-indigo-500 sm:block">
                            View invoice
                            <span aria-hidden="true"> &rarr;</span>
                        </a>
                    </li>

                    <li class="flex">
                        <span class="o-label font-bold text-slate-600 mr-1">Order Status:</span>

                        @php
                        $statusClass = match ($order->status) {
                        'delivered' => 'bg-green-100 text-green-800',
                        'inprogress' => 'bg-yellow-100 text-yellow-800',
                        'pending' => 'bg-yellow-100 text-gray-800',
                        'cancelled' => 'bg-red-100 text-red-800',
                        'completed' => 'bg-green-100 text-green-800',
                        default => 'bg-gray-100 text-gray-800',
                        };
                        @endphp

                        <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $statusClass }}">
                            {{ ucfirst($order->status) }}
                        </span>

                    </li>
                </ul>
                @if ($order_memo)
                <div class="mt-4">
                    <div class="pt-4 pb-2 font-bold border-t border-zinc-200">
                        Advertiser Note
                    </div>
                    <div>
                        {{ $order_memo }}
                    </div>
                </div>
                @endif

            </div>



            @if ($order->orderItems->contains(fn($item) => get_class($item->state) ===
            \App\States\OrderItem\RequirementsPending::class))
            <x-marketplace.order.pending-requirements-message />
            @endif



            <!-- ORDERS TABLE -->
            <div id="order-items-table-user" style="overflow-x:auto;"
                class="bg-white border rounded-lg shadow sm:my-12">
                <table
                    class="min-w-full divide-y whitespace-nowrap divide-gray-200 dark:divide-gray-700 border-b bg-white ">

                    <thead
                        class="bg-gray-200/90 text-gray-900 border-b dark:bg-zinc-900 dark:text-zinc-100 tracking-wide uppercase text-xs font-semibold overflow-scroll">
                        <tr>
                            <th scope="col" class="px-4 py-3">
                                Details
                            </th>

                            <th scope="col" class="px-4 py-3">
                                <div class="flex items-center gap-x-2">
                                    <span title="Website where your content will be published.">
                                        Website
                                    </span>
                                </div>
                            </th>

                            <th scope="col" class="px-4 py-3">
                                <div class="flex items-center gap-x-2">
                                    Order Status
                                    <div class="tooltip opacity-30 hover:opacity-100 pt-1">
                                        <x-ui.tooltip text="?" tooltip="URL where you post is published." />
                                    </div>
                                </div>
                            </th>

                            <th scope="col" class="px-4 py-3">
                                <div class="flex items-center gap-x-2">
                                    Published URL
                                    <div class="tooltip opacity-30 hover:opacity-100 pt-1">
                                        <x-ui.tooltip text="?" tooltip="URL where you post is published." />
                                    </div>
                                </div>
                            </th>

                            <th scope="col" class="px-4 py-3">
                                <div title="last time this item was updated" class="flex items-center gap-x-2">
                                    Last Updated
                                </div>
                            </th>
                        </tr>
                    </thead>


                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700 text-gray-600 font-medium">

                        @foreach ($order->orderItems as $item)
                        <tr>
                            <td class="whitespace-nowrap px-4 py-2">
                                <div class="p-4">
                                    <a href="{{ route('advertiser.order-item-details', $item->id) }}" class="button-action block mx-auto py-2 text-xs px-4 bg-emerald-600
                               hover:bg-emerald-700 text-white w-fit rounded-lg cursor-pointer">
                                        Details
                                    </a>
                                </div>
                            </td>

                            <td class="whitespace-nowrap px-4 py-2">
                                <div x-data @click="$dispatch('toggle', '{{ $item->website->website_domain }}')"
                                    class="p-4 cursor-pointer">

                                    <span class="text-sm font-bold text-emerald-900 hover:underline 
                                flex items-center mr-2">
                                        <img width="16px" height="16px"
                                            src="https://www.google.com/s2/favicons?sz=64&domain_url={{ $item->website->website_domain }}"
                                            class="w-4 h-4 border-1 rounded-full mr-1.5">
                                        <span class="pr-1 tracking-wide">
                                            {{ $item->website->website_domain }}
                                        </span>
                                        <div class="mt-0.5">
                                            <x-icons.etc.down-icon />
                                        </div>
                                    </span>
                                </div>
                            </td>

                            <td class="whitespace-nowrap px-4 py-2">
                                {{-- State: Badge --}}
                                <div x-cloak>
                                    <x-ui.badges.order.statusBadge :item="$item" />
                                </div>
                            </td>

                            <td class="whitespace-nowrap px-4 py-2">
                                @if($item->publication?->publication_url)
                                <a href="https://{{$item->publication->publication_url}}" class="p-4 flex items-center">
                                    <span class="text-sm mr-1">{{$item->publication->publication_url}}</span>
                                    <span class="w-4 text-green-600">
                                        <x-icons.lucide.external-link />
                                    </span>
                                </a>
                                @else
                                <span class="text-xs text-gray-300">Awaiting Publication.</span>
                                @endif
                            </td>


                            <td class="whitespace-nowrap px-4 py-2">
                                <div class="p-4">
                                    <span class="text-sm text-gray-600">
                                        {{ date_format($item->updated_at, 'd M Y') }}
                                    </span>
                                </div>
                            </td>

                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <!-- End Table -->


        </div>



    </div>

</div>