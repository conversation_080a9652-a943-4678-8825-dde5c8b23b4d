// @tailwind base;
// @tailwind components;
// @tailwind utilities;

@use './design-system.scss';



.title-1 {
  @apply text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight;
}

.subtitle-1 {
  @apply mt-2 text-sm text-gray-700;
}

.link-indigo {
  @apply text-indigo-600 hover:text-indigo-500 transition-colors;
}

// BUTTONS 
.btn-indigo {
  @apply w-fit flex flex-row items-center justify-center gap-2 rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors;
}

.btn-red {
  @apply w-fit flex flex-row items-center justify-center gap-2 rounded-md bg-red-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600 transition-colors;
}

.btn-green {
  @apply w-fit flex flex-row items-center justify-center gap-2 rounded-md bg-green-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600 transition-colors;
}

.btn-gray {
  @apply w-fit flex flex-row items-center justify-center gap-2 rounded-md bg-gray-500 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-gray-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 transition-colors;
}

.btn-gray-selected {
  @apply w-fit flex flex-row items-center justify-center gap-2 rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 transition-colors;
}


.link-indigo-icon {
  @apply text-indigo-600 hover:text-indigo-500 transition-colors;
}

.badge-custom {
  @apply inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset;

}

.badge-basic {
  @apply badge-custom bg-white text-gray-800 ring-gray-600/20;
}


.badge-gray {
  @apply badge-custom bg-gray-50 text-gray-800 ring-gray-400/20;
}

.badge-red {
  @apply badge-custom bg-red-50/10 text-red-800 ring-red-400/20;
}

.badge-yellow {
  @apply badge-custom bg-yellow-50/10 text-yellow-800 ring-yellow-400/20;
}

.badge-red {
  @apply badge-custom bg-red-50/10 text-red-800 ring-red-400/20;
}

.badge-green {
  @apply badge-custom bg-green-50/10 text-green-800 ring-green-400/20;
}

.badge-blue {
  @apply badge-custom bg-purple-50/10 text-purple-800 ring-purple-400/20;
}

.badge-purple {
  @apply badge-custom bg-purple-50/10 text-purple-800 ring-purple-400/20;
}

.badge-indigo {
  @apply badge-custom bg-indigo-50/10 text-indigo-800 ring-indigo-400/20;
}

.badge-pink {
  @apply badge-custom bg-pink-50/10 text-pink-800 ring-pink-400/20;
}


.badge-pending {
  @apply badge-blue;
}

.badge-inprogress {
  @apply badge-yellow;
}

.badge-onboarded {
  @apply badge-green;
}

.badge-rejected {
  @apply badge-gray;
}

.badge-delivered{
  @apply badge-indigo;
}

.badge-late{
  @apply badge-pink;
}

.badge-completed{
  @apply badge-green;
}

.badge-cancelled{
  @apply badge-red;
}

.badge-refunded{
  @apply badge-gray;
}



.stats-block {
  @apply flex flex-col gap-0 justify-start items-start overflow-hidden rounded-lg px-4 py-5 shadow-md border sm:p-6 ring-1 ring-inset capitalize text-xs font-medium;

  .stats-block-number {
    @apply text-3xl font-bold;
  }

  .stats-block-label {
    @apply text-sm;
  }
}

.stats-admin-block {
  @apply flex flex-col gap-0 justify-start items-start px-4 py-5 sm:px-6 sm:py-10 rounded-none;

  .stats-block-number {
    @apply text-3xl font-bold;
  }

  .stats-block-label {
    @apply text-sm;
  }
}

// @keyframes shake {

//   0%,
//   100% {
//     transform: rotate(0deg);
//   }

//   25% {
//     transform: rotate(-15deg);
//   }

//   75% {
//     transform: rotate(15deg);
//   }
// }

@keyframes shake {
  0%, 100% { transform: rotate(0deg); }
  20%, 60% { transform: rotate(-15deg); }
  40%, 80% { transform: rotate(15deg); }
}

.animate-shake-twice {
  animation: shake 1s ease-in-out 2; /* 2 iterations */
}

.animate-shake {
  animation: shake 1s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
  transform-origin: top center;
}

.status-pending {
  @apply bg-blue-100 text-blue-600 border-blue-300 ring-1 ring-inset ring-blue-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-inprogress {
  @apply bg-yellow-100 text-yellow-600 border-yellow-300 ring-1 ring-inset ring-yellow-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-delivered {
  @apply bg-purple-100 text-purple-600 border-purple-300 ring-1 ring-inset ring-purple-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-late {
  @apply bg-pink-100 text-pink-600 border-pink-300 ring-1 ring-inset ring-pink-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-cancelled {
  @apply bg-red-100 text-red-600 border-red-300 ring-1 ring-inset ring-red-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-refunded {
  @apply bg-gray-100 text-gray-600 border-gray-300 ring-1 ring-inset ring-gray-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-completed {
  @apply bg-green-100 text-green-600 border-green-300 ring-1 ring-inset ring-green-600/20 border capitalize inline-flex items-center rounded-md px-2 py-1 text-xs font-medium gap-2;
}

.status-large {
  @apply text-lg font-bold flex flex-row px-4 py-2 mr-4;
}


.select-option {
  @apply bg-red-500;
}

.fade-enter-active,
.fade-leave-active {
  @apply transition-all duration-300 ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0 max-h-0;
}

.fade-enter-to,
.fade-leave-from {
  @apply opacity-100 max-h-[500px];
}


.paper-shadow {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 20px 10px -20px;
}