@tailwind base;
@tailwind components;
@tailwind utilities;

/*Alpine Cloak*/
[x-cloak] { display: none !important; }


@layer base {
    [type='text']:focus,
    input:where(:not([type])):focus,
    [type='email']:focus,
    [type='url']:focus,
    [type='password']:focus,
    [type='number']:focus,
    [type='date']:focus,
    [type='datetime-local']:focus,
    [type='month']:focus,
    [type='search']:focus,
    [type='tel']:focus,
    [type='time']:focus,
    [type='radio']:focus,
    [type='week']:focus,
    [multiple]:focus,
    textarea:focus,
    select:focus {
        --tw-ring-color: #059669;
        border-color: #d6d3d1;
    }
  :root { --background: 0 0% 100%; --foreground: 0 0% 3.9%; --card: 0 0% 100%; --card-foreground: 0 0% 3.9%; --popover: 0 0% 100%; --popover-foreground: 0 0% 3.9%; --primary: 0 0% 9%; --primary-foreground: 0 0% 98%; --secondary: 0 0% 96.1%; --secondary-foreground: 0 0% 9%; --muted: 0 0% 96.1%; --muted-foreground: 0 0% 45.1%; --accent: 0 0% 96.1%; --accent-foreground: 0 0% 9%; --destructive: 0 84.2% 60.2%; --destructive-foreground: 0 0% 98%; --border: 0 0% 89.8%; --input: 0 0% 89.8%; --ring: 0 0% 3.9%; --chart-1: 12 76% 61%; --chart-2: 173 58% 39%; --chart-3: 197 37% 24%; --chart-4: 43 74% 66%; --chart-5: 27 87% 67%; --radius: 0.5rem; }
  .dark { --background: 0 0% 3.9%; --foreground: 0 0% 98%; --card: 0 0% 3.9%; --card-foreground: 0 0% 98%; --popover: 0 0% 3.9%; --popover-foreground: 0 0% 98%; --primary: 0 0% 98%; --primary-foreground: 0 0% 9%; --secondary: 0 0% 14.9%; --secondary-foreground: 0 0% 98%; --muted: 0 0% 14.9%; --muted-foreground: 0 0% 63.9%; --accent: 0 0% 14.9%; --accent-foreground: 0 0% 98%; --destructive: 0 62.8% 30.6%; --destructive-foreground: 0 0% 98%; --border: 0 0% 14.9%; --input: 0 0% 14.9%; --ring: 0 0% 83.1%; --chart-1: 220 70% 50%; --chart-2: 160 60% 45%; --chart-3: 30 80% 55%; --chart-4: 280 65% 60%; --chart-5: 340 75% 55%; }
}

/*Root Stylign From Shadcn*/
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
 
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
 
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
 
    --ring: 161, 94%, 30%;
 
    --radius: 0.5rem;


    --orange-primary: #ff622d;
    --orange-link: #dd3500;
    --orange-link-bg: #ffe5db;
    --green-primary: #009f81;
/*    --red-primary: rgb(209, 0, 47);*/


/*  --orange-50: #fff3d9;
    --orange-100: #ffdca2;
    --orange-200: #ffb26e;
    --orange-300: #ff8c43;
    --orange-400: #ff642d;
    --orange-500: #c33909;
    --orange-600: #8b1500;
    --orange-700: #551200;
    --orange-800: #351000;*/

  }
 
  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
 
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
 
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
 
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
 
    --border: 216 34% 17%;
    --input: 216 34% 17%;
 
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
 
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
 
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
 
    --ring: 216 34% 17%;
 
    --radius: 0.5rem;
  }

  .text-accent{
    color: hsl(var(--accent));
  }

  .text-accent-foreground{
    color: hsl(var(--accent-foreground));
  }

  .bg-background{
    background: hsl(var(--accent));
  }

  .bg-accent{
    background: hsl(var(--accent));
  }

  .bg-muted{
    background: hsl(var(--muted));
  }

  .bg-primary{
    background: hsl(var(--primary));
  }


  .border-border{
    border-color: hsl(var(--border));
  }

  .border-primary{
    border-color: hsl(var(--border));
  }


  .text-primary{
    color: hsl(var(--primary));
  }

  .text-foreground{
    color: hsl(var(--foreground));
  }

  .text-muted-primary{
    color: hsl(var(--muted));
  }

  .text-muted-foreground{
    color: hsl(var(--muted-foreground));
  }

  .border-input{
    border-color: hsl(var(--input));
  }

 

  .ring-ring{
    box-shadow: var(--ring);
  }

  .ring-primary{

  }

  .text-primary-foreground{
    color: hsl(var(--primary-foreground));
  }

  .orange-primary{
    color:var(--orange-primary);
  }

  .orange-link{
    color: var(--orange-link);
  }

  .orange-link-bg{
    background: var(--orange-link-bg);
  }


}


@layer base {
  * {
    @apply  border-border;
  }
  body {
    @apply text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}


/* Custom classes */
.max-w-8xl {
    max-width: 85rem;
}
@layer base {
  * {
    @apply border-border; }
  body {
    @apply bg-background text-foreground; } }
