import { defineConfig } from 'vite';
import laravel, { refreshPaths } from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        laravel({
            input: [

                // App
                // 'resources/css/design-system.css',
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/js/bootstrap.js',
                'resources/js/checkout.js',
                'resources/js/marketplace.js',

                // Public
                'resources/css/public.css',
                'resources/js/public.js',

                // admin
                'resources/js/admin.js',
                'resources/sass/app.scss',


            ],
            refresh: [
                ...refreshPaths,
                'app/Http/Livewire/**',
            ],
        }),
        vue(), // Add Vue support
    ],
});
