#Additional config in pressbear config file.


APP_NAME=Pressbear
APP_ENV=local
APP_KEY=base64:5tyQ89krscVOSgZeZ/m1XFG51jnhpmrt3pYzGV1a/RQ=
APP_DEBUG=true


# DOMAIN
APP_URL=https://pressbear.test
APP_DOMAIN=${APP_URL}
SESSION_DOMAIN=*.${APP_URL}


# APP LOG
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug


# DB CONNECTION
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=pressbear_dev
DB_USERNAME=root
DB_PASSWORD=

# Old Live DB
DB_CONNECTION_OLD=mysql
DB_HOST_OLD=127.0.0.1
DB_OLD_PORT=3306
DB_DATABASE_OLD="pressbear live"
DB_USERNAME_OLD=root
DB_PASSWORD_OLD=


# DRIVERS
BROADCAST_DRIVER=pusher
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=database
SESSION_LIFETIME=120


# CACHING
MEMCACHED_HOST=127.0.0.1
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379


# EMAIL
MAIL_DRIVER=smtp
MAIL_HOST=0.0.0.0
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"


# Payment Method
DEFAULT_PAYMENT_METHOD='stripe'

# STRIPE KEYS
STRIPE_CLIENT="sk_test_51RP0guQ1hFv0wmi3UckFXEZDfhaQtYXuF4WJpKqpOqAuKlbvFtSAX13auKUinH064gvklHWbShPAfWOc95uH98yY00zefQi9ML"
STRIPE_PUBLISHABLE_KEY="pk_test_51RP0guQ1hFv0wmi3HWHUax7wpmqaDV2f2kD63XwEUWFJPC7ls3SWarNO86W58YcduDI02aOJf5d33dGiOh6ym7BA000FVljfis"
STRIPE_WEBHOOK_SECRET="whsec_3d1871f78e666b3f7d5a0304b258089342087281b8b70281038834c0dd8d8cda"


# REAL TIME COMMUNICATION
# Pusher
PUSHER_APP_ID=*******
PUSHER_APP_KEY=034769e1e4680f91a8f1
PUSHER_APP_SECRET=ec5655764bfae574f855
PUSHER_APP_CLUSTER=us2
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Reverb
REVERB_APP_ID=536362
REVERB_APP_KEY=b9iqywa6pemlttnon0bb
REVERB_APP_SECRET=tt22vgo6jzbrxxrv7w17
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME=http
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"


# STORAGE
CLOUDFLARE_R2_ACCESS_KEY_ID="85601a3b26e63b76c6480a88fd4c969a"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="afb47cf485282bc0595212533a26090cab0f5ca98d32d2e6f95ec28fa8e5f9b0"
CLOUDFLARE_R2_BUCKET=pressbear
CLOUDFLARE_R2_ENDPOINT=https://eb4c56748804c5530e89e01307691200.r2.cloudflarestorage.com/pressbear
CLOUDFLARE_R2_URL=https://eb4c56748804c5530e89e01307691200.r2.cloudflarestorage.com/pressbear

B2_ACCESS_KEY_ID="005eef84814d7aa0000000002"
B2_SECRET_ACCESS_KEY="K0052IgwArMIEb1T38hpq3i6SzcDyNI"
B2_DEFAULT_REGION=us-east-005
B2_BUCKET=PressbearLive
B2_ENDPOINT=https://s3.us-east-005.backblazeb2.com
B2_URL=https://pressbearLive.s3.us-east-005.backblazeb2.com/

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false


# MISC
IGNITION_EDITOR="cursor"
# SENTRY_LARAVEL_DSN=https://<EMAIL>/4509366372073472
# SENTRY_TRACES_SAMPLE_RATE=1.0